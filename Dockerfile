FROM python:3.8-slim

RUN apt-get update && apt-get install -y --no-install-recommends gdal-bin libjpeg62 libjpeg62-turbo-dev zlib1g-dev  \
           libxml2-dev libxslt-dev libjpeg-dev libbz2-dev python-dev gcc swig libpulse-dev antiword  musl-dev \
           libsqlite3-dev libbz2-dev build-essential libffi-dev libssl-dev build-essential libpq-dev libmagic1 poppler-utils \
           && apt-get clean \
           && rm -rf /var/lib/apt/lists/*


WORKDIR /usr/src/dossier-backend

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

COPY . .

RUN pip install --upgrade pip
RUN pip install -r requirements.txt


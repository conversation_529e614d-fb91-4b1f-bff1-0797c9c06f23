import jwt

from datetime import datetime, timedelta

KEYCLOAK_PRIVATE_KEY = b'MIIEogIBAAKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQABAoIBADMl1yi3Ps4BFncNKJyBZRGL5cfE5PfZcB9mzP+Up6cSig19Av/xbzgDyJcgXrcdDd585Yabqcx/sbdlkkCGPR6vD1tDra7ywIxtiAEnEtowpS29Thlei/FxIQtSwzBGZ0p4guYn5xFXq4eEkVFESpMU88p1BXDJ1coIGvWKGiMfmox64XPC9IOOveRNDoAc7zYkc2WugvGRJsnrvTsj6ZI1459K4N4MUISQfYpZ7LRI4fNafbH824fTOgCAA+VPVIdKTaWhHp6WbZvpRvAxxvJU3Ummg7u2ejVt0XmjSmcChQ+vUkwhK87iWcqYVp2WhwFu4clo3xHxK3NVOiheq6ECgYEA3U40YG2/Lr8Kklokz+WIq1t2DrXXLvSFg0umyJvbqUS0v/IqKJ5Vlv0QcY4sH2nPJ9f1VwgTf7QBJniNDcn8ffSo5wUwB8qCFaRJ/pFEvGKhZrUor609vSgSPvlcyNY/kB/CmdDrguZRn9PnnRaaOHpAZZnPM5m6i22BOJ6vFiMCgYEApsN3ZHeC/xSE8Q+/vFyrmfPfhvYtc338fe8tQXzbee3p8/mdZrLgW1KNrpR92KZxhqVSqJebjQo1rt/l+ltXM37gPfgSgTtFs1P1W71e8colQOXcj0gr01S2Xz5KgaJ1MSIzdY1vf8+/1aqrP8KS4fzumF9vcr+GBWnI2DC4yeUCgYAltJNb8pbmQgptEQrAmR0GBRlCPF0jVOoCirsp5tQwLNKW+Y2RShjPFuLcVhrSZ1ayNStJ3shjs6mWgmeV99obI7o7UjjyuuAbC9jHlLyfVDanpyn9dIjWV6N0M0xJs6c0yRSA3IWj0dYKYzJNI3K3OK0MIn6ZDhIoe0nO+cpe/QKBgFq9SR21meXJ+HxioWWQ1x6yABKjqR+KkfTES8+ybInv1rkWPXtAIawPQ2CXE9Lq3iLxmgR6Wf1obMV2tuB/CvfONZ4Cea8v3UEykfMVG7Bc2eByMo0ULCSVl5ZDgq7/At11SqLQDrdxB2TvtLbA3MNyqTDn3PCHHhmb8dkQybuxAoGAcU21ekiBktCuUVYrbKzceJ+ubMYfGEadi+00Sm0F0Jw+rQx4ruqAsnlANTgDOmfN/EJN6uubusjNm+d/BjvviI4GrNDkNjWTQMp8VRm87+v8h1wfosy78CmFDR+d1LCWCJipaiAfYF+Ft3Syqit2C3vmXtImDo+1U9a8HQMrF+k='


def _generate_jwt_token(username):
    """
    Generate a JSON web token in which an identifier of this user is stored
    the token lifetime is 15 minutes since the moment of its creation.
    """

    dt = datetime.now() + timedelta(minutes=15)
    private_key = b"-----BEGIN RSA PRIVATE KEY-----\n" + KEYCLOAK_PRIVATE_KEY + b"\n-----END RSA PRIVATE KEY-----"

    return jwt.encode({
        'aud': 'account',
        'preferred_username': username,
        'exp': int(dt.strftime('%s'))
    }, private_key, algorithm='RS256')

import click
import click_pathlib
import logging
import requests
import semantic_dossier
from pathlib import Path
from uuid import uuid4
from dossier_client.utils import _generate_jwt_token

logger = logging.getLogger(__name__)

color_mapping = {
    "green": "high",
    "orange": "medium",
    "red": "low"
}


@click.command()
@click.argument("dossier_path", type=click_pathlib.Path(file_okay=False, dir_okay=True))
@click.option("--dossier-api-url", type=click.STRING, default="https://dms.hypo.duckdns.org/api")
def main(dossier_path: Path, dossier_api_url: str):
    semantic_document_path = dossier_path / 'semantic-document.json'
    dossier = semantic_dossier.SemanticDossier.parse_file(semantic_document_path)

    token = _generate_jwt_token('<EMAIL>')
    client = HypodossierAPI(dossier_api_url, token=token, type_auth='bearer')
    dossier_uuid = client.create_dossier()

    uploaded_original_file = {}
    # first step is uploading the documents
    for original_file_name, extracted_files in dossier.extracted_files.items():
        original_file_path = (dossier_path / 'original_files' / original_file_name)
        logger.info(f"uploading {original_file_path}")

        with original_file_path.open('rb') as fp:
            response = client.call_api_post(f"{dossier_api_url}/dossier/{dossier_uuid}/original_files",
                                            files={'file': fp})
            response.raise_for_status()
            original_file_uuid = response.json().get('uuid')
            uploaded_original_file = {original_file_name: original_file_uuid}

    # get the extracted file an upload either the extracted file or the exception
    uploaded_extracted_files = {}
    for original_file_name, original_file_uuid in uploaded_original_file.items():
        # get extracted files and upload
        for extracted_file_path in dossier.extracted_files[original_file_name].extracted_files:
            extracted_file_path_local = dossier_path / 'extracted_files' / extracted_file_path
            assert extracted_file_path_local.exists()

            extracted_file_uuid = client.upload_extracted_file(dossier_uuid, original_file_uuid,
                                                               extracted_file_path_local,
                                                               extracted_file_path)

            if extracted_file_path in dossier.extracted_files[original_file_name].exceptions:
                # load file extraction exceptions
                exception = dossier.extracted_files[original_file_name].exceptions[extracted_file_path]
                response = client.call_api_post(f"{dossier_api_url}/dossier/{dossier_uuid}/file-exceptions/", json={
                    'file_uuid': extracted_file_uuid,
                    'type': 'Extracted',
                    'de': exception.de,
                    'en': exception.en,
                    'details': exception.details
                })
                print(response.text)
                response.raise_for_status()
                continue

            # now the processing would occur after that, upload processing errors
            if extracted_file_path in dossier.processing_exceptions:
                exception = dossier.processing_exceptions[extracted_file_path]

                response = client.call_api_post(f"{dossier_api_url}/dossier/{dossier_uuid}/file-exceptions/", json={
                    'file_uuid': extracted_file_uuid,
                    'type': 'Processed',
                    'de': exception.de,
                    'en': exception.en,
                    'details': exception.details
                })
                print(response.text)
                response.raise_for_status()
                continue

            # the processing was successful, upload the result
            processed_file = dossier.processed_files[extracted_file_path]
            data = {}
            data['processed_file'] = processed_file.dict()
            data['processed_file']['extracted_file_uuid'] = extracted_file_uuid

            page_uuids = {}
            for number, page in processed_file.pages.items():
                # upload files
                page_uuid = str(uuid4())
                page_uuids[number] = page_uuid

                page_dict = data['processed_file']['pages'][number]
                page_dict['uuid'] = page_uuid

                page_dict['image_dossier_file_uuid'] = client.upload_dossier_file(dossier_uuid,
                                                                                  dossier_path / page.image)
                page_dict['searchable_pdf_dossier_file_uuid'] = client.upload_dossier_file(dossier_uuid,
                                                                                           dossier_path.parent / page.searchable_pdf)
                page_dict['searchable_txt_dossier_file_uuid'] = client.upload_dossier_file(dossier_uuid,
                                                                                           dossier_path.parent / page.searchable_txt)

                # map page objects to the new format
                for idx, page_object in enumerate(page.page_objects):
                    page_dict['source'] = None
                    page_dict['page_objects'][idx]['confidence_value'] = page_object.confidence_summary.value
                    page_dict['page_objects'][idx][
                        'confidence_formatted'] = page_object.confidence_summary.value_formatted
                    page_dict['page_objects'][idx]['confidence_level'] = color_mapping[
                        page_object.confidence_summary.color]

            data['semantic_documents'] = []
            for semantic_document in dossier.semantic_documents:
                if semantic_document.semantic_pages[0].source_file_path == extracted_file_path:
                    semantic_document_dict = semantic_document.dict()

                    # map confidence structure
                    map_confidence_objects([semantic_document_dict], [semantic_document])
                    map_confidence_objects(semantic_document_dict['aggregated_objects'],
                                           semantic_document.aggregated_objects)
                    map_confidence_objects(semantic_document_dict['semantic_pages'], semantic_document.semantic_pages)

                    for idx, semantic_page in enumerate(semantic_document.semantic_pages):
                        map_confidence_objects(semantic_document_dict['semantic_pages'][idx]['page_objects'],
                                               semantic_page.page_objects)
                        semantic_document_dict['semantic_pages'][idx]['number'] = idx
                        assert processed_file.file_path == semantic_page.source_file_path, f'its currently assumed that the semantic document pages are all based on the current processed page {processed_file.file_path} {semantic_page.source_file_path}'
                        semantic_document_dict['semantic_pages'][idx]['processed_page_uuid'] = page_uuids[
                            semantic_page.source_page_number]

                    data['semantic_documents'].append(semantic_document_dict)

            response = client.call_api_post(f"{dossier_api_url}/dossier/{dossier_uuid}/processing-result/", json=data)

            response.raise_for_status()


def map_confidence_objects(confidence_dict, confidence_objects):
    for idx, confidence_object in enumerate(confidence_objects):
        relevant_object = confidence_dict[idx]
        relevant_object['confidence_value'] = confidence_object.confidence_summary.value
        relevant_object['confidence_formatted'] = confidence_object.confidence_summary.value_formatted
        relevant_object['confidence_level'] = color_mapping[
            confidence_object.confidence_summary.color]


class HypodossierAPI:
    def __init__(self, dossier_api_url, credentials=None, token=None, type_auth='basic'):
        """type auth basic | bearer"""
        self.dossier_api_url = dossier_api_url
        self.credentials = credentials
        self.token = token
        self.type_auth = type_auth

    def call_api_post(self, url: str, **kwargs):
        if self.type_auth.lower() == 'basic':
            return requests.post(url, **kwargs, auth=self.credentials)
        elif self.type_auth.lower() == 'bearer':
            headers = {"Authorization": f"Bearer {self.token}"}
            return requests.post(url, **kwargs, headers=headers)
        else:
            raise Exception('Неизвестный тип авторизации')

    def call_api_get(self):
        return

    def create_dossier(self, title=None) -> str:
        response = self.call_api_post(f"{self.dossier_api_url}/dossier/", json={"title": title, "lang": 'DE'})
        response.raise_for_status()
        dossier_uuid = response.json().get('uuid')
        return dossier_uuid

    def upload_dossier_file(self, dossier_uuid: str, file_path: Path) -> str:
        assert file_path.exists(), file_path

        with file_path.open('rb') as fp:
            response = self.call_api_post(f"{self.dossier_api_url}/dossier/{dossier_uuid}/files", files={'file': fp})
            response.raise_for_status()
            return response.json().get('uuid')

    def upload_extracted_file(self, dossier_uuid: str, original_file_uuid: str, extracted_file_path: Path,
                              path_from_original: str):
        dossier_file_uuid = self.upload_dossier_file(dossier_uuid, extracted_file_path)
        response = self.call_api_post(f"{self.dossier_api_url}/dossier/{dossier_uuid}/extracted_files", json={
            'original_file_uuid': original_file_uuid,
            'dossier_file_uuid': dossier_file_uuid,
            'path_from_original': path_from_original
        })

        return response.json().get('uuid')


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    main()

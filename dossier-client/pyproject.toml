[tool.poetry]
name = "dossier-client"
version = "0.1.0"
description = ""
authors = ["<PERSON> <andrea<PERSON>@wapf.ch>"]

[tool.poetry.dependencies]
python = "^3.8"
click = "^7.1.2"
semantic-dossier = "^0.2.0"
click-pathlib = "^2020.3.13"
requests = "^2.25.1"
python-keycloak = "^0.24.0"
aio-pika = "^6.8.0"
PyJWT = "^2.1.0"
cryptography = "^43.0.0"


[tool.poetry.dev-dependencies]
pytest = "^5.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"


[[tool.poetry.source]]
name = "document-universe"
url = "https://gitlab.com/api/v4/projects/26418056/packages/pypi/simple"
{
    acme_ca https://acme.zerossl.com/v2/DV90
    email   <EMAIL>
}

(tls) {
    tls {
      dns duckdns {
       api_token {$DUCKDNS_API_TOKEN}
       override_domain {$BASE_DOMAIN}
    }
  }
}

auth.{$BASE_DOMAIN} {
  reverse_proxy http://keycloak:8080
  import tls
}

admin.{$BASE_DOMAIN}  {

  redir / /admin/ permanent

  reverse_proxy /static/* dms:8000
  reverse_proxy /admin/* dms:8000

  import tls
}

service.{$BASE_DOMAIN}  {
  reverse_proxy /api/* dms:8000
  reverse_proxy /* http://dossier-frontend:80

  import tls
}

sample-spa.{$BASE_DOMAIN}  {
  header Access-Control-Allow-Origin "https://auth.localhost/auth/realms/Hypodossier/protocol/openid-connect/token"
  reverse_proxy sample-react:3000
  import tls
}

dossier-app.{$BASE_DOMAIN}  {
  header Access-Control-Allow-Origin "https://auth.localhost/auth/realms/Hypodossier/protocol/openid-connect/token"
  reverse_proxy http://dossier-frontend:80

  import tls
}

dms.{$BASE_DOMAIN}  {
  reverse_proxy http://dms:8000

  import tls
}

minio.{$BASE_DOMAIN}  {
  reverse_proxy http://minio:9000
  import tls
}

rabbitmq.{$BASE_DOMAIN} {
  reverse_proxy http://rabbitmq:15672
  import tls
}

whoami.{$BASE_DOMAIN} {
  reverse_proxy http://whoami:80
  import tls

}

localhost:80 {
    respond /health "OK" 200
}
image: docker:20.10

variables:
  # When you use the dind service, you must instruct <PERSON><PERSON> to talk with
  # the daemon started inside of the service. The daemon is available
  # with a network connection instead of the default
  # /var/run/docker.sock socket. Docker 19.03 does this automatically
  # by setting the DOCKER_HOST in
  # https://github.com/docker-library/docker/blob/d45051476babc297257df490d22cbd806f1b11e4/19.03/docker-entrypoint.sh#L23-L29
  #
  # The 'docker' hostname is the alias of the service container as described at
  # https://docs.gitlab.com/ee/ci/docker/using_docker_images.html#accessing-the-services.
  #
  # Specify to Dock<PERSON> where to create the certificates. Dock<PERSON>
  # creates them automatically on boot, and creates
  # `/certs/client` to share between the service and job
  # container, thanks to volume mount from config.toml
  DOCKER_TLS_CERTDIR: "/certs"
  # When using dind, it's wise to use the overlayfs driver for
  # improved performance.
  DOCKER_DRIVER: overlay
  # because we are in a subfolder
  IMAGE: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server

services:
  - docker:20.10.16-dind

stages:
  - build
  - test
  - staging
  - stop_staging

build:
  stage: build
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  variables:
    TAG: $CI_COMMIT_SHA



    #GIT_SUBMODULE_STRATEGY: recursive
  script:
    - echo $BASE_DOMAIN
    # prepares the build (auth.toml file for access to the gitlab pypi repository)
    - cp dossier-backend/auth-example.toml dossier-backend/auth.toml
    - sed -i "s/replace_username/gitlab-ci-token/g" dossier-backend/auth.toml
    - sed -i "s/replace_password/${CI_JOB_TOKEN}/g" dossier-backend/auth.toml
    # fetches the latest image (not failing if image is not found)
    - docker pull $IMAGE:latest || true
    - >
      DOCKER_BUILDKIT=1
      docker build
      --pull
      --cache-from $IMAGE:latest
      --tag $IMAGE:$CI_COMMIT_SHA
      --secret id=auth_toml,src=dossier-backend/auth.toml
      --build-arg BUILDKIT_INLINE_CACHE=1
      dossier-backend
    - |
      if [[ "$CI_COMMIT_TAG" ]]; then
        docker image tag $IMAGE:$CI_COMMIT_SHA $IMAGE:$CI_COMMIT_TAG
      fi
    - docker image tag $IMAGE:$CI_COMMIT_SHA $IMAGE:latest
    - docker image tag $IMAGE:$CI_COMMIT_SHA $IMAGE:$CI_COMMIT_REF_SLUG
    - docker image ls
    - docker push --all-tags $IMAGE

linting-pylama:
  stage: build
  image: python:3.11-slim
  tags: [ saas-linux-medium-amd64 ]
  before_script:
    - python --version ; pip --version  # For debugging
    - cd dossier-backend
    - pip install poetry
    - poetry config virtualenvs.create true --local
    - poetry config virtualenvs.in-project false --local
    - poetry config http-basic.hypodossier gitlab-ci-token ${CI_JOB_TOKEN}
    - poetry install --no-interaction --no-ansi --no-root

  script:
    - poetry run pylama

test-parallel:
  stage: test
  image: docker:20.10.16-dind
  tags: [ saas-linux-medium-amd64 ]
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - |
      if [[ "$CI_COMMIT_TAG" ]]; then
        export TAG="$CI_COMMIT_TAG"
      else
        export TAG="$CI_COMMIT_SHA"
      fi

    - docker pull "$IMAGE:$TAG"
    - cd dossier-backend
    - docker-compose -f docker-compose.test.yml run  dms-test python manage.py makemigrations --check
    - docker-compose -f docker-compose.test.yml run  dms-test black --version
    - docker-compose -f docker-compose.test.yml run  dms-test black . --check
    - docker-compose -f docker-compose.test.yml run  dms-test python manage.py s3_assets load dms-default-bucket
    - docker-compose -f docker-compose.test.yml run  dms-test pytest -n auto --cov --junitxml /output/report.xml --cov-report term --cov-report xml:/output/coverage.xml
  coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  artifacts:
    when: always
    reports:
      junit: dossier-backend/output/report.xml
      coverage_report:
        coverage_format: cobertura
        path: dossier-backend/output/coverage.xml

security-audit:
  stage: test
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - |
      if [[ "$CI_COMMIT_TAG" ]]; then
        export TAG="$CI_COMMIT_TAG"
      else
        export TAG="$CI_COMMIT_SHA"
      fi

    - docker pull "$IMAGE:$TAG"
    - cd dossier-backend
    - docker run "$IMAGE:$TAG" python security-audit.py

build-sonar:
  image: 
    name: sonarsource/sonar-scanner-cli:11
    entrypoint: [""]
  stage: test
  timeout: 30m
  tags: [ e2e-test ]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0" 
  cache:
    policy: pull-push
    key: "sonar-cache-$CI_COMMIT_REF_SLUG"
    paths:
      - "${SONAR_USER_HOME}/cache"
      - sonar-scanner/
  script: 
    - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}"
  allow_failure: true
  only:
    - branches

staging:
  stage: staging
  environment:
    name: staging
  tags:
    - swarm-staging-dp1
  script:
    - echo "deploying to $BASE_DOMAIN"
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - cat $SWARM_PRIVATE_KEY | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
    - |
      if [[ "$CI_COMMIT_TAG" ]]; then
        export TAG="$CI_COMMIT_TAG"
      else
        export TAG="$CI_COMMIT_SHA"
      fi

    ## deploy
    - docker context create swarm --docker "host=ssh://$SWARM_USER@$DEPLOY_HOST"
    - docker --context=swarm login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
    - docker --context=swarm stack rm staging-backend
    - TAG=$TAG BASE_DOMAIN=$BASE_DOMAIN docker --context=swarm stack deploy --prune -c dossier-backend/docker-compose.staging.yml staging_backend --with-registry-auth

  only:
    - tags
    - staging

stop_staging:
  image: docker:latest
  stage: stop_staging
  tags:
    - swarm-staging-dp1
  script:
    - 'command -v ssh-agent >/dev/null || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - cat $SWARM_PRIVATE_KEY | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
    - docker context create swarm --docker "host=ssh://$SWARM_USER@$DEPLOY_HOST"
    - docker --context swarm stack rm staging_backend
  when: manual
  environment:
    name: staging
    action: stop

  only:
    - staging
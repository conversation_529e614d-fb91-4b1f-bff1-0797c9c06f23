FROM node:16-alpine as build
WORKDIR /app
ENV PATH /app/node_modules/.bin:$PATH
COPY package.json ./
COPY yarn.lock ./
RUN npm install react-scripts@4.0.3
RUN yarn install

COPY ./public /app/public
COPY ./src /app/src
RUN yarn build

FROM caddy:2-alpine
COPY --from=build /app/build /build
EXPOSE 3000
RUN apk add curl
HEALTHCHECK CMD curl --fail http://localhost:3000 || exit 1
CMD ["caddy", "file-server", "--listen", ":3000", "--root", "/build"]
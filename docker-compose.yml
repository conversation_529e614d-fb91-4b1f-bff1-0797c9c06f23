version: '3.8'
services:
  caddy:
    image: registry.gitlab.com/hypodossier/caddy:v2
    environment:
      - BASE_DOMAIN=hypo.duckdns.org
      - DUCKDNS_API_TOKEN=63daf4d5-9c7e-4c02-9702-5842664a5077
    networks:
      default:
        aliases:
          - auth.hypo.duckdns.org
          - minio.hypo.duckdns.org
          - dms.hypo.duckdns.org
          - service.hypo.duckdns.org
          - dossier-app.hypo.duckdns.org
    ports:
      - 443:443
      - 80:80
    volumes:
      - ./reverse_proxy/caddy_data:/data
      - caddy-config:/config
      - ./reverse_proxy/Caddyfile:/etc/caddy/Caddyfile

    healthcheck:
      test: wget -O /dev/null -q localhost/health
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20


  django-db:
    image: postgres:12
    restart: always
    environment:
      POSTGRES_USER: django
      POSTGRES_PASSWORD: django
    ports:
      - 5432:5432
    volumes:
      - django-db:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -d django -U django" ]
      interval: 5s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:RELEASE.2021-06-07T21-40-51Z
    environment:
      - MINIO_ROOT_USER=S3_ACCESS_KEY
      - MINIO_ROOT_PASSWORD=S3_SECRET_KEY
    #     - MINIO_ACCESS_KEY=S3_ACCESS_KEY
    #     - MINIO_SECRET_KEY=S3_SECRET_KEY
    ports:
      - 127.0.0.1:9000:9000
    entrypoint: sh
    command: -c 'mkdir -p /data/dossier && /usr/bin/minio server /data'
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
      interval: 5s
      timeout: 30s
      retries: 3

  dms:
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-server:${TAG-dev}
    build:
      context:
        dossier-backend
    env_file:
      - ./.env
    secrets:
      - DMS_POSTGRES_PASSWORD
      - DMS_SECRET_KEY
    networks:
      default:
        aliases:
          - dms.hypo.duckdns.org
          - dossier-manager-server
    depends_on:
      django-db:
        condition: service_healthy

    healthcheck:
      test: curl --fail http://localhost:8000/api/docs || exit 1
      start_period: 30s
      interval: 5s
      timeout: 5s
      retries: 20

  #  sample-react:
  #    image: registry.gitlab.com/hypodossier/document-universe/sample-react:${TAG-dev}
  #    build:
  #      context: sample-react
  #    healthcheck:
  #      interval: 5s
  #      timeout: 5s
  #      retries: 5


  dossier-frontend:
    env_file:
      - ./dossier-manager-frontend/.env
    image: registry.gitlab.com/hypodossier/document-universe/dossier-manager-frontend:${TAG-dev}
    build:
      context: dossier-manager-frontend
      args:
        NPM_TOKEN: ${NPM_TOKEN}
    healthcheck:
      test: wget -O /dev/null -q localhost:2019/config
      start_period: 5s
      interval: 5s
      timeout: 5s
      retries: 20

  dossier-zipper:
    image: registry.gitlab.com/hypodossier/dossier-zipper:v2-1-0
    command: poetry run
    environment:
      RABBIT_URL: "amqp://admin:admin@rabbitmq:5672/"
      S3_HOST: "minio:9000"
      S3_ACCESS_KEY: "S3_ACCESS_KEY"
      S3_SECRET_KEY: "S3_SECRET_KEY"
      S3_SECURE: "False"
      ASYNC_DOSSIER_ZIPPER_WORKER_ROUTING_KEY: "DossierZipper.DossierZipRequestV1"
    depends_on:
      rabbitmq:
        condition: service_healthy

  rabbitmq:
    image: rabbitmq:3.8.19-management
    hostname: hyrabbit
    ports:
      - 15672:15672
      - 5672:5672
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin
    healthcheck:
      start_period: 60s
      test: rabbitmq-diagnostics -q check_virtual_hosts --timeout 30
      interval: 30s
      timeout: 30s
      retries: 3

volumes:
  caddy-config:
  caddy-data:
  django-db:
  rabbitmq-data:
  rabbitmq-logs:

secrets:
  DMS_POSTGRES_PASSWORD:
    file: secrets/DMS_POSTGRES_PASSWORD.txt
  DMS_SECRET_KEY:
    file: secrets/DMS_SECRET_KEY.txt

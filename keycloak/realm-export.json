{"id": "dev-hypodossier", "realm": "dev-hypodossier", "displayName": "Hypodossier AG - DEV", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 600, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": true, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "0da8ab8d-d4b6-4033-9975-72d32fa3b3fe", "name": "Test role", "description": "Role only for testing", "composite": false, "clientRole": false, "containerId": "dev-hypodossier", "attributes": {}}, {"id": "b1c0655f-a424-4ad8-904a-b9cb6bb11908", "name": "default-roles-dev-hypodossier", "description": "${role_default-roles-dev-hypodossier}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "dev-hypodossier", "attributes": {}}, {"id": "da59be73-236a-46b9-9d5e-de3ca710f5ee", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "dev-hypodossier", "attributes": {}}, {"id": "9f869604-3bb5-4be4-b3aa-06ba53098ab7", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "dev-hypodossier", "attributes": {}}], "client": {"realm-management": [{"id": "320b62a5-9673-4477-a857-243241052b65", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "d1c555e6-ffd4-44fc-8458-ff8e4c785fc4", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "fac13f1e-220c-46ea-b581-b25e18a36912", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "c85b13c3-dfc2-4a1a-a655-cd68297a9002", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "7673eeae-3d15-4ecb-a1f4-ba172ff2d407", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "ae066e21-6dc6-41c9-9f68-1c1cfcc9701a", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "26e5848d-ef59-4dfa-8d5a-d3d6447741bc", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "548c5d29-b173-4ea6-91e7-04854a5cf0e7", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "95cda054-c796-4b4d-9d18-5e4205407714", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "54699f17-a6cc-4fbc-8887-f06e5aa4c50f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "578f389c-3dbe-4e74-95b7-82e8146193d9", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "cccd856e-d1ca-455c-8e70-d6545faea4dc", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "7cea57b7-0e3f-41c7-9be0-87d301a04c39", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "b72c7749-08f0-4f3e-8b29-e60b0ee5a0a3", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "43d80d0a-8d62-4bf7-adf8-63e56605ffc7", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users", "manage-clients", "manage-identity-providers", "impersonation", "manage-authorization", "view-authorization", "manage-users", "manage-realm", "view-identity-providers", "query-clients", "view-users", "query-realms", "view-events", "view-realm", "manage-events", "create-client", "view-clients"]}}, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "48afae1e-05df-490d-9c01-750fbdc2f8b8", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "d7b9a951-e713-4886-a650-5bf434778ab8", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "1fd9e9c8-e3eb-4ade-a310-cffd77829912", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}, {"id": "1c0552a7-01d5-4e61-88c4-c66e088b263f", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "attributes": {}}], "sample-react": [], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "ee732538-9d3a-4b7e-bac4-dbff526a1121", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "61670d2f-ac68-4196-a52a-9a50caa9b328", "attributes": {}}], "account": [{"id": "8e85ba4a-1934-4774-b3fd-25c8f6f94247", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "50bb7ada-3962-45e4-8b89-7c833db7627a", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "21e34b61-d1f6-47aa-b5b5-36c99cf9d4f2", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "163cdd75-48d8-4773-8456-ae82fd079cca", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "14fec0e1-79b0-4a69-9e19-622e0260c646", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "245626fb-1365-40c1-bd8d-1de81fc5f1de", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}, {"id": "07f963b3-a561-4baf-987c-a189e9c36c57", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "attributes": {}}]}}, "groups": [{"id": "9042ab13-ba02-48db-88e0-3c2c3ef4a94d", "name": "Group XY", "path": "/Group XY", "attributes": {"account_name": ["Manager"]}, "realmRoles": ["Test role"], "clientRoles": {}, "subGroups": []}, {"id": "2e1bb9ef-ff9e-4cde-ad6f-2c62e6323290", "name": "Group Z", "path": "/Group Z", "attributes": {"account_name": ["admin"]}, "realmRoles": [], "clientRoles": {}, "subGroups": []}], "defaultRole": {"id": "b1c0655f-a424-4ad8-904a-b9cb6bb11908", "name": "default-roles-dev-hypodossier", "description": "${role_default-roles-dev-hypodossier}", "composite": true, "clientRole": false, "containerId": "dev-hypodossier"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": [{"name": "builtin-default-policy", "builtin": true, "enable": false}]}, "users": [{"id": "fa59d77a-c4c8-4cc9-9d3f-5ee6413cbd44", "createdTimestamp": 1622469028534, "username": "admin", "enabled": true, "totp": false, "emailVerified": false, "firstName": "admin", "lastName": "admin", "email": "<EMAIL>", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev-hypodossier"], "notBefore": 0, "groups": []}, {"id": "3ba5fbdd-4df6-4e06-ab6b-0241e30f673a", "createdTimestamp": 1622025689219, "username": "<EMAIL>", "enabled": true, "totp": false, "emailVerified": true, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "credentials": [{"id": "4379280c-ae53-4e8e-b62b-7f2f72b8ad53", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"WeLZf14QOWdWCMxnngaDvAjZiPVsrhfTYv4tOf+gY6wTcj9d2ifzaSAudnzYUb71Ywc9nrGcaJ1Otbjt7LXYJA==\",\"salt\":\"AtaHQv3FijIZZArqJDQw0A==\"}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\"}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["offline_access", "uma_authorization"], "clientRoles": {"account": ["view-profile", "manage-account"]}, "notBefore": 0, "groups": ["Group XY"]}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "ac222f78-f1b4-4b93-b72b-85a9b2194620", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dev-hypodossier/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/dev-hypodossier/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "51df2009-eed9-48f4-9394-fc1b6e518af3", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dev-hypodossier/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/dev-hypodossier/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "861b0893-a9b2-43ab-8b34-abfb24c9b1ad", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "5bb37229-9e18-4af7-b0a5-1d5211330ed6", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "61670d2f-ac68-4196-a52a-9a50caa9b328", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b0b3b485-1e0a-42d6-a260-0bf3b4af177c", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "db964df0-7db9-48a7-99d9-650fad633840", "clientId": "sample-react", "rootUrl": "https://service.hypo.duckdns.org", "adminUrl": "https://service.hypo.duckdns.org", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["https://service.hypo.duckdns.org/*"], "webOrigins": ["https://service.hypo.duckdns.org"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "roles", "profile", "email", "Auth_scope"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6877803f-7ad7-4d41-8c2d-3c22139207a8", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/dev-hypodossier/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/admin/dev-hypodossier/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f5e5e26b-790f-4f78-9961-ea3345b2eb3d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "25bb545c-265a-4c0c-bb91-d863fe1f3b6e", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "04ff2a89-fbd1-4376-820f-c1e8844ec772", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "291a9bc1-d31c-4bf4-88e1-a973ceff244c", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "6302203a-0109-4915-8701-d243dfe40a84", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "015fcbc9-7d9d-494d-a9a2-045556a12c4b", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "d61c489e-c517-4ce1-a114-4ee51309f1db", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "23c25224-42dd-4cdb-ba0f-afe39c0cf6d0", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "d6cf8308-4edf-4c5f-a324-d29228583d1f", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "36e66c15-8f00-4cfd-9a40-45e5cf5e62fa", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "888bc740-b40c-4bc3-8d85-bd0d01199064", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "f1934642-7853-4802-9c4e-2f7077e725aa", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "d007d6bc-4d97-45fd-8fc1-17f0d33e2af7", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "7adc1092-85a7-4226-89ac-836ef202fa1c", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "84216894-f2c8-43b6-a4aa-287932039d2f", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "d777b789-ef7b-4048-8c64-d99b02e93978", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "1fd4791b-ee67-43b0-a7da-beed57c8654a", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "06413589-f80b-469f-ad28-18592abf5354", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "cef0032b-7f61-452f-bc2c-da8fdf517606", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "48faf539-9508-4cde-a7e2-478c78240747", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "c244845e-9352-4eb1-987c-91178eb9647b", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f85c2c36-8313-4c97-843c-e201081ca03b", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "1cf383fc-4fc9-4055-b1a8-f23c2816881d", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "ee5ccf33-7b4b-466f-83cc-bf9dbaf9f38a", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "d1e449ae-8440-405a-b26b-e16bd6f42cee", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "c38d6d77-8374-4205-8cb7-cb8616ef3ae7", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "dd8e300e-a218-4675-bdd7-cdc406e7497f", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "a8b0c0bf-ab31-4edb-a2d5-e3a8812e89c3", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "f0cc071e-4853-4a6d-9576-7a5fcff4f56f", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "9e0c796e-5d32-4d02-ba9b-43fc4e1476b3", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "fe9ed035-9578-4e91-86c6-66db472501b8", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}]}, {"id": "ab6d91a3-5b68-4fcf-a636-6dce23937589", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "0088af61-3c38-4497-9837-d85ec6e307e8", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "89ab1062-33c4-48e4-9c26-967d03bb8d85", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "196e83db-ad80-4cc6-be9e-98da09a7dc6e", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "b63f114e-5e7c-4e74-84a5-77f73a9a9f38", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "abffde28-062f-4f75-b865-cae1065ec661", "name": "Auth_scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "d1814049-04e8-4e92-9222-b9158b8ae011", "name": "Role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_roles", "jsonType.label": "String"}}, {"id": "7efcfce8-9b79-43ac-bdb8-571b9f5be580", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-group-membership-mapper", "consentRequired": false, "config": {"full.path": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "userinfo.token.claim": "true"}}, {"id": "a3f19fb0-dfca-440b-aa9f-e6b8d87efcaf", "name": "account_name from group", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "account_name", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "account_name", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "e4900230-c1bc-452f-b27b-4ea22d70c055", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "400234d8-8a2a-48aa-a142-7b5e09dd333a", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "67cce51c-cb25-4da3-bc38-3a235d92b487", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "dd0c1163-4c2a-471d-92ee-a81e66da6641", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "oidc-address-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "7c772e2d-cc33-48e9-9ef5-a46a07514413", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "40b6915c-cdb7-456a-86eb-07ac029d3d11", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "7a83cbf8-bddc-4211-be10-bf254b3db69a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "a5e2e865-092e-4132-b91f-199ecefcba68", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "ebcfada3-0fa4-4914-9ed9-524ad79b456c", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["00732d40-11a8-4573-a3d3-059ab21c54c1"], "secret": ["sZ5qJA86Ii0to9gC32S5Jw"], "priority": ["100"]}}, {"id": "7ef43b3f-fbcd-403a-9424-f2b1a9465985", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEogIBAAKCAQEAkCmvvqDTcjFI+Xphm/cIiC4Ov91C3BcqERNLKDnb7cq5lOLlPBMEBgW7VLKv+ZydQk86CcowRM5Ck8msew0NwBnfnNtp8XjLRqlsdEDerRzWt6vATgAlhXNduG8jfCoCNiqDT27+CC1p/lPcINOx3hWnppv8XVrs4rDUESSX0RRLhgnQazr1qPdq9hohY2dOBP4NN8aLz989YI2+VjTr3SJEpZvJXwINcud5WJspUtuBCyLt1lIOP3Sj+XqcZXvKAo/v1U2A9c5CztadZSa7TL8TZa/TOByxbYCaElxWKs+TWVmnj+TPAoABPcV6IjSvRg57iFuAMpaiPfPD5ChITwIDAQABAoIBADMl1yi3Ps4BFncNKJyBZRGL5cfE5PfZcB9mzP+Up6cSig19Av/xbzgDyJcgXrcdDd585Yabqcx/sbdlkkCGPR6vD1tDra7ywIxtiAEnEtowpS29Thlei/FxIQtSwzBGZ0p4guYn5xFXq4eEkVFESpMU88p1BXDJ1coIGvWKGiMfmox64XPC9IOOveRNDoAc7zYkc2WugvGRJsnrvTsj6ZI1459K4N4MUISQfYpZ7LRI4fNafbH824fTOgCAA+VPVIdKTaWhHp6WbZvpRvAxxvJU3Ummg7u2ejVt0XmjSmcChQ+vUkwhK87iWcqYVp2WhwFu4clo3xHxK3NVOiheq6ECgYEA3U40YG2/Lr8Kklokz+WIq1t2DrXXLvSFg0umyJvbqUS0v/IqKJ5Vlv0QcY4sH2nPJ9f1VwgTf7QBJniNDcn8ffSo5wUwB8qCFaRJ/pFEvGKhZrUor609vSgSPvlcyNY/kB/CmdDrguZRn9PnnRaaOHpAZZnPM5m6i22BOJ6vFiMCgYEApsN3ZHeC/xSE8Q+/vFyrmfPfhvYtc338fe8tQXzbee3p8/mdZrLgW1KNrpR92KZxhqVSqJebjQo1rt/l+ltXM37gPfgSgTtFs1P1W71e8colQOXcj0gr01S2Xz5KgaJ1MSIzdY1vf8+/1aqrP8KS4fzumF9vcr+GBWnI2DC4yeUCgYAltJNb8pbmQgptEQrAmR0GBRlCPF0jVOoCirsp5tQwLNKW+Y2RShjPFuLcVhrSZ1ayNStJ3shjs6mWgmeV99obI7o7UjjyuuAbC9jHlLyfVDanpyn9dIjWV6N0M0xJs6c0yRSA3IWj0dYKYzJNI3K3OK0MIn6ZDhIoe0nO+cpe/QKBgFq9SR21meXJ+HxioWWQ1x6yABKjqR+KkfTES8+ybInv1rkWPXtAIawPQ2CXE9Lq3iLxmgR6Wf1obMV2tuB/CvfONZ4Cea8v3UEykfMVG7Bc2eByMo0ULCSVl5ZDgq7/At11SqLQDrdxB2TvtLbA3MNyqTDn3PCHHhmb8dkQybuxAoGAcU21ekiBktCuUVYrbKzceJ+ubMYfGEadi+00Sm0F0Jw+rQx4ruqAsnlANTgDOmfN/EJN6uubusjNm+d/BjvviI4GrNDkNjWTQMp8VRm87+v8h1wfosy78CmFDR+d1LCWCJipaiAfYF+Ft3Syqit2C3vmXtImDo+1U9a8HQMrF+k="], "certificate": ["MIICrTCCAZUCBgF5qD0C+DANBgkqhkiG9w0BAQsFADAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwHhcNMjEwNTI2MTAzMjIxWhcNMzEwNTI2MTAzNDAxWjAaMRgwFgYDVQQDDA9kZXYtaHlwb2Rvc3NpZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCQKa++oNNyMUj5emGb9wiILg6/3ULcFyoRE0soOdvtyrmU4uU8EwQGBbtUsq/5nJ1CTzoJyjBEzkKTyax7DQ3AGd+c22nxeMtGqWx0QN6tHNa3q8BOACWFc124byN8KgI2KoNPbv4ILWn+U9wg07HeFaemm/xdWuzisNQRJJfRFEuGCdBrOvWo92r2GiFjZ04E/g03xovP3z1gjb5WNOvdIkSlm8lfAg1y53lYmylS24ELIu3WUg4/dKP5epxle8oCj+/VTYD1zkLO1p1lJrtMvxNlr9M4HLFtgJoSXFYqz5NZWaeP5M8CgAE9xXoiNK9GDnuIW4AylqI988PkKEhPAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAI4kPHlNw9vRxXJyiwQjeRRg1IvdokqOVu/fbo70v6R+YsrpeX8PElWFkAHjN3YFX4HE5uaCefjFL+fNIHnmUYLlNVxCEDyOADAmwvu7L4MCZdRrO8ihyZUf5pUMo0WhW+8+uXQX8kyfOaC0W3xcbbJjZ2znQGWdHzWNL17YYV8ywC/+UoGfxpr0EpgIbXEa3+DIAZHvijxrwS1SGIjckCmE62myt7BM3CrfNgOGm9P5rNDYgLT/GhUWQZ1DjyC7BX5/KaPUv2aTSpHZg/PaBed/0S7RzLiAbzEKWH3uO3Vxx/lzHcYyw5Z8v+X13QLrueS76Trwpfd9Suv0JbjTZxU="], "priority": ["100"]}}, {"id": "b4dd052f-4c1b-4f1c-8e67-a0cc715787e6", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["ea52036f-eaff-49df-9624-328905c4908e"], "secret": ["iAEYsFT1MgDJtTZG-jshOTgLS-wF8foYpsE9nVz5jO4PsEVLk9Xq0zS3lXhbKW344rE6BFHS2BdIRcfNzOlmZg"], "priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "b343695a-8d52-43fa-88ab-ade378dd27e3", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "b171904c-c2b0-464b-894c-c5b76656d0fe", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "352d5f4a-20d9-4969-aaf4-ed05a49848ee", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "8b13f4b0-a1bc-4faa-9842-8ab59c956edc", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "749d03f6-c0b0-4ff0-83b6-49b182101d47", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "01ccbe7b-2cd6-4e42-bbba-e19a3e038912", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "29a378e3-4ba7-4b47-98da-158a822e9b19", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "668d3d7d-f609-4fef-b810-00fe19139027", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "a5c482a2-d42c-4077-ae29-a77c78f59221", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0f99ff87-53da-4041-a717-8c6a294f549c", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "8f08b691-f346-4c43-9115-e02372dfc00d", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "699f227c-b97d-4da7-ac3c-2a6fbee4f81e", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f735e90f-5199-4439-96d1-ae22e162d073", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1e42e1b7-ec07-4488-b211-ac4e5f49a735", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "cab07d09-ca08-4282-9072-d1894631243e", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e8307b64-0681-475d-9799-d1eea77d1f41", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "5877be73-472e-4096-a032-0c65018e5fa7", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e7b48cff-8676-464e-9f61-333a3c8c0a78", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "2aa68e8f-6968-4501-8291-d68c7c867f81", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "a7f82abd-5533-4263-8c13-097fa02068d6", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "f57c1dcc-1914-42c5-814b-cac8b7747951", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "7ff0433f-ce4d-4783-8aa5-26af4afd907d", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "600", "clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "13.0.1", "userManagedAccessAllowed": false}
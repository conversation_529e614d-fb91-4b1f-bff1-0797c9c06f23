import structlog

import djclick as click

from core.publisher import publish
from dossier.models import <PERSON>ssierUser

from projectconfig.settings import (
    ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
)
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    export_semantic_document_wrapper_with_access_check,
)
from workers.schemas import SemanticDocumentPDFRequestV1

logger = structlog.get_logger()


@click.command()
@click.option("-semantic_document_uuid", type=click.UUID)
def zkb_export_semantic_document(
    semantic_document_uuid: str,
):
    """
    Example:
    python manage.py zkb_export_semantic_document -semantic_document_uuid 254e93ec-c0f2-4133-be04-24170c60e650

    After this command, you can check in http://localhost:8000/admin/workers/semanticdocumentexport/
    to see if the export is done and a url works to download the pdf
    """

    dossier = SemanticDocument.objects.get(uuid=semantic_document_uuid).dossier

    # dossier_user is account specific as the same owner can be used in different accounts.
    # So we need to ensure to get exactly one element returned.
    dossier_user = DossierUser.objects.get(user=dossier.owner, account=dossier.account)

    dossier_pdf_request: SemanticDocumentPDFRequestV1 = (
        export_semantic_document_wrapper_with_access_check(
            dossier_user=dossier_user,
            external_dossier_id=dossier.external_id,
            semantic_document_uuid=semantic_document_uuid,
        )
    )

    publish(
        message=dossier_pdf_request.model_dump_json().encode(),
        routing_key=ASYNC_DOSSIER_SEMANTIC_DOCUMENT_PDF_WORKER_ROUTING_KEY,
    )

    logger.info(
        f"Exported dossier with uuid {dossier.uuid} for ZKB, Semantic uuid "
        f"{semantic_document_uuid}, request uuid "
        f"{dossier_pdf_request.semantic_document_pdf_request_uuid}"
    )

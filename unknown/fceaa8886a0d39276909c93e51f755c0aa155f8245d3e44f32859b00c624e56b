import datetime
from datetime import date
from enum import Enum
from typing import Optional, List
from uuid import UUID

from pydantic import StringConstraints, BaseModel, Field, RootModel
from typing_extensions import Annotated


class Langugage(str, Enum):
    de = "de"
    fr = "fr"


class AccountNameMortgage(str, Enum):
    bekbe = "bekbe"  # echo (development)
    bekbs = "bekbs"  # sierra (training)
    bekbu = "bekbu"  # uniform (integration)
    bekbz = "bekbz"  # zulu (pre production)
    bekbp = "bekbp"  # papa (production)


class AccountNameFipla(str, Enum):
    bekbfiplae = "bekbfiplae"  # echo (development)
    bekbfiplas = "bekbfiplas"  # sierra (training)
    bekbfiplau = "bekbfiplau"  # uniform (integration)
    bekbfiplaz = "bekbfiplaz"  # zulu (pre production)
    bekbfiplap = "bekbfiplap"  # papa (production)


Username = Annotated[str, StringConstraints(max_length=255)]
Firstname = Annotated[str, StringConstraints(max_length=255)]
Name = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]
BusinessNumber = Annotated[str, StringConstraints(max_length=20)]
Parkey = Annotated[str, StringConstraints(max_length=20)]
CollateralDescription = Optional[Annotated[str, StringConstraints(max_length=255)]]
CollateralPolicyNumber = Optional[Annotated[str, StringConstraints(max_length=255)]]


class Partner(BaseModel):
    parkey: Parkey
    name: Annotated[str, StringConstraints(max_length=255)]
    firstname: Optional[Annotated[str, StringConstraints(max_length=255)]] = None


class BusinessPartner(Partner):
    pers: bool


CollateralNumber = Annotated[str, StringConstraints(max_length=20)]
PropertyNumber = Annotated[str, StringConstraints(max_length=20)]
PropertyCollateralType = Annotated[
    str, StringConstraints(max_length=40)
]  # E.g. '_Hauptdeckung' or 'Nebendeckung'
AttributeKey = Annotated[str, StringConstraints(max_length=40)]


class User(BaseModel):
    username: Username = Field(description="should be the email address as username")
    firstname: Optional[Firstname] = None
    name: Name
    pers: Optional[bool] = False
    active: Optional[bool] = True


class JWTAuthSchemaBase(BaseModel):
    # Minimum Requirements are aud, email_verified, user_roles, account_key
    aud: str = Field(
        default="account",
        description="Audience claim, typically 'account' for user profiles",
    )
    email_verified: bool = Field(description="Indicates if the email has been verified")
    user_roles: List[str] = Field(
        default_factory=list, description="List of roles assigned to the user"
    )
    # Checked on 2025-02-20: The preferred_username is the email address and required via keycloak
    preferred_username: str = Field(description="Usually the same as the email")

    # Others are optional
    email: Optional[str] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None


class JWTAuthSchemaMortgage(JWTAuthSchemaBase):
    account_key: AccountNameMortgage


class DossierCreateJWTMortgage(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular."
    )

    account_name: AccountNameMortgage

    business_partner: BusinessPartner
    partner_partner: Optional[Partner] = None

    dossier_name: DossierName

    language: Langugage

    fico: User = Field(description="The responsible FICO for the dossier")
    current_user: User = Field(
        description="The current user (from the DBP). Will be the owner of the dossier during creation."
    )


class DossierShowJWTMortgage(BaseModel):
    exp: int = Field(
        description="Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular."
    )

    account_name: AccountNameMortgage
    business_partner: BusinessPartner
    partner_partner: Optional[Partner] = None

    language: Langugage

    fico: User = Field(description="The responsible FICO for the dossier")
    current_user: User = Field(
        description="The current user (from the DBP). Will be the owner of the dossier."
    )

    dossier_uuid: Optional[UUID] = None


class DossierShow(BaseModel):
    dossier_uuid: UUID
    business_partner: BusinessPartner
    dossier_name: DossierName
    language: Langugage
    access_mode: str
    work_status_key: str
    work_status_name_de: str
    work_status_name_en: Optional[str] = None
    work_status_name_fr: str
    work_status_name_it: Optional[str] = None
    businesscase_type_key: Optional[str] = None
    businesscase_type_name_de: Optional[str] = None
    businesscase_type_name_en: Optional[str] = None
    businesscase_type_name_fr: Optional[str] = None
    businesscase_type_name_it: Optional[str] = None
    assignee_username: Optional[str] = None
    assignee_firstname: Optional[str] = None
    assignee_lastname: Optional[str] = None
    fico_username: Optional[str] = None
    fico_firstname: Optional[str] = None
    fico_lastname: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    expires_at: datetime.datetime


class DossierProperties(BaseModel):
    business_parkey: Parkey

    fico: Optional[User] = Field(
        description="the responsible financial coach", default=None
    )


class DossierPropertiesUpdates(RootModel):
    root: List[DossierProperties]


class UserAttributes(BaseModel):
    username: Username = Field(
        description="The username (should be an email address) of the user to update"
    )
    firstname: Optional[Firstname] = None
    name: Optional[Name] = None
    pers: Optional[bool] = Field(
        None, description="set to true if the user has access to pers dossiers"
    )
    active: Optional[bool] = Field(
        None, description="Is the user active (True) or inactive (False)"
    )


class UserUpdates(RootModel):
    root: List[UserAttributes]


class BusinessPartnerUpdate(BaseModel):
    business_parkey: Parkey
    business_partner: Optional[BusinessPartner] = None
    partner_partner: Optional[Partner] = None


class Entity(str, Enum):
    BusinessStatus = "BusinessStatus"
    BusinessType = "BusinessType"
    CollateralType = "CollateralType"
    CollateralStatus = "CollateralStatus"
    PropertyType = "PropertyType"
    PropertyCollateralType = "PropertyCollateralType"
    RealEstatePropertyStatus = "RealEstatePropertyStatus"


class Attribute(BaseModel):
    entity: Entity
    key: AttributeKey
    name_de: Annotated[str, StringConstraints(max_length=255)]
    name_fr: Annotated[str, StringConstraints(max_length=255)]


class BusinessCase(BaseModel):
    """Geschäftsfall"""

    business_parkey: Parkey

    business_number: BusinessNumber
    business_type: AttributeKey = Field(
        description="Geschäftsart (Immobilienfinanzierung, ...)"
    )
    business_status: AttributeKey = Field(
        description="Geschäftsstatus (pendent, definitiv, abgelehnt, aufgehoben, auszahlungsgeprüft, nicht zustande gekommen"
    )
    mutation_date: date
    mutation_user: Annotated[str, StringConstraints(max_length=50)]


class RealEstateProperty(BaseModel):
    property_partner: Partner = Field(description="Partner der Liegenschaft")
    property_number: PropertyNumber = Field(description="Liegenschaftsnummer")

    property_type: AttributeKey = Field(
        description="Liegenschaftsobjektart (e.g. Eigentumswohnung, Garage/Abstellplatz)"
    )

    address_street: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    address_street_nr: Optional[Annotated[str, StringConstraints(max_length=20)]] = None
    address_zip: Annotated[str, StringConstraints(max_length=30)]
    address_city: Annotated[str, StringConstraints(max_length=255)]

    land_register_municipality: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = Field(None, description="Grundbuch Gemeinde")
    land_register_id: Optional[Annotated[str, StringConstraints(max_length=100)]] = (
        Field(None, description="Grundbuch Blatt-Nr.")
    )

    status: AttributeKey = Field(description="RealEstatePropertyStatus Attribute")


class CollateralRealEstateProperty(BaseModel):
    property_number: PropertyNumber = Field(description="Liegenschaftsnummer")
    property_collateral_type: AttributeKey = Field(
        description="Liegenschaftsdeckungstyp: Hauptdeckung | Nebendeckung | Verwaltungsgeschäft"
    )


class Collateral(BaseModel):
    """additional fields? sollen values mitgeliefert werden für GUI Mapping? partner parkey kommt separat"""

    business_parkey: Parkey = Field(
        description="Geschäftsfall-Parkey of the collateral"
    )
    business_number: BusinessNumber = Field(description="Geschäfts-Nr.")

    collateral_number: CollateralNumber = Field(description="Deckungs-Nr.")
    collateral_partner: Partner = Field(description="Partner des Deckungsgebers")

    collateral_type: AttributeKey = Field(
        description="e.g. Grundpfand, Faustpfand intern"
    )

    description: CollateralDescription = Field(description="Bemerkung")

    collateral_status: AttributeKey = Field(
        description="Deckungsstatus Todo: tatsächlich gebraucht"
    )

    policy_number: CollateralPolicyNumber = Field(
        description="Policennummer (im Fall der Deckungsart 'Personenversicherungen')"
    )

    real_estate_properties: Optional[List[CollateralRealEstateProperty]] = None


class DossierExport(BaseModel):
    dossier_uuid: UUID
    export_uuid: UUID


class ExportStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"


class DossierExportFeedback(BaseModel):
    export_uuid: UUID
    status: ExportStatus
    message: Optional[Annotated[str, StringConstraints(max_length=500)]] = None


class DossierExportFeedbacks(RootModel):
    root: List[DossierExportFeedback]


class BusinessPartnerUpdates(RootModel):
    root: List[BusinessPartnerUpdate] = []


class AttributeUpdates(RootModel):
    root: List[Attribute]


class BusinessCaseUpdates(RootModel):
    root: List[BusinessCase]


class CollateralUpdates(RootModel):
    root: List[Collateral]


class RealEstatePropertiesUpdates(RootModel):
    root: List[RealEstateProperty]


class ProcessReadyForExportRequest(BaseModel):
    dossier_uuids: Optional[List[UUID]] = None
    not_updated_since: Optional[datetime.datetime] = None

# Instructions for generating a JWK key pair and signing a token

## Install dependencies

```shell
pip install jwcrypto jwt
```

## Generate a JWK key pair
Generate two files, a JWK containing both a public an private key (keep this to yourselves). And a public key (share this with us)

```python

from jwcrypto import jwk as jwcrypto_jwk

key = jwcrypto_jwk.JWK.generate(kty="RSA", kid="swissfex", size=2048, use='sig', alg='RS256')

# Save the private key to a file
with open('public_private_key.jwk', 'w') as public_private_file:
    public_private_file.write(key.export())

# Save the public key to a file
with open('public_key.jwk', 'w') as pub_file:
    pub_file.write(key.export_public())

print("Private and public keys have been saved to 'public_private_key.jwk' and 'public_key.jwk' respectively.")


```

## Sign a token with the private key
Load the private key and use it to sign a token

```python

import json
from jwcrypto import jwk as jwcrypto_jwk
import jwt
import uuid
from datetime import timedelta
from django.utils.timezone import now

def create_expiration_date(minutes=60) -> int:
    """
    Creates an expiration date 60 minutes from the current time as unix timestamp.

    Returns:
        int: The expiration date as a unix timestamp.
    """
    return int(
        (
            now() + timedelta(minutes)
        ).timestamp()
    )

with open("public_private_key.jwk", "r") as key_file:
    jwk_key = jwcrypto_jwk.JWK.from_json(json.dumps(json.loads(key_file.read())))
    pem = jwk_key.export_to_pem(private_key=True, password=None)

claims = {
    "aud": "account",
    "exp": create_expiration_date(),
    "name": "service-swissfex-first service-swissfex-last",
    "given_name": "service-swissfex-first",
    "family_name": "service-swissfex-last",
    "preferred_username": "<EMAIL>",
    "email": "<EMAIL>",
    "user_roles": ["api_role"],
    "account_key": "swissfext", # swissfext for test and swissfex for production
    "jti": str(uuid.uuid4()),  # unique identifier for the token
}

token = jwt.encode(claims, pem, algorithm="RS256")
print("JWT token:")
print(token)

```

# Full code example

```python
import json
import os

from jwcrypto import jwk as jwcrypto_jwk
import jwt
import uuid
from datetime import timedelta
from django.utils.timezone import now


def generate_jwk(overwrite=False):
    # Check if files already exist
    private_file_exists = os.path.exists("public_private_key.jwk")

    # If both files exist, just return
    if private_file_exists and not overwrite:
        print("Both private and public key files already exist. Skipping generation.")
        return

    key = jwcrypto_jwk.JWK.generate(
        kty="RSA", kid="swissfex", size=2048, use="sig", alg="RS256"
    )

    # Save the private key to a file
    with open("public_private_key.jwk", "w") as public_private_file:
        public_private_file.write(key.export())

    # Save the public key to a file
    with open("public_key.jwk", "w") as pub_file:
        pub_file.write(key.export_public())

    print(
        "Private and public keys have been saved to 'public_private_key.jwk' and 'public_key.jwk' respectively."
    )


def generate_jwt():
    def create_expiration_date(minutes=60) -> int:
        """
        Creates an expiration date 60 minutes from the current time as unix timestamp.

        Returns:
            int: The expiration date as a unix timestamp.
        """
        return int(
            (
                now() + timedelta(minutes)
            ).timestamp()
        )

    with open("public_private_key.jwk", "r") as key_file:
        jwk_key = jwcrypto_jwk.JWK.from_json(json.dumps(json.loads(key_file.read())))
        pem = jwk_key.export_to_pem(private_key=True, password=None)

    claims = {
        "aud": "account",
        "exp": create_expiration_date(),
        "name": "service-swissfex-first service-swissfex-last",
        "given_name": "service-swissfex-first",
        "family_name": "service-swissfex-last",
        "preferred_username": "<EMAIL>",
        "email": "<EMAIL>",
        "external_dossier_id": "your_dossier_id",
        "user_roles": ["api_role"],
        "account_key": "swissfex",
        "account_name": "swissfex account",
        "jti": str(uuid.uuid4()),  # unique identifier for the token
    }

    token = jwt.encode(claims, pem, algorithm="RS256")
    print("JWT token:")
    print(token)


if __name__ == "__main__":
    generate_jwk()
    generate_jwt()
```

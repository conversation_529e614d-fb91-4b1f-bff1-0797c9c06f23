import datetime

from bekb.management.commands.process_ready_for_export_dossier import (
    calc_not_updated_since,
)


def test_before_21():
    # Set mock time to today at 20:00
    mock_now = datetime.datetime(2025, 1, 9, 20, 0, tzinfo=datetime.timezone.utc)
    result = calc_not_updated_since(mock_now)
    expected = datetime.datetime(
        2025, 1, 8, 23, 0, tzinfo=datetime.timezone.utc
    )  # Yesterday at 23:00
    assert result == expected


def test_after_21():
    # Set mock time to today at 22:00
    mock_now = datetime.datetime(2025, 1, 9, 22, 0, tzinfo=datetime.timezone.utc)
    result = calc_not_updated_since(mock_now)
    expected = datetime.datetime(
        2025, 1, 9, 19, 0, tzinfo=datetime.timezone.utc
    )  # Today at 19:00
    assert result == expected


def test_exactly_21():
    # Set mock time to today at 21:00
    mock_now = datetime.datetime(2025, 1, 9, 21, 0, tzinfo=datetime.timezone.utc)
    result = calc_not_updated_since(mock_now)
    expected = datetime.datetime(
        2025, 1, 8, 23, 0, tzinfo=datetime.timezone.utc
    )  # Yesterday at 23:00
    assert result == expected

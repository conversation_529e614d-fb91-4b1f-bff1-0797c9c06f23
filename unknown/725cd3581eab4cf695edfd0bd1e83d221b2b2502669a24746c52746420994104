import structlog

import djclick as click

from bekb.api import update_business_partners, list_business_parkeys
from bekb.models import Partner
from bekb.schemas.schemas import (
    BusinessPartnerUpdates,
    BusinessPartner,
    BusinessPartnerUpdate,
)

logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.option("-account_key", default="bekbp", type=click.STRING)
def api_list_business_parkeys(account_key: str):
    """
    python manage.py bekbapi list-business-parkeys -account_key=bekbp

    @param account_key:
    @return:
    """
    # endpoint = "/partner/bekb/api/0.7"
    # url = f"{endpoint}/business-parkeys?account_name={account_key}"
    # token = create_token("BEKB API given name", "BEKB API family name", "<EMAIL>", roles="bekb_api")
    # bekb_api_client = AuthenticatedClient(token)
    #
    # res = bekb_api_client.get(url)
    # print(res)

    res = list_business_parkeys(None, account_key)
    print(res)

    parkey = "********"

    if parkey in res:
        print("Found it")

    p = Partner.objects.filter(parkey=parkey, account__key=account_key).all()
    print(p)

    updates = []
    updates.append(
        BusinessPartnerUpdate(
            business_parkey=parkey,
            business_partner=BusinessPartner(
                parkey=parkey,
                name="changed name",
                firstname="changed firstname",
                pers=False,
            ),
        )
    )
    updates_data = BusinessPartnerUpdates(root=updates)

    update_business_partners(None, account_key, updates_data)

    print(f"list_business_parkeys for account={account_key} done.")

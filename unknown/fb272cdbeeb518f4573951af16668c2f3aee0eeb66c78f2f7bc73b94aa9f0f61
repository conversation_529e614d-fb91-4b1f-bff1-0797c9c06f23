{% extends 'stats/base.html' %}

{% block title %}User Statistics{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            User Statistics
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Detailed statistics about users in the system.
        </p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <!-- Total Users -->
        <div class="bg-indigo-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Users
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-indigo-600">
                    {{ total_users }}
                </dd>
            </div>
        </div>

        <!-- Active Users (Last 30 Days) -->
        <div class="bg-green-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Active Users (Last 30 Days)
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-green-600">
                    {{ active_users }}
                </dd>
                {% if active_users_change is not None and compare %}
                <dd class="mt-2 text-sm {% if active_users_change > 0 %}text-green-600{% elif active_users_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if active_users_change > 0 %}+{% endif %}{{ active_users_change }}% from previous period ({{ active_users_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Staff Users -->
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Staff Users
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-blue-600">
                    {{ staff_users }}
                </dd>
            </div>
        </div>
    </div>

    <!-- Top Users by Dossier Count -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Top Users by Dossier Count
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for user in top_users_by_dossier %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-indigo-600 truncate">
                                {{ user.username }} ({{ user.first_name }} {{ user.last_name }})
                            </p>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ user.dossier_count }} dossiers
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No users found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Recent User Activity -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Recent User Activity
        </h3>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for event in recent_events %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-indigo-600">
                                    {{ event.username }}
                                </p>
                                <p class="text-sm text-gray-500">
                                    {{ event.type }}
                                </p>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ event.created_at|date:"M d, Y H:i" }}
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No recent activity found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}

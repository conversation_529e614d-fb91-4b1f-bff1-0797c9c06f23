[{"key": "account_name"}, {"key": "account_no"}, {"key": "account_type"}, {"key": "accounting_period_from"}, {"key": "accounting_period_to"}, {"key": "address_block"}, {"key": "address_real_estate_primary"}, {"key": "addressline"}, {"key": "addressline_fullname"}, {"key": "ahv_new"}, {"key": "amount_paid"}, {"key": "applicable_annual_salary_declared"}, {"key": "applicable_annual_salary_insured"}, {"key": "assets_cars"}, {"key": "assets_cash_gold"}, {"key": "assets_gross_business"}, {"key": "assets_gross_private"}, {"key": "assets_gross_total"}, {"key": "assets_life_insurance"}, {"key": "assets_net_business"}, {"key": "assets_net_total"}, {"key": "assets_other"}, {"key": "assets_portfolio"}, {"key": "assets_real_estate_capitalized_earnings_value"}, {"key": "assets_real_estate_current_value"}, {"key": "assets_real_estate_main_property"}, {"key": "assets_real_estate_total_gross"}, {"key": "assets_real_estate_total_net"}, {"key": "assets_taxable_global"}, {"key": "assets_taxable_local"}, {"key": "assets_undistributed_inheritances"}, {"key": "availability"}, {"key": "bonus"}, {"key": "building_id"}, {"key": "cadaster_no"}, {"key": "canton_short"}, {"key": "city"}, {"key": "company"}, {"key": "company_contact"}, {"key": "company_id"}, {"key": "confirmation_empty"}, {"key": "confirmation_not_empty"}, {"key": "contract_end_date"}, {"key": "contract_number"}, {"key": "contract_start_date"}, {"key": "contribution_3a"}, {"key": "contributions_pillar_1_2"}, {"key": "cubature"}, {"key": "current_assets"}, {"key": "date_of_birth"}, {"key": "debt_business"}, {"key": "debt_detail_lines"}, {"key": "debt_detail_lines_2"}, {"key": "debt_mortgages"}, {"key": "debt_private"}, {"key": "debt_total"}, {"key": "deductions_donations"}, {"key": "deductions_education"}, {"key": "deductions_illness"}, {"key": "deductions_other"}, {"key": "deductions_total"}, {"key": "deductions_wealth_management"}, {"key": "degree_employment"}, {"key": "document_date"}, {"key": "document_month"}, {"key": "document_title"}, {"key": "document_type"}, {"key": "document_validity_end_date"}, {"key": "document_validity_start_date"}, {"key": "email"}, {"key": "email_date"}, {"key": "email_from"}, {"key": "email_subject"}, {"key": "email_to"}, {"key": "employer"}, {"key": "expense_alimony_children"}, {"key": "expense_alimony_partner"}, {"key": "expense_annuity_contributions"}, {"key": "expense_children_daycare"}, {"key": "firstname"}, {"key": "flag_pillar_3a"}, {"key": "flag_pillar_3b"}, {"key": "floor_number"}, {"key": "fullname"}, {"key": "heat_distribution"}, {"key": "heating_system"}, {"key": "hometown"}, {"key": "iban"}, {"key": "imputed_rental_value_gross"}, {"key": "imputed_rental_value_net"}, {"key": "income_alimony_children"}, {"key": "income_alimony_partner"}, {"key": "income_child_benefits"}, {"key": "income_gross_total"}, {"key": "income_lump_sum"}, {"key": "income_net_total"}, {"key": "income_other"}, {"key": "income_portfolio"}, {"key": "income_real_estate_gross"}, {"key": "income_real_estate_gross_other"}, {"key": "income_real_estate_net"}, {"key": "income_real_estate_net_other"}, {"key": "income_real_estate_net_primary"}, {"key": "income_taxable_global"}, {"key": "income_taxable_local"}, {"key": "income_undistributed_inheritances"}, {"key": "insurance_amount"}, {"key": "insurance_premiums_and_interest_on_savings_accounts"}, {"key": "insurance_yearly_bill_amount"}, {"key": "interest_paid_on_debt"}, {"key": "land_register_area"}, {"key": "land_register_egrid"}, {"key": "land_register_id"}, {"key": "land_register_municipality"}, {"key": "lastname"}, {"key": "marital_status"}, {"key": "mo_interest_rate"}, {"key": "mo_interest_usance"}, {"key": "mo_mortgage_type"}, {"key": "mo_property_address"}, {"key": "mo_total_amount"}, {"key": "mrz"}, {"key": "municipality"}, {"key": "nationality"}, {"key": "native_place"}, {"key": "num_rooms_bathroom"}, {"key": "num_rooms_bedroom"}, {"key": "num_rooms_living"}, {"key": "num_rooms_toilet"}, {"key": "num_rooms_wetroom"}, {"key": "owner_address"}, {"key": "owner_date_of_birth"}, {"key": "owner_fullname"}, {"key": "owner_salutation"}, {"key": "ownership_type"}, {"key": "p1_ahv_new"}, {"key": "p1_contribution_pillar_1"}, {"key": "p1_contribution_pillar_2"}, {"key": "p1_contribution_pillar_3a"}, {"key": "p1_date_of_birth"}, {"key": "p1_deductions_education"}, {"key": "p1_deductions_illness"}, {"key": "p1_employer"}, {"key": "p1_employer_location"}, {"key": "p1_expense_employment"}, {"key": "p1_expense_employment_other"}, {"key": "p1_firstname"}, {"key": "p1_fullname"}, {"key": "p1_income_alimony_children"}, {"key": "p1_income_alimony_partner"}, {"key": "p1_income_alimony_total"}, {"key": "p1_income_board_seats"}, {"key": "p1_income_child_benefits"}, {"key": "p1_income_employed_benefits"}, {"key": "p1_income_employed_main"}, {"key": "p1_income_employed_side"}, {"key": "p1_income_eo"}, {"key": "p1_income_gross_total"}, {"key": "p1_income_lump_sum"}, {"key": "p1_income_net_total"}, {"key": "p1_income_other"}, {"key": "p1_income_pension"}, {"key": "p1_income_pension_ahv"}, {"key": "p1_income_self_main"}, {"key": "p1_income_self_side"}, {"key": "p1_income_social_security"}, {"key": "p1_lastname"}, {"key": "p1_marital_status"}, {"key": "p1_profession"}, {"key": "p2_ahv_new"}, {"key": "p2_contribution_pillar_1"}, {"key": "p2_contribution_pillar_2"}, {"key": "p2_contribution_pillar_3a"}, {"key": "p2_date_of_birth"}, {"key": "p2_deductions_education"}, {"key": "p2_deductions_illness"}, {"key": "p2_email"}, {"key": "p2_employer"}, {"key": "p2_employer_location"}, {"key": "p2_expense_employment"}, {"key": "p2_expense_employment_other"}, {"key": "p2_firstname"}, {"key": "p2_fullname"}, {"key": "p2_income_alimony_children"}, {"key": "p2_income_alimony_partner"}, {"key": "p2_income_alimony_total"}, {"key": "p2_income_board_seats"}, {"key": "p2_income_child_benefits"}, {"key": "p2_income_employed_benefits"}, {"key": "p2_income_employed_main"}, {"key": "p2_income_employed_side"}, {"key": "p2_income_eo"}, {"key": "p2_income_gross_total"}, {"key": "p2_income_lump_sum"}, {"key": "p2_income_net_total"}, {"key": "p2_income_other"}, {"key": "p2_income_pension"}, {"key": "p2_income_pension_ahv"}, {"key": "p2_income_self_main"}, {"key": "p2_income_self_side"}, {"key": "p2_income_social_security"}, {"key": "p2_lastname"}, {"key": "p2_marital_status"}, {"key": "p2_nationality"}, {"key": "p2_phone_primary"}, {"key": "p2_profession"}, {"key": "page_index_current"}, {"key": "page_index_max"}, {"key": "payment_frequency"}, {"key": "period"}, {"key": "person_height"}, {"key": "person_id"}, {"key": "phone_primary"}, {"key": "phone_secondary"}, {"key": "plot_size"}, {"key": "police_no"}, {"key": "product"}, {"key": "projected_assets_retirement"}, {"key": "projected_pension_retirement"}, {"key": "prop_aquisition_title"}, {"key": "prop_notes"}, {"key": "prop_obligations"}, {"key": "prop_pledge"}, {"key": "prop_property"}, {"key": "prop_remarks"}, {"key": "prop_servitudes"}, {"key": "property_address"}, {"key": "property_address_city"}, {"key": "property_address_street"}, {"key": "property_address_street_no"}, {"key": "property_address_zip"}, {"key": "property_area_living_default"}, {"key": "property_area_living_gross"}, {"key": "property_area_living_net"}, {"key": "property_desc"}, {"key": "property_estimation_date"}, {"key": "property_estimation_reason"}, {"key": "property_estimation_value"}, {"key": "property_imputed_rental_value"}, {"key": "property_imputed_rental_value_canton"}, {"key": "property_maintenance_cost"}, {"key": "property_new_value"}, {"key": "property_price"}, {"key": "property_purchase_year"}, {"key": "property_time_value"}, {"key": "property_type"}, {"key": "property_value_ratio"}, {"key": "property_year"}, {"key": "redemption_value_delta"}, {"key": "redemption_value_gross"}, {"key": "redemption_value_net"}, {"key": "salary_base"}, {"key": "salary_benefits_ahv"}, {"key": "salary_benefits_car"}, {"key": "salary_benefits_other"}, {"key": "salary_benefits_travel"}, {"key": "salary_board"}, {"key": "salary_capital_benefits"}, {"key": "salary_comments"}, {"key": "salary_contributions_education"}, {"key": "salary_expenses_actual_other"}, {"key": "salary_expenses_actual_travel"}, {"key": "salary_expenses_overall_car"}, {"key": "salary_expenses_overall_other"}, {"key": "salary_expenses_overall_representation"}, {"key": "salary_gross"}, {"key": "salary_irregular_benefits"}, {"key": "salary_irregular_benefits_desc"}, {"key": "salary_month_gross"}, {"key": "salary_month_net"}, {"key": "salary_month_undefined"}, {"key": "salary_net"}, {"key": "salary_other"}, {"key": "salary_ownership_rights"}, {"key": "salary_paid_amount"}, {"key": "salary_paid_salary"}, {"key": "salary_pillar_2_purchase"}, {"key": "salary_pillar_2_regular"}, {"key": "salary_withholding_tax"}, {"key": "salutation"}, {"key": "section_children"}, {"key": "section_children_adult"}, {"key": "section_supported_persons"}, {"key": "sex"}, {"key": "status"}, {"key": "street"}, {"key": "tax_value_gross"}, {"key": "tax_value_net"}, {"key": "total_amount"}, {"key": "url_email_company"}, {"key": "wef_pledging_possible_amount"}, {"key": "wef_pledging_registered_status"}, {"key": "wef_withdrawal_registered_payout_amount"}, {"key": "wef_withdrawal_registered_repaid_amount"}, {"key": "withdrawal_benefit"}, {"key": "year"}, {"key": "year_construction"}, {"key": "zip"}]
import math

from django.contrib import admin
from import_export import fields, widgets
from import_export.admin import ImportExportModelAdmin, ExportMixin
from import_export.resources import ModelResource
from rangefilter.filters import DateRangeFilter
from django.utils.html import format_html

from bekb.models import (
    RealEstateProperty,
    Attribute,
    Partner,
    BusinessCase,
    Collateral,
    CollateralAssignment,
    BEKBDossierProperties,
    BEKBDossierExport,
    ExportFeedback,
    CollateralRealEstateProperty,
)
from dossier.models import Dossier


@admin.register(Attribute)
class AttributeAdmin(ImportExportModelAdmin):
    list_display = ("entity", "key", "name_de", "name_fr", "created_at", "updated_at")
    list_filter = ("entity", "account")
    search_fields = ("key", "name_de", "name_fr")
    readonly_fields = ["created_at", "updated_at"]


@admin.register(RealEstateProperty)
class RealEstatePropertyAdmin(admin.ModelAdmin):
    list_display = (
        "property_number",
        "address_street",
        "address_zip",
        "address_city",
        "created_at",
        "updated_at",
    )
    list_filter = ("account", "property_type", "status")

    search_fields = (
        "uuid",
        "property_number",
        "address_street",
        "address_city",
        "land_register_id",
    )

    raw_id_fields = ["property_partner"]

    readonly_fields = ["created_at", "updated_at"]


@admin.register(Partner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ("parkey", "name", "firstname", "created_at", "updated_at")
    search_fields = ("uuid", "parkey", "name", "firstname")
    list_filter = ("account",)
    readonly_fields = ["created_at", "updated_at"]


class CollateralInline(admin.TabularInline):
    model = Collateral
    extra = 0

    raw_id_fields = ["business_partner", "collateral_partner"]


@admin.register(BusinessCase)
class BusinessCaseAdmin(admin.ModelAdmin):
    list_display = (
        "business_partner",
        "business_number",
        "business_type",
        "business_status",
        "mutation_date",
        "mutation_user",
        "created_at",
        "updated_at",
    )
    search_fields = ("uuid", "business_number")
    list_filter = ("account",)

    raw_id_fields = ["business_partner"]

    readonly_fields = ["created_at", "updated_at"]

    inlines = (CollateralInline,)


@admin.register(Collateral)
class CollateralAdmin(admin.ModelAdmin):
    list_display = (
        "collateral_number",
        "collateral_type",
        "collateral_status",
        "created_at",
        "updated_at",
    )
    search_fields = (
        "collateral_number",
        "business_partner__parkey",
        "collateral_partner__parkey",
    )
    list_filter = ("account", "collateral_type", "collateral_status")

    raw_id_fields = ["business_partner", "collateral_partner", "businesscase"]

    readonly_fields = ["created_at", "updated_at"]


@admin.register(CollateralAssignment)
class CollateralAssignmentAdmin(admin.ModelAdmin):
    list_display = (
        "uuid",
        "semantic_document",
        "collateral",
        "created_at",
        "updated_at",
    )
    list_filter = ("account",)

    search_fields = (
        "uuid",
        "semantic_document__uuid",
        "collateral__collateral_number",
        "collateral__uuid",
    )

    raw_id_fields = ["semantic_document", "collateral", "property"]

    readonly_fields = ["created_at", "updated_at"]


@admin.register(CollateralRealEstateProperty)
class CollateralRealEstatePropertyAdmin(admin.ModelAdmin):
    list_display = (
        "account",
        "collateral",
        "realestate_property",
        "created_at",
        "updated_at",
    )

    search_fields = (
        "uuid",
        "collateral__business_partner__parkey",
        "collateral__collateral_partner__parkey",
        "collateral__collateral_number",
        "realestate_property__property_number",
    )

    list_filter = ("account", "collateral__collateral_type")

    raw_id_fields = ["collateral", "realestate_property"]

    readonly_fields = ["created_at", "updated_at"]


@admin.register(BEKBDossierProperties)
class BEKBDossierPropertiesAdmin(admin.ModelAdmin):
    list_display = [
        "dossier",
        "business_partner",
        "partner_partner",
        "business_case",
        "pers",
        "created_at",
        "updated_at",
    ]

    search_fields = [
        "dossier__name",
        "dossier__uuid",
        "uuid",
        "business_partner__parkey",
        "business_partner__name",
        "partner_partner__parkey",
        "partner_partner__name",
    ]
    list_filter = ("account", "pers")

    raw_id_fields = ["business_partner", "partner_partner", "business_case", "dossier"]

    readonly_fields = ["created_at", "updated_at"]

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == "dossier":
            kwargs["queryset"] = Dossier.objects.all().order_by("name")
        # No needed anymore for now as we use raw_id_fields
        # elif db_field.name == "partner_partner" or db_field.name == "business_partner":
        #     kwargs["queryset"] = Partner.objects.all().order_by("parkey")
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class BEKBDossierExportResource(ModelResource):
    dossier_uuid = fields.Field(attribute="dossier__uuid", column_name="Dossier UUID")
    dossier_name = fields.Field(attribute="dossier__name", column_name="Dossier Name")
    dossier_created_at = fields.Field(
        attribute="dossier__created_at", column_name="Dossier Created At"
    )
    dossier_age_days = fields.Field(
        attribute="dossier__created_at",
        column_name="Dossier Age (Days)",
        widget=widgets.IntegerWidget(),
    )

    def get_queryset(self):
        return super().get_queryset().order_by("created_at")

    class Meta:
        model = BEKBDossierExport
        fields = (
            "uuid",
            "file",
            "created_at",
            "updated_at",
            "dossier_uuid",
            "dossier_name",
            "dossier_created_at",
            "dossier_age_days",
        )
        export_order = (
            "uuid",
            "file",
            "created_at",
            "updated_at",
            "dossier_uuid",
            "dossier_name",
            "dossier_created_at",
            "dossier_age_days",
        )

    def dehydrate_dossier_uuid(self, export):
        return str(export.dossier.uuid)

    def dehydrate_uuid(self, export):
        return str(export.uuid)

    def dehydrate_file(self, export):
        return str(export.file.uuid)

    def dehydrate_dossier_age_days(self, export):
        return (export.created_at - export.dossier.created_at).days + 1


@admin.register(BEKBDossierExport)
class BEKBDossierExportAdmin(ExportMixin, admin.ModelAdmin):
    resource_class = BEKBDossierExportResource

    def get_dossier_url(self, obj):
        if obj.file:
            url = obj.file.fast_url
            return format_html('<a href="{}" target="_blank">Download Export</a>', url)
        return "N/A"

    get_dossier_url.short_description = "Export URL"

    list_display = (
        "uuid",
        "account",
        "created_at",
        "updated_at",
        "dossier_uuid",
        "dossier_name",
        "dossier_created_at",
        "dossier_age_days",
        "get_dossier_url",
    )
    ordering = ["-created_at"]
    search_fields = ["uuid", "dossier__name", "dossier__uuid"]
    list_filter = (
        "account",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
        ("dossier__created_at", DateRangeFilter),
    )
    readonly_fields = ("uuid", "account", "dossier", "file", "created_at", "updated_at")

    def dossier_uuid(self, obj):
        return obj.dossier.uuid

    def dossier_name(self, obj):
        return obj.dossier.name

    def dossier_created_at(self, obj):
        return obj.dossier.created_at

    def dossier_age_days(self, obj):
        # .days rounds down
        return math.ceil((obj.created_at - obj.dossier.created_at).days + 1)

    dossier_uuid.short_description = (
        "Dossier UUID"  # This sets the column header in the admin list view
    )


@admin.register(ExportFeedback)
class ExportFeedbackAdmin(admin.ModelAdmin):
    list_display = ("uuid", "status", "created_at", "updated_at")
    list_filter = ("account", "status")
    search_fields = [
        "uuid",
        "export__uuid",
        "export__dossier__uuid",
        "export__dossier__name",
    ]
    readonly_fields = ["created_at", "updated_at"]

from django.apps import AppConfig


class ZkbConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "zkb"

    def ready(self):
        from dossier.models import account_specific_access_checks
        from zkb.external_auth import check_dossier_access_external_call_zkb

        zkb_external_check = "zkb external check (call zkb)"
        account_specific_access_checks[zkb_external_check] = (
            check_dossier_access_external_call_zkb
        )

        from zkb.external_auth import check_zkb_dossier_access_external_always_true

        zkb_external_check_always_true = "zkb external check always true"
        account_specific_access_checks[zkb_external_check_always_true] = (
            check_zkb_dossier_access_external_always_true
        )

        from zkb.external_auth import check_zkb_dossier_access_external_always_false

        zkb_external_check_always_false = "zkb external check always false"

        account_specific_access_checks[zkb_external_check_always_false] = (
            check_zkb_dossier_access_external_always_false
        )

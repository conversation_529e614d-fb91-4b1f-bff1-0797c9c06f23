[pytest]
DJANGO_SETTINGS_MODULE = projectconfig.test_settings
python_files = tests.py test_*.py *_tests.py

; addopts = --reuse-db --no-cov
addopts = --create-db
asyncio_mode = auto
log_cli = False
log_cli_level = INFO

filterwarnings =
    ignore::DeprecationWarning:pydantic\..*
    ignore::DeprecationWarning:pandas\..*

markers =
    integration: marks tests as integration tests (deselect with '-m "not integration"')
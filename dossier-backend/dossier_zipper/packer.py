import asyncio

import structlog
import re
import shutil
import uuid
import zipfile
from dataclasses import dataclass
from io import Bytes<PERSON>
from pathlib import Path
from shutil import unpack_archive
from tempfile import TemporaryDirectory
from typing import Dict, List
from uuid import uuid4, UUID

import PyPDF2
import click
import minio
import requests
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter

from dossier.models import ExportStrategy
from dossier.schemas import (
    SemanticDossier,
    ProcessedFileFullApiData,
    SemanticPageFullApiData,
)
from dossier.services import download_extraction_excel_feature
from dossier_zipper.helpers import rotate_pdf, add_document_extraction_excel
from semantic_document.services_pdf import (
    add_document_info_to_pdfwriter,
    create_semantic_document_pdf,
    add_annotations_to_page,
)
from semantic_document.schemas_page_annotations import (
    UserAnnotationsSchema,
)
from django.conf import settings
from projectconfig.settings import (
    DOCUMENT_BROWSER_BUILD_PATH,
    S3_ACCESS_KEY,
    S3_SECRET_KEY,
    S3_SECURE,
    test_dossier,
    S3_ENDPOINT_URL,
    ZKB_VBV_DOCUMENT_CATEGORY_KEY,
    MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX,
)
from semantic_document.models import SemanticDocument, SemanticPage
from workers.services.pdfa import convert_and_get_pdfa

logger = structlog.get_logger()


@dataclass
class SemanticDocumentExport:
    semantic_document_uuid: UUID
    filename: str
    pages: int


@dataclass
class DocumentPackage:
    files: List[Path]
    semantic_document_exports: Dict[UUID, SemanticDocumentExport]


def get_target_filename_from_semantic_document(
    dest_path: Path, doc_filename: str, add_uuid_suffix: bool, doc_uuid: str
) -> Path:
    """
    Calculate target filename for a file. Example:

    /tmp/tmpier_rqbx/310 Steuererklärung Mustermann Max ZH 2019 d5559300-b669-48b8-b278-a8d949dfc815.pdf

    @param dest_path: Root path of all files
    @param doc_filename: Standard filename that has been generated for the document
    @param add_uuid_suffix: If True then the UUID should be added to the filename (with a space in front)
    @param doc_uuid: UUID of the document
    @return: Path where the file should be written
    """
    if "/" in doc_filename or "/" in doc_filename:
        doc_filename = doc_filename.replace("/", "_").replace("/", "_")

    parts = doc_filename.split(".")

    if add_uuid_suffix and len(parts) > 1:
        name = ".".join(parts[:-1])

        # Define a margin of 30 chars for ID + document category title + ".pdf" (and then add the suffix)
        max_length = MAX_LENGTH_SEMANTIC_DOCUMENT_TITLE_SUFFIX + 30
        name = name[
            :max_length
        ]  # Limit the name to 200 characters, as max filename length is 255 on unix systems
        suffix = parts[-1]
        document_file_path: Path = dest_path / f"{name} {doc_uuid}.{suffix}"
    else:
        document_file_path: Path = dest_path / doc_filename

    return document_file_path


def package_documents(
    semantic_dossier: SemanticDossier,
    dest_path: Path,
    add_metadata: bool = True,
    add_document_info: bool = True,
    add_uuid_suffix: bool = False,
    zkb_vbv_use_extracted_file: bool = settings.ZKB_VBV_USE_EXTRACTED_FILE,
    enable_pdfa_conversion_for_export: bool = False,
) -> DocumentPackage:
    """
    Create pdfs for all semantic documents.
    There is one pdf per semantic document.
    These are then exported and zipped.

    @param enable_pdfa_conversion_for_export: bool
    @param zkb_vbv_use_extracted_file:
    @param add_uuid_suffix:
    @param semantic_dossier: SemanticDossier
    @param dest_path: Path
    @param add_metadata: bool
    @param add_document_info: bool
    @return: DocumentPackage
    """
    dest_path.mkdir(parents=True, exist_ok=True)

    files = []

    for original_file, fileextraction in semantic_dossier.extracted_files.items():
        for file, file_exception in fileextraction.exceptions.items():
            file = add_exception_file(semantic_dossier, file_exception, dest_path)
            files.append(file)

    for (
        extracted_file_path,
        file_exception,
    ) in semantic_dossier.processing_exceptions.items():
        file = add_exception_file(semantic_dossier, file_exception, dest_path)
        files.append(file)

    semantic_document_exports = {}
    for document in semantic_dossier.semantic_documents:
        # Calculate the target path for the document
        document_file_path = get_target_filename_from_semantic_document(
            dest_path, document.filename, add_uuid_suffix, document.uuid
        )

        handled = write_special_document_in_packer(
            document_file_path, document, semantic_dossier, zkb_vbv_use_extracted_file
        )
        if not handled:
            # Compose the target file from the pages of the semantic document
            write_normal_document_in_packer(
                document_file_path,
                document,
                semantic_dossier,
                add_document_info,
                add_metadata,
            )

        semantic_document_exports[document.uuid] = SemanticDocumentExport(
            semantic_document_uuid=document.uuid,
            filename=document.filename,
            pages=len(document.semantic_pages),
        )

        if enable_pdfa_conversion_for_export:
            # Convert the pdf to pdfa
            response_content = convert_and_get_pdfa(
                document_path=document_file_path,
                document_uuid=str(document.uuid),
            )

            # Download the pdfa file to document_file_path overwriting the original file
            with open(document_file_path, "wb") as fp:
                fp.write(response_content)

        files.append(document_file_path)
    return DocumentPackage(
        files=files, semantic_document_exports=semantic_document_exports
    )


async def async_process_document(
    document,
    semantic_dossier,
    dest_path,
    add_uuid_suffix,
    zkb_vbv_use_extracted_file,
    add_document_info,
    add_metadata,
    enable_pdfa_conversion_for_export,
    convert_and_download_pdfa_async,
):
    # Calculate the target path for the document
    document_file_path = get_target_filename_from_semantic_document(
        dest_path, document.filename, add_uuid_suffix, document.uuid
    )

    # Attempt special handling
    handled = write_special_document_in_packer(
        document_file_path, document, semantic_dossier, zkb_vbv_use_extracted_file
    )

    if not handled:
        # Compose the target file from the pages of the semantic document
        write_normal_document_in_packer(
            document_file_path,
            document,
            semantic_dossier,
            add_document_info,
            add_metadata,
        )

    semantic_document_export = SemanticDocumentExport(
        semantic_document_uuid=document.uuid,
        filename=document.filename,
        pages=len(document.semantic_pages),
    )

    if enable_pdfa_conversion_for_export:
        # Convert the pdf to pdf/a asynchronously
        response_content = await convert_and_download_pdfa_async(
            document_path=document_file_path,
            document_uuid=str(document.uuid),
        )

        # Download the pdfa file, overwriting the original file
        with open(document_file_path, "wb") as fp:
            fp.write(response_content)

    # Return both the export info and the file path so the caller can aggregate results
    return semantic_document_export, document_file_path


async def process_all_documents(
    semantic_dossier,
    dest_path,
    add_uuid_suffix,
    zkb_vbv_use_extracted_file,
    add_document_info,
    add_metadata,
    enable_pdfa_conversion_for_export,
    convert_and_download_pdfa_async,
    max_concurrency=10,
):
    # Create a semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrency)

    tasks = [
        async_process_document(
            document,
            semantic_dossier,
            dest_path,
            add_uuid_suffix,
            zkb_vbv_use_extracted_file,
            add_document_info,
            add_metadata,
            enable_pdfa_conversion_for_export,
            convert_and_download_pdfa_async,
            semaphore,  # Pass semaphore down
        )
        for document in semantic_dossier.semantic_documents
    ]

    # Run all tasks concurrently, with concurrency limited by the semaphore
    results = await asyncio.gather(*tasks)

    # Aggregate results
    semantic_document_exports = {}
    files = []

    for semantic_document_export, document_file_path in results:
        semantic_document_exports[semantic_document_export.semantic_document_uuid] = (
            semantic_document_export
        )
        files.append(document_file_path)

    return semantic_document_exports, files


def write_special_document_in_packer(
    document_file_path: Path,
    document: SemanticPageFullApiData,
    semantic_dossier: SemanticDossier,
    zkb_vbv_use_extracted_file: bool,
) -> bool:
    """

    @param semantic_dossier:
    @param document_file_path: Destination path of where the file should be created (includes filename)
    @param document: Information about semantic document that should be transformed into a file
    @return: True if the document has been handled in a special way. False if it should be treated normally
    """
    if zkb_vbv_use_extracted_file and (
        document.document_category.name == ZKB_VBV_DOCUMENT_CATEGORY_KEY
    ):
        # This can contain a signature so we do not want this to be modified
        # We should deliver the extracted file instead of the doc created from the
        # semantic document.
        if document.semantic_pages:
            sem_page: SemanticPageFullApiData = document.semantic_pages[0]
            sem_page_db = SemanticPage.objects.get(uuid=sem_page.uuid)
            extracted_file = sem_page_db.processed_page.processed_file.extracted_file
            logger.info(extracted_file.path_from_original)

            fast_url = extracted_file.file.get_fast_url()
            res = requests.get(fast_url)
            file_size_bytes = open(document_file_path, "wb").write(res.content)

            assert (
                file_size_bytes > 0
            ), f"Could not download original file from {fast_url} for semantic document {sem_page.uuid}"

            return True
        else:
            # Ignore this empty document but put a warning
            logger.warning(
                f"Found empty ZKB_VBV document in dossier {semantic_dossier.uuid} for document {document.uuid}, doc_file_path={document_file_path}"
            )
        return True

    return False


class PackerException(Exception):
    pass


def write_normal_document_in_packer(
    document_file_path, document, semantic_dossier, add_document_info, add_metadata
):
    process_files: dict[str, ProcessedFileFullApiData] = (
        semantic_dossier.processed_files
    )

    def get_pdf_url(page):
        pages = process_files[page.source_file_uuid].pages
        processed_page = pages[str(page.source_page_number)]
        dossier_file_uuid = processed_page.searchable_pdf_dossier_file_uuid
        return semantic_dossier.dossier_files[str(dossier_file_uuid)].url

    def get_annotations(page):
        if hasattr(page, "annotations"):
            return [
                UserAnnotationsSchema(
                    annotation_group_uuid=ann.annotation_group_uuid,
                    annotation_type=ann.annotation_type,
                    bbox_top=ann.bbox_top,
                    bbox_left=ann.bbox_left,
                    bbox_width=ann.bbox_width,
                    bbox_height=ann.bbox_height,
                    text=ann.text,
                    hexcolor=ann.hexcolor,
                )
                for ann in page.annotations
            ]
        return None

    # Create PDF using semantic_document_pdf service
    writer = create_semantic_document_pdf(
        semantic_document=document,
        pages=document.semantic_pages,
        get_pdf_url_func=get_pdf_url,
        get_annotations_func=get_annotations,
        add_metadata=add_metadata,
    )

    if add_document_info:
        add_document_info_to_pdfwriter(writer, document)

    writer.write(document_file_path)


def package_document(
    semantic_dossier: SemanticDossier,
    dest_path: Path,
    add_uuid_suffix: bool = False,
    enable_pdfa_conversion_for_export: bool = False,
) -> DocumentPackage:
    """
    Create a single pdf by merging all semantic documents
    This is then zipped up.

    This is honestly a bit of a hack, as it's embedding a switch into the async_process_dossier_zip_request function.

    We look into the user account to see if document_export_strategy is set to 'SINGLEPDF' or 'MULTIPLEPDF'.

    and based off that we either call package_documents or package_document

    @param semantic_dossier:
    @param dest_path:
    @return:
    """
    # Create a single pdf by merging all semantic documents
    dest_path.mkdir(parents=True, exist_ok=True)

    # Initialize PdfWriter to collect all documents into a single PDF
    writer: PdfWriter = PdfWriter()

    semantic_document_exports = {}

    toc_page_num: int = (
        0  # Keep track of the current page number for the table of contents
    )

    for document in semantic_dossier.semantic_documents:
        process_files: dict[str, ProcessedFileFullApiData] = (
            semantic_dossier.processed_files
        )

        def get_pdf_url(page):
            pages = process_files[page.source_file_uuid].pages
            processed_page = pages[str(page.source_page_number)]
            dossier_file_uuid = processed_page.searchable_pdf_dossier_file_uuid
            return semantic_dossier.dossier_files[str(dossier_file_uuid)].url

        def get_annotations(page):
            if hasattr(page, "annotations"):
                return [
                    UserAnnotationsSchema(
                        annotation_group_uuid=ann.annotation_group_uuid,
                        annotation_type=ann.annotation_type,
                        bbox_top=ann.bbox_top,
                        bbox_left=ann.bbox_left,
                        bbox_width=ann.bbox_width,
                        bbox_height=ann.bbox_height,
                        text=ann.text,
                        hexcolor=ann.hexcolor,
                    )
                    for ann in page.annotations
                ]
            return None

        for page in document.semantic_pages:
            page: SemanticPageFullApiData
            pdf_page_url = get_pdf_url(page)
            pdf_reader: PdfReader = PyPDF2.PdfReader(
                BytesIO(requests.get(pdf_page_url).content)
            )
            pdf_page = rotate_pdf(pdf_reader.pages[0], page.rotation_angle)

            # Add annotations to the page
            annotations = get_annotations(page)
            if annotations and len(annotations) > 0:
                add_annotations_to_page(pdf_page, annotations, writer)

            writer.add_page(pdf_page)

        # Add a bookmark for the document in the table of contents
        writer.add_outline_item(document.filename, toc_page_num)

        semantic_document_exports[document.uuid] = SemanticDocumentExport(
            semantic_document_uuid=document.uuid,
            filename=document.filename,
            pages=len(document.semantic_pages),
        )

        # Update toc_page_num for the next document
        toc_page_num += len(document.semantic_pages)

    # Write the single PDF file
    # we use regex to remove any non-alphanumeric characters from the dossier name and replace them with underscores
    # as filenames with spaces in them can cause issues
    if add_uuid_suffix:
        document_file_path: Path = (
            dest_path
            / f"Combined_Document_{re.sub(r'[^a-zA-Z0-9]', '_', semantic_dossier.name)}_{str(semantic_dossier.uuid)}.pdf"
        )
    else:
        document_file_path: Path = (
            dest_path
            / f"Combined_Document_{re.sub(r'[^a-zA-Z0-9]', '_', semantic_dossier.name)}.pdf"
        )
    writer.write(document_file_path)

    if enable_pdfa_conversion_for_export:
        # Convert the pdf to pdfa
        response_content = convert_and_get_pdfa(
            document_path=document_file_path,
            document_uuid=str(semantic_dossier.semantic_documents[0].uuid),
        )

        # Download the pdfa file to document_file_path overwriting the original file
        with open(document_file_path, "wb") as fp:
            fp.write(response_content)

    return DocumentPackage(
        files=[document_file_path], semantic_document_exports=semantic_document_exports
    )


def add_exception_file(semantic_dossier, file_exception, dest_path):
    extracted_file = semantic_dossier.extracted_files_v2[str(file_exception.file_uuid)]
    dossier_file = semantic_dossier.dossier_files[str(extracted_file.dossier_file_uuid)]
    dest_file = (
        dest_path / "000 HypoDossier Problems" / extracted_file.path_from_original
    )
    dest_file.parent.mkdir(parents=True, exist_ok=True)
    with dest_file.open("wb") as fp:
        fp.write(requests.get(dossier_file.url).content)
    return dest_file


def create_package_zip(package_path: Path, dest_path: Path):
    with zipfile.ZipFile(dest_path / "package.zip", "w") as zf:
        for file in package_path.glob("**/*"):
            zf.write(file, file.relative_to(package_path))


def add_document_browser_viewer(semantic_dossier: SemanticDossier, dest_path: Path):
    """
    Copy react code, create done.js, create features.js, copy logo, create and customize index.html.

    all inside of dest_path locally page_images_source_path is the path to the temp dir 'pageimages' where the images are in
    dest_path is the path to the temp dir 'package' where the doc browser could be created
    """

    build_path = Path(DOCUMENT_BROWSER_BUILD_PATH)
    assert build_path.exists()

    app_name = "hypodossier-app"
    app_path = dest_path / app_name
    app_path.mkdir()
    # copy build data
    dest_build_path = app_path / "build"
    shutil.unpack_archive(build_path, app_path)

    # setup config.js
    config_js = dest_build_path / "js/config.js"
    config_js.write_text(
        """
window.config = {
    features: [
        {
            "key": "enableFeedbackForm",
            "name": "Feedback form",
            "description": "Send ",
            "active": true
        },
        {
            "key": "enableDossierSorting",
            "name": "Sorting",
            "description": "Sorting/ordering dossiers in page/data/compact view",
            "active": true
        },
        {
            "key": "enableErrorDetail",
            "name": "Enable error detail",
            "description": "Hide details info in error section",
            "active": false
        },
        {
            "key": "enableDossierSearch",
            "name": "Search",
            "description": "Search functionality in page/data/compact view",
            "active": false
        },
        {
            "key": "enableZoomFeature",
            "name": "Zoom feature",
            "description": "Zoom feature in page view",
            "active": true
        },
        {
            "key": "enableDebugDocument",
            "name": "Debug document",
            "description": "Debug document on detail page",
            "active": true
        },
        {
            "key": "enableFeatureColorLayer",
            "name": "Document grouping by color",
            "description": "Document grouping by color",
            "active": false
        }],
    FEEDBACK_BACKEND_HOST: 'https://feedback.service.hypodossier.ch',
    PATH_TO_BUILD: './hypodossier-app/build/',
}
"""
    )

    index_html_build_path = dest_build_path / "index.html"
    # copy page images

    page_images = app_path / "pageimages"
    processed_files = semantic_dossier.processed_files
    for semantic_document in semantic_dossier.semantic_documents:
        for page in semantic_document.semantic_pages:
            processed_page = processed_files[page.source_file_uuid].pages[
                page.source_page_number
            ]
            dest_file = page_images / f"{uuid4()}.jpg"
            dest_file.parent.mkdir(exist_ok=True)
            page_image_url = semantic_dossier.dossier_files[
                str(processed_page.image_dossier_file_uuid)
            ].url
            with dest_file.open("wb") as fp:
                fp.write(requests.get(page_image_url).content)
            processed_page.image = f"{dest_file.relative_to(app_path)}"
            processed_page.searchable_pdf = ""
            processed_page.searchable_txt = ""

    # setup done.js
    # semantic_dossier.dossier_files = {}
    # semantic_dossier.extracted_files_v2 = {}

    json_data = semantic_dossier.model_dump_json()
    done_js = app_path / "done.js"
    done_js.write_text("window.dossierData = " + json_data)

    # set the logo url
    (dest_build_path / "static/js").glob("*.js")

    # # replace company logo
    # if semantic_dossier.company:
    #     try:
    #         company_logo_path = dest_build_path / 'static/media/company_logo.png'
    #         logo_path = get_company_logo_path(semantic_dossier.company)
    #         shutil.copy(logo_path, company_logo_path)
    #     except Exception as e:
    #         logging.warning(f"could not replace company logo. e={e}", exc_info=True)

    # setup index.html
    html = index_html_build_path.read_text()
    done_script = f'<script src="./{app_name}/done.js"></script>'
    # feature_script = f'<script src="./{app_name}/features.js"></script>'
    html = html.replace('<div id="root"></div>', f'<div id="root"></div>{done_script}')
    html = html.replace("./favicon.ico", f"./{app_name}/build/favicon.ico")
    html = html.replace("./static/", f"./{app_name}/build/static/")
    html = html.replace("/js/config.js", f"./{app_name}/build/js/config.js")
    html = html.replace("/js/feedback.js", f"./{app_name}/build/js/feedback.js")
    html = html.replace("/js/jquery.js", f"./{app_name}/build/js/jquery.js")
    html = html.replace("./manifest.json", f"./{app_name}/build/manifest.json")

    index_html = dest_path / "001 Hypodossier Document Browser.html"
    index_html.write_text(html)


def package_offline_version(
    semantic_dossier,
    dest_path,
    with_document_browser_viewer=True,
    add_metadata_json=True,
    add_uuid_suffix=False,
    enable_pdfa_conversion_for_export=False,
):
    export_strategy = None

    if len(semantic_dossier.semantic_documents) > 0:
        first_semantic_document = semantic_dossier.semantic_documents[0]

        account = (
            SemanticDocument.objects.select_related("dossier__account")
            .get(uuid=first_semantic_document.uuid)
            .dossier.account
        )

        export_strategy = account.document_export_strategy

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        if export_strategy == ExportStrategy.SINGLEPDF:
            package_document(
                semantic_dossier=semantic_dossier,
                dest_path=temp_path,
                add_uuid_suffix=add_uuid_suffix,
                enable_pdfa_conversion_for_export=enable_pdfa_conversion_for_export,
            )
        else:
            package_documents(
                semantic_dossier=semantic_dossier,
                dest_path=temp_path,
                add_metadata=add_metadata_json,
                add_uuid_suffix=add_uuid_suffix,
                enable_pdfa_conversion_for_export=enable_pdfa_conversion_for_export,
            )

        if with_document_browser_viewer:
            add_document_browser_viewer(semantic_dossier, temp_path)

        # TODO: does not work because of the wrong assumption that the aggregated page object index is the semantic
        #  page index. see also issue https://gitlab.com/hypodossier/dossier-zipper/-/issues/1
        # check if excel feature enabled for the account
        excel_feature = download_extraction_excel_feature(semantic_dossier.uuid)
        if excel_feature:
            add_document_extraction_excel(semantic_dossier, temp_path)
            assert (temp_path / "000 Hypodossier Datenextraktion.xlsx").exists()

        create_package_zip(temp_path, dest_path)


@click.command()
@click.argument("dest_folder", type=click.types.Path())
def save_demo_dossier_result(dest_folder):
    semantic_dossier = upload_demo_dossier()
    result_path = Path(dest_folder)
    result_path.mkdir(exist_ok=True, parents=True)
    package_offline_version(semantic_dossier, result_path)
    unpack_archive(result_path / "package.zip", result_path / "package")


def upload_demo_dossier():
    client = minio.Minio(
        S3_ENDPOINT_URL, S3_ACCESS_KEY, S3_SECRET_KEY, secure=S3_SECURE
    )
    test_uuid = uuid.uuid4()
    assert test_dossier.exists()
    semantic_dossier = SemanticDossier.parse_file(
        test_dossier / "semantic-dossier.json"
    )
    for file in test_dossier.glob("**/*"):
        if file.is_file() and file.name != "semantic-dossier.json":
            bytes = file.read_bytes()
            data = BytesIO(bytes)
            object_name = f"{test_uuid}/{file.relative_to(test_dossier)}"
            client.put_object("test", object_name, data, len(bytes))
            dossier_file_uuid = file.parent.name
            semantic_dossier.dossier_files[str(dossier_file_uuid)].url = (
                client.presigned_get_object("test", object_name)
            )
    return semantic_dossier

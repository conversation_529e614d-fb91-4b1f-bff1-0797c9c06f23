from uuid import UUID

import djclick as click
import structlog

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.fakes import BekbAccountFactoryFaker
from bekb.schemas.schemas import AccountNameMortgage, AccountNameFipla
from bekb.services import process_ready_for_export_dossiers
from bekb.services_api import create_test_dossiers_ready_for_export
from dossier.models import Dossier
from semantic_document.services_external import create_example_annotations_simply_law

logger = structlog.get_logger()

"""
Group of commands for manual testing of BEKB and BEKBFipla accounts.
This module provides commands to create test dossiers, add annotations, and process exports.

Example workflow:
    # 1. Create a test dossier (returns a UUID in the output)
    python manage.py process_export_integration_test_manual create-test-dossier --account-key bekbe

    # 2. Add example annotations to the dossier (use UUID from step 1)
    python manage.py process_export_integration_test_manual add-annotations YOUR_DOSSIER_UUID

    # 3. Process the dossier for export (use same UUID)
    python manage.py process_export_integration_test_manual process-export YOUR_DOSSIER_UUID

    # You can also create multiple dossiers and process them together:
    python manage.py process_export_integration_test_manual create-test-dossier --account-key bekbfiplae --count 2
    python manage.py process_export_integration_test_manual process-export UUID1 UUID2
"""


@click.group()
def cli():
    """Management commands for export integration testing

    Example:
        # List available commands
        python manage.py process_export_integration_test_manual --help
    """
    pass


@cli.command()
@click.option(
    "--account-key",
    type=click.Choice(
        [
            # Mortgage accounts
            "bekbe",  # echo (development)
            "bekbs",  # sierra (training)
            "bekbu",  # uniform (integration)
            "bekbz",  # zulu (pre-production)
            "bekbp",  # papa (production)
            # Fipla accounts
            "bekbfiplae",  # echo (development)
            "bekbfiplas",  # sierra (training)
            "bekbfiplau",  # uniform (integration)
            "bekbfiplaz",  # zulu (pre-production)
            "bekbfiplap",  # papa (production)
        ]
    ),
    default="bekbe",
    help="Account key to use (environment: e=echo/dev, s=sierra/training, u=uniform/int, z=zulu/preprod, p=papa/prod)",
)
@click.option("--count", default=1, help="Number of dossiers to create")
def create_test_dossier(account_key: str, count: int):
    """Create test dossiers ready for export

    Examples:
        # Create a single test dossier with default account
        python manage.py process_export_integration_test_manual create-test-dossier

        # Create test dossiers for different account types
        python manage.py process_export_integration_test_manual create-test-dossier --count 1 --account-key bekbe
        python manage.py process_export_integration_test_manual create-test-dossier --count 1 --account-key bekbfiplae
    """
    # Convert account key to enum value and determine factory class
    try:
        account_name = (
            AccountNameFipla[account_key]
            if "fipla" in account_key or "fila" in account_key
            else AccountNameMortgage[account_key]
        )
        factory_class = (
            BekbFiplaAccountFactory
            if isinstance(account_name, AccountNameFipla)
            else BekbAccountFactoryFaker
        )
    except KeyError:
        raise click.BadParameter(f"Invalid account key: {account_key}")

    # Create the test dossiers
    created_dossiers = create_test_dossiers_ready_for_export(
        account_name=account_name,
        factory_class=factory_class,
        count=count,
    )

    click.echo(
        f"Created {created_dossiers} dossier(s) for account {account_key}, with UUIDs:{[dossier.uuid for dossier in created_dossiers]}"
    )


@cli.command()
@click.argument("dossier_uuid")
def add_annotations(dossier_uuid: str):
    """Add example annotations to a semantic dossier

    Examples:
        # Add annotations to a specific dossier
        python manage.py process_export_integration_test_manual add-annotations 0c3cb1cf-414e-4a8d-9c7f-53d91ae70fb2
    """
    dossier = Dossier.objects.get(uuid=dossier_uuid)

    # Add annotations to each page of each document
    annotation_count = 0

    for semantic_page in dossier.semantic_pages.all():
        create_example_annotations_simply_law(semantic_page)
        # Count annotations (4 boxes in first highlight + 5 boxes in second highlight + 1 comment)
        annotation_count += 10

    click.echo(f"Added {annotation_count} annotations to dossier {dossier_uuid}")


@cli.command()
@click.argument("dossier_uuids", nargs=-1)
def process_export(dossier_uuids):
    """Process ready for export dossiers

    Examples:
        # Process a single dossier
        python manage.py process_export_integration_test_manual process-export 0c3cb1cf-414e-4a8d-9c7f-53d91ae70fb2

        # Process multiple dossiers
        python manage.py process_export_integration_test_manual process-export \\
            0c3cb1cf-414e-4a8d-9c7f-53d91ae70fb2 \\
            1d4dc2df-525f-5b9e-ad8f-64ea2bf81fc3
    """
    # Convert string UUIDs to UUID objects
    uuids = [UUID(uuid_str) for uuid_str in dossier_uuids]

    # Get the first dossier to get its account
    if not uuids:
        click.echo("No dossier UUIDs provided")
        return

    first_dossier = Dossier.objects.get(uuid=uuids[0])
    account = first_dossier.account

    results = process_ready_for_export_dossiers(
        account=account,
        dossier_uuids=uuids,
    )

    click.echo(f"Processed {len(uuids)} dossier(s)")
    click.echo(f"Results: {results}")


if __name__ == "__main__":
    cli()

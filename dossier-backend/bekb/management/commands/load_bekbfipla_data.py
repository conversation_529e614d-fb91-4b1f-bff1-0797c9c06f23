import structlog

import djclick as click
from django.contrib.auth import get_user_model
from faker import Faker

from bekb.bekbfipla_factory import (
    BekbFiplaAccountFactory,
    load_bekbfipla_document_categories,
)

# Management commands to set up a) the bekbfiplae account and b) create lots of useful test dossiers.

User = get_user_model()
logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
@click.option("-update_statemgmt", default=True, type=click.BOOL)
def load_account(
    account_key: str,
    update_statemgmt: bool,
):
    """
    Create or update a bekb Fipla account with close to production config. This loads
    document categories and optionally updates the state machine.

    Example:

    python manage.py reset-db
    python manage.py load_bekbfipla_data load-account bekbfiplae

    """
    Faker.seed(234342)
    bfac = BekbFiplaAccountFactory(
        update_statemgmt=update_statemgmt,
        account_key=account_key,
    )
    bfac.account.save()


@grp.command()
@click.argument("account_key")
def update_document_categories(account_key: str):
    """
    Load / update all document categories for BEKB. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_bekbfipla_data update-document-categories bekbfiplae

    @param account_key:
    @return:
    """
    load_bekbfipla_document_categories(account_key)

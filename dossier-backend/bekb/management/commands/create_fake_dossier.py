import structlog

import djclick as click

from bekb.fakes import BekbAccountFactoryFaker

logger = structlog.get_logger()


@click.command()
def faker():
    bekb_account_factory = BekbAccountFactoryFaker()
    for i in range(5):
        bekb_account_factory.create_sample_dossier()
    for i in range(5):
        bekb_account_factory.create_ready_for_export_dossier()

    for i in range(3):
        bekb_account_factory.create_export_archive_available_dossier()

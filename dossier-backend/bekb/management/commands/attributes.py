import structlog

import djclick as click

from bekb import services
from bekb.models import (
    CollateralAssignment,
    CollateralRealEstateProperty,
)

logger = structlog.get_logger()


@click.group()
def grp():
    pass


def assign_property_to_collateral_assignment(
    ca: CollateralAssignment, c: CollateralRealEstateProperty
):
    assert ca.property is None
    ca.property = c.realestate_property
    ca.save()


@grp.command()
@click.argument("account_key")
@click.argument("path", type=click.STRING, default="")
def load_bekb_attributes(account_key: str, path: str = ""):
    """
    Load and update-or-createe attributes from file

    python manage.py attributes load-bekb-attributes bekbe

    """
    num_updates, num_created = services.load_bekb_attributes(account_key, path)

    logger.info(
        f"Created {num_created} attributes. Updated-or-created {num_updates} attributes"
    )

import structlog
from typing import Dict
from uuid import uuid4, UUID

import djclick as click
from django.utils import timezone

import bekb.services
from bekb import services
from bekb.api import show_dossier_ready_for_export
from bekb.bekb_instance_type import is_bekb_fipla_account
from bekb.models import (
    BEKBDossierProperties,
)
from bekb.services import (
    create_index_file,
    SemanticDocumentExportFile,
)
from dossier.helpers_v2 import (
    prepare_semantic_documents_for_api_v2,
)
from dossier.helpers import (
    create_export_filename,
)
from dossier.models import Account, Dossier
from statemgmt.models import Status

logger = structlog.get_logger()


@click.group()
def grp():
    pass


def create_dummy_semantic_document_files(semantic_documents):
    """
    Provide the same datastructure needed for archive index file creation without
    having access to the real files. This works based on the database only (without S3)
    """
    semantic_document_files: Dict[UUID, SemanticDocumentExportFile] = {}
    for semantic_document in semantic_documents:
        uuid = semantic_document["uuid"]
        export_filename = create_export_filename(semantic_document["filename"])
        semantic_document_files[uuid] = SemanticDocumentExportFile(
            filename=export_filename, pages=len(semantic_document["semantic_pages"])
        )
    return semantic_document_files


@grp.command()
@click.argument("account_key")
@click.argument("dossier_uuid")
def show_index_file(account_key: str, dossier_uuid: str):
    """

    Generate the index file for a specific dossier and print it to the console without accessing the files.

    Example:
    python manage.py load_bekbe_account -num_export_archives=1 -num_dossiers_ready_for_export=1 -num_dossiers_sample=1 -add_standard_dossiers=True
    python manage.py archive show-index-file bekbe 7401771e-7434-402b-9410-d29dcface290

    python manage.py archive show-index-file bekbfiplae 7d303a62-cba9-4a6b-830d-5d8cb11d8a4e

    Structure of resulting index file:

        COMMENT: OnDemand Generic Index File Format
        COMMENT: This File is from Hypodossier AG
        COMMENT: 29.02.2024 - 16:17
        COMMENT:
        CODEPAGE:819
        COMMENT:
        GROUP_FIELD_NAME:SCANDATUM
        GROUP_FIELD_VALUE:29.02.2024
        GROUP_FIELD_NAME:FORMULAR
        GROUP_FIELD_VALUE:EKD39
        GROUP_FIELD_NAME:ORDNER
        GROUP_FIELD_VALUE:EKD39
        GROUP_FIELD_NAME:FORMNR
        GROUP_FIELD_VALUE:EKD39
        GROUP_FIELD_NAME:DATUM
        GROUP_FIELD_VALUE:29.02.2024
        GROUP_FIELD_NAME:BATCHID
        GROUP_FIELD_VALUE:********-e081-41ec-a666-1e66d49333af
        GROUP_FIELD_NAME:PARKEY_G
        GROUP_FIELD_VALUE:173382400
        GROUP_FIELD_NAME:PARKEY_P
        GROUP_FIELD_VALUE:408688830
        GROUP_FIELD_NAME:PARKEY_D
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:PARKEY_L
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:PARKEY_DOK
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:KRE_KEY
        GROUP_FIELD_VALUE:825854515
        GROUP_FIELD_NAME:LIE_KEY
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:DEC_KEY
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:PERSCODE
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:KURZFORM
        GROUP_FIELD_VALUE:0
        GROUP_FIELD_NAME:SEITEN
        GROUP_FIELD_VALUE:10
        GROUP_FIELD_NAME:FILENAME
        GROUP_FIELD_VALUE:611-EKD39_Extrait_du_registre_foncier_asdfsuffix.pdf
        GROUP_OFFSET:0
        GROUP_LENGTH:0
        GROUP_FILENAME:611-EKD39_Extrait_du_registre_foncier_asdfsuffix.pdf

    @param account_key: Account key for dossier
    @param dossier_uuid: Dossier for which the index file is created (not the bekb_dossier_uuid)
    @return: Nothing. Prints index file to console
    """
    # account_key="bekbp"
    account: Account = Account.objects.filter(key=account_key).first()
    assert account.key == account_key
    assert account.active_work_status_state_machine

    logger.info(f"account={account}")
    logger.info(f"dossier_uuid={dossier_uuid}")
    logger.info(f"state machine={account.active_work_status_state_machine}")

    d = Dossier.get_by_uuid(uuid=dossier_uuid)

    old_work_status = d.work_status

    if is_bekb_fipla_account(account_key):
        ready_state = services.VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA[0]
    else:
        ready_state = services.VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE[0]

    temp_work_status = Status.objects.get(
        state_machine=account.active_work_status_state_machine,
        key=ready_state,
    )
    d.work_status = temp_work_status
    d.save()

    assert d

    bekb_dossier = BEKBDossierProperties.objects.get(dossier=d)

    assert bekb_dossier

    semantic_documents = prepare_semantic_documents_for_api_v2(
        d,
        show_soft_deleted=False,
        hide_empty_semantic_documents=False,
        date_range=None,
        include_annotations=True,
    )

    semantic_document_files = create_dummy_semantic_document_files(semantic_documents)

    creation = timezone.now()
    batch_id = uuid4()
    index_file = create_index_file(
        batch_id, bekb_dossier, creation, semantic_document_files
    )

    d.work_status = old_work_status
    d.save()

    print(index_file)


@grp.command()
@click.argument("account_key")
def show_ready_for_export(account_key: str):
    """
    python manage.py archive show-ready-for-export bekbp

    @param account:
    @return:
    """

    bdossiers = show_dossier_ready_for_export(None, account_key)
    logger.info("show_dossier_ready_for_export.", num_dossiers=len(bdossiers))
    for bd in bdossiers:
        logger.info(
            "ready_for_export_dossier",
            export_uuid=str(bd.export_uuid),
            dossier_uuid=str(bd.dossier_uuid),
        )


@grp.command()
@click.argument("account_key", type=click.STRING, default="")
def check_work_status_transition_consistency_bekb_archiving(account_key: str):
    """
    python manage.py archive check-work-status-transition-consistency-bekb-archiving bekbp

    @param account_key:
    @return:
    """
    account = Account.objects.get(key=account_key)
    assert account
    dossiers = Dossier._base_manager.filter(account=account)

    for index, d in enumerate(dossiers):
        list_of_transitions, events, inconsistent_event = (
            bekb.services.check_work_status_transition_consistency_bekb_archiving(
                d.uuid
            )
        )
        if inconsistent_event:
            logger.error(
                "Found inconsistent transitions:",
                list_of_transitions=list_of_transitions,
                dossier_uuid=d.uuid,
            )
        if index % 100 == 0:
            logger.info(f"{index}/{len(dossiers)}...")

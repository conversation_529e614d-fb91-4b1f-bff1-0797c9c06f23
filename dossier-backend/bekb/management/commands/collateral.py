import structlog
from typing import List, Set

import djclick as click
from django.utils import timezone

from bekb.collaterals import (
    find_correct_real_estate_properties_for_collateral,
    get_prop_collateral_attribute_by_name,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS,
)
from bekb.models import (
    CollateralAssignment,
    Attribute,
    CollateralRealEstateProperty,
)
from dossier.models import Account

logger = structlog.get_logger()


@click.group()
def grp():
    pass


def assign_property_to_collateral_assignment(
    ca: CollateralAssignment, c: CollateralRealEstateProperty
):
    assert ca.property is None
    ca.property = c.realestate_property
    ca.save()


@grp.command()
@click.argument("account_key")
@click.option("-dry_run", default=True, type=click.BOOL)
@click.option("-max", default=1, type=click.INT)
def fix_missing_property_assignment(account_key: str, dry_run: bool, max: int):
    """
    To see what is going on without changing anything use
    python manage.py collateral fix-missing-property-assignment bekbp

    To change one entry use
    python manage.py collateral fix-missing-property-assignment default -dry_run=False -max=1

    To change all entries with one real estate property use
    python manage.py collateral fix-missing-property-assignment bekbp -dry_run=False -max=99999

    This fixes only entries that have exactly one option. Non expired Dossiers with more than one
    option are logged so they can be fixed manually

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @param max: Number of entries to be updated
    @return: Nothing. Prints out the stats
    """
    account = Account.objects.get(key=account_key)
    print(f"account={account}, dry_run={dry_run}, max={max}")

    collateral_type_prop = get_attribute_collateral_type_property(account)
    count = 0
    count_zero = 0
    count_fixable_one = 0
    count_fixable_morethanone = 0
    count_changed = 0

    count_expired = 0

    # Number of ca that already has a real estate property assigned (do nothing here)
    count_hasprop = 0

    dossiers_zero = set()
    dossiers_fixable_more_than_one = set()

    ca_multiple_options: Set[CollateralAssignment] = set()

    for ca in CollateralAssignment.objects.filter(account=account).order_by(
        "-updated_at"
    ):
        if ca.collateral.collateral_type == collateral_type_prop:
            count += 1

            expired = False
            if ca.semantic_document.dossier.expiry_date < timezone.now():
                count_expired += 1
                expired = True

            assert (
                ca.collateral
            ), f"Could not find collateral of type Grundpfand with collateral assignment = {ca}"

            if ca.property:
                # Do nothing as a property is already assigned
                count_hasprop += 1
                continue

            options = find_correct_real_estate_properties_for_collateral(
                account, ca.collateral
            )
            num_options = len(options)

            if num_options == 0:
                # This is an error case because the real estate property is missing
                # Maybe it will be delivered with a future update
                count_zero += 1
                dossiers_zero.add(ca.semantic_document.dossier)
                logger.error(
                    f"found num_options=={num_options} for collateral={ca.collateral}"
                )
            elif num_options >= 1:
                if num_options == 1:
                    # This is a collateral assignment of a collateral type that requires a property
                    # and exactly one property exists that could fulfil this requirement.
                    # So we can fix the missing requirement by auto-assigning it.
                    count_fixable_one += 1
                    crep = options[0]
                else:
                    if expired:
                        # this is not needed anymore -> do nothing
                        continue

                    # more than one option
                    # There are several options and we need to find the correct one
                    count_fixable_morethanone += 1
                    dossiers_fixable_more_than_one.add(ca.semantic_document.dossier)
                    ca_multiple_options.add(ca)
                    # We decided to take the first of the options which are ordered by property_number

                    for o in options:
                        print(
                            f"Options: {o.property_collateral_type.name_de} -> {o.realestate_property}, property_number={o.realestate_property.property_number}, dossier={ca.semantic_document.dossier.uuid}"
                        )

                    print(
                        f"Found num_options={num_options}. Take first from options={options}"
                    )
                    crep = None  # Do not fix anything if multiple options

                if dry_run:
                    # do nothing
                    continue
                else:
                    if count_fixable_one < max + 1:
                        if crep:
                            count_changed += 1

                            assign_property_to_collateral_assignment(ca, crep)
                            print(
                                f"#{count_changed} Assign real estate property number {crep.realestate_property.property_number} to the collateral assignment {ca.uuid} with collateral number {ca.collateral.collateral_number}"
                            )
                    else:
                        logger.warning(
                            f"Abort update after count_fixable_one={count_fixable_one}, count_changed={count_changed} because max={max}"
                        )
                        break
            else:
                raise Exception("Must not happen")

    print(f"Found {len(dossiers_zero)} dossiers with 0 valid options")
    for d in dossiers_zero:
        print(f"  {d.name}, expiry={d.expiry_date}")

    print(
        f"count={count}, count_fixable_one={count_fixable_one}, count_zero={count_zero}, count_fixable_morethanone={count_fixable_morethanone}, count_changed={count_changed}, count_hasprop={count_hasprop}, dossiers_morethanone={len(dossiers_fixable_more_than_one)}, count_expired={count_expired}"
    )
    print(f"ca_multiple_options={len(ca_multiple_options)}")
    dossier_uuids = set()
    for ca in list(ca_multiple_options):
        dossier_uuids.add(ca.semantic_document.dossier.uuid)

    for u in dossier_uuids:
        print(f"multiple options in non-expired dossier, therefore do nothing: {u}")


def get_attribute_collateral_type_property(account):
    key_property = "1"
    collateral_type_prop = Attribute.objects.get(
        entity=Attribute.Entity.CollateralType.value,
        key=key_property,
        account=account,
    )
    return collateral_type_prop


@grp.command()
@click.argument("account_key")
def find_collateral_assignments_with_nebendeckung(account_key: str):
    """
    This should never happen but was possible until 231107 and happened twice in production.
    Nebendeckung should never be used in a collateral assignment

    Example: python manage.py collateral find-collateral-assignments-with-nebendeckung default

    Changes nothing, only prints out the wrong assignments

    @param account_key:
    @return:
    """
    account = Account.objects.get(key=account_key)

    attr_ver = get_prop_collateral_attribute_by_name(
        account, COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS
    )
    attr_haupt = get_prop_collateral_attribute_by_name(
        account, COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT
    )
    get_prop_collateral_attribute_by_name(
        account, COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN
    )

    collateral_type_prop = get_attribute_collateral_type_property(account)

    count_total = 0
    count_wrong = 0
    for ca in CollateralAssignment.objects.filter(account=account).order_by(
        "-updated_at"
    ):
        count_total += 1
        if count_total % 500 == 0:
            print(f"count_total={count_total}...")
        if ca.collateral.collateral_type == collateral_type_prop:
            creps: List[CollateralRealEstateProperty] = list(
                CollateralRealEstateProperty.objects.filter(
                    collateral=ca.collateral
                ).all()
            )
            has_ver = False
            has_haupt = False
            for crep in creps:
                if crep.property_collateral_type == attr_ver:
                    has_ver = True
                    break
            for crep in creps:
                if crep.property_collateral_type == attr_haupt:
                    has_haupt = True
                    break
            if not (has_ver or has_haupt):
                # All collaterals are of type neben, this must not be!
                # This should not happen. Here a collateral of type Nebendeckung is assigned.
                count_wrong += 1
                print(
                    f"wrong assignment: {ca} with collateral {ca.collateral.collateral_number} in dossier {ca.semantic_document.dossier.name}"
                )

    print(f"Found count_total={count_total}, count_wrong={count_wrong}")

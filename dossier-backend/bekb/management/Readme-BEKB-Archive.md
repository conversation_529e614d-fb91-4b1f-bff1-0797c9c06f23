Commands that are useful to check the BEKB archiving process:

# Show all dossiers in state EXPORT_ARCHIVE_AVAILABLE
python manage.py archive show-ready-for-export bekbp

# Mirror files for a dossier with dossier_uuid X from exoscale to perform a successful archiving: 
mcli mirror --newer-than "30d10h30m"  exo/production-v2-bekb-dms/14f9faa8-e149-4c22-9315-3a66b984855a local/production-v2-bekb-dms/14f9faa8-e149-4c22-9315-3a66b984855a

# Process a dossier that is READY_FOR_ARCHIVING_XXX to transition to EXPORT_ARCHIVE_AVAILABLE
python manage.py process_ready_for_export_dossier now bekbp 14f9faa8-e149-4c22-9315-3a66b984855a


# Show work status transitions of a dossier: 
python manage.py dossier show-work-status-transitions 14f9faa8-e149-4c22-9315-3a66b984855a
python manage.py dossier show-work-status-transitions  4559a48c-da15-4175-845b-768492e870a5

# Check if all transitions are consistent GENERAL VERSION
python manage.py dossier check-work-status-consistency bekbp

# Check if all transitions are consistent BEKB VERSION with additional check
python manage.py archive check-work-status-transition-consistency-bekb-archiving bekbp


# Old way of doing it before December 2024 in production with modifications going on
This ticket explains all commands to create BEKB document catalog: https://gitlab.com/hypodossier/hyextract/-/issues/121
-> Section "Commands for BEKB" 

python manage.py bekbconf update-240718-add-bill-property bekbe

python manage.py update_dossier_categories bekbe assets/document_category/default/DocumentCategory-2024-07-09.json -update_id=False -update_exclude_for_recommendation=False

python manage.py bekbconf update-ekd-ids-and-external-titles bekbe -dry_run=False

python manage.py bekbconf check-ekd-ids-and-external-titles bekbe
python manage.py bekbconf check-bekb-config bekbe



# New way of doing it in 2025

# Optional if account does not exist
python manage.py load_bekb_data load-account bekbe
# Load basic categories
#python manage.py update_dossier_categories bekbe assets/document_category/default/DocumentCategory-2025-03-10.json -update_id=False -update_exclude_for_recommendation=False
# Load custom BEKB categories
python manage.py update_dossier_categories bekbe assets/document_category/bekb/DocumentCategory-2025-01-03bekb.json -update_id=False -update_exclude_for_recommendation=False
# Update ekds and ids
python manage.py bekbconf update-ekd-ids-and-external-titles bekbe -dry_run=False


# Make sure everything is consistent
python manage.py bekbconf check-ekd-ids-and-external-titles bekbe
python manage.py bekbconf check-bekb-config bekbe

# Now export from prod without UUID, Account, and compare to local
import datetime
import structlog
from enum import Enum
from typing import List, Optional, Dict, Set
from uuid import UUID

from pydantic import StringConstraints, BaseModel

from bekb.bekb_instance_type import (
    BekbInstanceType,
    get_bekb_instance_type,
    has_collaterals,
)
from bekb.bekbload import (
    load_document_category2ekd_mappings,
    load_bekb_external_document_categories,
)
from bekb.schemas import schemas as schemasBekb
from bekb.models import (
    CollateralAssignment,
    Collateral,
    Attribute,
    Partner,
    BusinessCase,
    CollateralRealEstateProperty,
)
from dossier.models import Account

from semantic_document.models import SemanticDocument
from typing_extensions import Annotated

logger = structlog.get_logger()

# It has been decided by <PERSON> (Nov 2023 by phone to MT) that Nebendeckung is to be ignored
# They could even be deleted from the system, currently not used.
DO_USE_NEBENDECKUNG_IN_COLLATERAL_ASSIGNMENT = False

COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS = "Verwaltungsgeschäft"
COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT = "Hauptdeckung"
COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN = "Nebendeckung"

COLLATERAL_TYPE_KEY_GRUNDPFAND = "1"
COLLATERAL_TYPE_KEY_PERSONENVERSICHERUNG = "2"


def prepare_collateral(collateral: Collateral):
    collateral_assignments = CollateralAssignment.objects.filter(collateral=collateral)

    real_estate_property = None

    semantic_documents = []

    if len(collateral_assignments) > 0:
        # This collateral has been assigned to a list of semantic documents
        semantic_documents = [
            collateral_assignment.semantic_document.uuid
            for collateral_assignment in collateral_assignments
        ]

        first_assignment = collateral_assignments[0]
        if first_assignment.property is not None:
            real_estate_property = map_real_estate_property(first_assignment.property)
    else:
        # This collateral has not been assigned to any semantic document
        # It still has a relation to 0..n real estate properties if it is a grundpfand
        if collateral.collateral_type.key == COLLATERAL_TYPE_KEY_GRUNDPFAND:
            # attr_neben = get_prop_collateral_attribute_by_name(
            #     collateral.account, COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN
            # )
            creps: List[CollateralRealEstateProperty] = list(
                CollateralRealEstateProperty.objects.filter(collateral=collateral)
                .exclude(
                    property_collateral_type__key=COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN
                )
                .all()
            )
            if len(creps) > 0:
                real_estate_property = map_real_estate_property(
                    creps[0].realestate_property
                )

    return CollateralWithAssignments(
        **{
            "collateral_number": collateral.collateral_number,
            "collateral_type": schemasBekb.Attribute(
                entity=collateral.collateral_type.entity,
                key=collateral.collateral_type.key,
                name_de=collateral.collateral_type.name_de,
                name_fr=collateral.collateral_type.name_fr,
            ),
            "description": collateral.description,
            "business_partner": schemasBekb.Partner(
                parkey=collateral.business_partner.parkey,
                name=collateral.business_partner.name,
                firstname=collateral.business_partner.firstname,
            ),
            "policy_number": collateral.policy_number,
            "updated_at": collateral.updated_at,
            "collateral_status": schemasBekb.Attribute(
                entity=collateral.collateral_status.entity,
                key=collateral.collateral_status.key,
                name_de=collateral.collateral_status.name_de,
                name_fr=collateral.collateral_status.name_fr,
            ),
            "semantic_documents": semantic_documents,
            "real_estate_property": real_estate_property,
        }
    )


def map_real_estate_property(property):
    return RealEstatePropertyFull(
        **{
            "property_partner": schemasBekb.Partner(
                parkey=property.property_partner.parkey,
                name=property.property_partner.name,
                firstname=property.property_partner.firstname,
            ),
            "property_number": property.property_number,
            "property_type": schemasBekb.Attribute(
                entity=property.property_type.entity,
                key=property.property_type.key,
                name_de=property.property_type.name_de,
                name_fr=property.property_type.name_fr,
            ),
            "address_street": (
                property.address_street if property.address_street is not None else ""
            ),
            "address_street_nr": (
                property.address_street_nr
                if property.address_street_nr is not None
                else ""
            ),
            "address_zip": property.address_zip,
            "address_city": property.address_city,
            "land_register_municipality": (
                property.land_register_municipality
                if property.land_register_municipality is not None
                else ""
            ),
            "land_register_id": (
                property.land_register_id
                if property.land_register_id is not None
                else ""
            ),
            "status": property.status,
        }
    )


class RealEstatePropertyFull(BaseModel):
    property_partner: schemasBekb.Partner
    property_number: schemasBekb.PropertyNumber

    property_type: schemasBekb.Attribute

    address_street: Annotated[str, StringConstraints(max_length=255)]
    address_street_nr: Annotated[str, StringConstraints(max_length=20)]
    address_zip: Annotated[str, StringConstraints(max_length=30)]
    address_city: Annotated[str, StringConstraints(max_length=255)]

    land_register_municipality: Annotated[str, StringConstraints(max_length=255)]
    land_register_id: Annotated[str, StringConstraints(max_length=100)]


class CollateralWithAssignments(BaseModel):
    collateral_number: schemasBekb.CollateralNumber
    collateral_type: schemasBekb.Attribute
    description: schemasBekb.CollateralDescription
    business_partner: schemasBekb.Partner
    policy_number: schemasBekb.CollateralPolicyNumber
    updated_at: datetime.datetime
    collateral_status: schemasBekb.Attribute
    semantic_documents: List[UUID]
    real_estate_property: Optional[RealEstatePropertyFull] = None


def get_prop_collateral_attribute_by_name(account: Account, name_de: str):
    """
    Get the attribute that indicates the type of collateral, used on CollateralRealEstateProperty
    Valid entries for name_de are "Verwaltungsgeschäft", "Hauptdeckung", "Nebendeckung". The last is not to be used
    for collateral assignments.
    @param account:
    @param name_de:
    @return:
    """
    return Attribute.objects.get(
        name_de=name_de,
        account=account,
        # key="MAIN",
        entity=Attribute.Entity.PropertyCollateralType.value,
    )


def find_correct_real_estate_properties_for_collateral(
    account: Account, collateral: Collateral
) -> List[CollateralRealEstateProperty]:
    """
    - A collateral cannot be created without a real estate property of type "Verwaltungsgeschäft" or "Hauptdeckung"
    - A collateral can have 0..n real estate properties of type "Nebendeckung" but they are not relevant to us (ignore)
    - "Verwaltungsgeschäft" should not appear combined with "Hauptdeckung" / "Nebendeckung". They are rare (2% of cases)
    - In rare cases (5%) there are 2 or more real estate properties of type "Hauptdeckung"
      (e.g. appartment + garage but on separate Schuldbriefe). In this case take the first entry of type "Hauptdeckung"
      ordered by ascending property_number (because we assume that the real estate property created first is the more
      important). This business decision has been defined by Tinu in Call with Manuel 231102

    @return: We return the CollateralRealEstateProperty and not the RealEstateProperty itself so we keep the
        CollateralRealEstateProperty.property_collateral_type information (Hauptdeckung/Nebendeckung)
    """

    attribute_hauptdeckung = get_prop_collateral_attribute_by_name(
        account, COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT
    )
    assert attribute_hauptdeckung
    attr_verwaltung = get_prop_collateral_attribute_by_name(
        account, COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS
    )
    assert attr_verwaltung

    options: List[CollateralRealEstateProperty] = list(
        CollateralRealEstateProperty.objects.filter(
            account=account,
            collateral=collateral,
            property_collateral_type__in=[attribute_hauptdeckung, attr_verwaltung],
        )
        .order_by("realestate_property__property_number")
        .all()
    )
    return options


def get_collaterals_by_businesscase(
    account: Account, business_partner: Partner, business_case: BusinessCase
) -> Set[Collateral]:
    if DO_USE_NEBENDECKUNG_IN_COLLATERAL_ASSIGNMENT:
        # Legacy implementation
        all_collaterals: Set[Collateral] = set(
            Collateral.objects.filter(
                account=account,
                business_partner=business_partner,
                businesscase=business_case,
            ).all()
        )
    else:
        # New implementation since November 2023

        # Step 0: return empty set if instance type FIPLA
        account_key = account.key
        instance_type = get_bekb_instance_type(account_key)
        if not has_collaterals(instance_type):
            all_collaterals: Set[Collateral] = set()
            return all_collaterals

        # Step 1: take all non-grundpfand collaterals
        all_collaterals: Set[Collateral] = set(
            Collateral.objects.filter(
                account=account,
                business_partner=business_partner,
                businesscase=business_case,
            )
            .exclude(collateral_type__key=COLLATERAL_TYPE_KEY_GRUNDPFAND)
            .all()
        )

        # Step 2: add the collaterals of type grundpfand which are not "neben"
        attr_neben = get_prop_collateral_attribute_by_name(
            account, COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN
        )
        creps = (
            CollateralRealEstateProperty.objects.filter(
                collateral__account=account,
                collateral__business_partner=business_partner,
                collateral__businesscase=business_case,
            ).exclude(property_collateral_type=attr_neben)
        ).all()
        collaterals = set()
        for crep in creps:
            collaterals.add(crep.collateral)
        all_collaterals.update(collaterals)

    return all_collaterals


def collateral_assignment_status(
    all_collaterals_valid: List[Collateral],
    semantic_document: SemanticDocument,
    requirements_satisfaction_options,
):
    collateral_assignment = CollateralAssignment.objects.filter(
        semantic_document=semantic_document
    ).first()

    document_category = semantic_document.document_category

    valid_collateral_types_for_a_semantic_doc = (
        requirements_satisfaction_options[document_category.name]
        if document_category.name in requirements_satisfaction_options
        else []
    )

    collaterals_for_assignment = []
    for collateral_type in valid_collateral_types_for_a_semantic_doc:
        for collateral in all_collaterals_valid:
            if collateral_type["uuid"] == collateral.collateral_type.uuid:
                collaterals_for_assignment.append(prepare_collateral(collateral))

    # Status INVALID_FOR_EXPORT
    if document_category.name not in requirements_satisfaction_options:
        semantic_document_status = {
            "semantic_document_uuid": semantic_document.uuid,
            "status": CollateralAssigmentStatus.Status.INVALID_FOR_EXPORT,
            "collateral_assignment": None,
            "collaterals": [],
        }

    # Status COLLATERAL_MAPPED or COLLATERAL_MISSING
    elif len(valid_collateral_types_for_a_semantic_doc) > 0:
        semantic_document_status = {
            "semantic_document_uuid": semantic_document.uuid,
            "status": (
                CollateralAssigmentStatus.Status.COLLATERAL_MAPPED
                if collateral_assignment is not None
                else CollateralAssigmentStatus.Status.COLLATERAL_MISSING
            ),
            "collateral_assignment": (
                prepare_collateral(collateral_assignment.collateral)
                if collateral_assignment
                else None
            ),
            "collaterals": collaterals_for_assignment,
        }
    # Status NO_COLLATERAL_REQUIRED
    else:
        semantic_document_status = {
            "semantic_document_uuid": semantic_document.uuid,
            "status": CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            "collateral_assignment": None,
            "collaterals": [],
        }
    return CollateralAssigmentStatus(**semantic_document_status)


class CollateralAssigmentStatus(BaseModel):
    class Status(str, Enum):
        INVALID_FOR_EXPORT = "INVALID_FOR_EXPORT"
        COLLATERAL_MISSING = "COLLATERAL_MISSING"
        COLLATERAL_MAPPED = "COLLATERAL_MAPPED"
        NO_COLLATERAL_REQUIRED = "NO_COLLATERAL_REQUIRED"

    semantic_document_uuid: UUID
    status: Status
    collateral_assignment: Optional[CollateralWithAssignments] = None
    collaterals: List[CollateralWithAssignments]


def get_collateral_requirements_satisfaction_options_for_account(account):
    attributes = Attribute.objects.filter(
        entity=Attribute.Entity.CollateralType.value, account=account
    ).all()
    collateral_attributes = {
        type.key: {
            "uuid": type.uuid,
            "entity": type.entity,
            "key": type.key,
            "name_de": type.name_de,
            "name_fr": type.name_fr,
        }
        for type in attributes
    }
    requirements_satisfaction_options = get_collateral_requirement_satisfaction_options(
        collateral_attributes
    )
    return requirements_satisfaction_options


def get_collateral_requirement_satisfaction_options(
    collateral_types, add_empty_mappings: bool = True
) -> Dict[str, Attribute]:
    """
    collateral_types is a dict collateral.id -> Attribute of type Attribute.Entity.CollateralType
    """

    """
        data maps which ekd numbers require which collateral types

        collateral_type: 'list of collateral.id' (comma separated)
        docid_external: e.g. 'EKD10' so the external part of the document id
        de_external: German external name
        en_external, fr_external, it_external: ...
        sort_external: key to sort these values
    """

    data = load_bekb_external_document_categories()

    # ekd_requirements is a dict 'EKDxy' -> List[Attribute of type Attribute.Entity.CollateralType]
    ekd_requirements = {}
    for entry in data:
        options = []
        for key in entry["collateral_type"].split(","):
            if len(key) > 0:
                if key in collateral_types:
                    options.append(collateral_types[key])
                else:
                    logger.warning(
                        "could not find collateral type with key", key=key, entry=entry
                    )
        ekd_requirements[entry["docid_external"]] = options

    document_categories_mapping = load_document_category2ekd_mappings(
        BekbInstanceType.MORTGAGE
    )

    # requirements satisfaction_options is a dict
    #   document_cat.name -> List[Attribute of type Attribute.Entity.CollateralType]
    requirements_satisfaction_options: dict[str, Attribute] = {}
    for document_category_key, ekd_number in document_categories_mapping.items():
        requirements = ekd_requirements[ekd_number]
        if add_empty_mappings or len(requirements) > 0:
            requirements_satisfaction_options[document_category_key] = requirements

    d = dict(sorted(requirements_satisfaction_options.items()))
    return d

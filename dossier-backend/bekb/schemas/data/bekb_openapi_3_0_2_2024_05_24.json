{"openapi": "3.0.2", "info": {"title": "Hypodossier - BEKB API", "version": "0.7.0", "description": ""}, "paths": {"/partner/bekb/api/0.7/dossier": {"get": {"operationId": "bekb_api_show_dossier", "summary": "Show Dossier", "parameters": [{"in": "query", "name": "encoded_jwt", "schema": {"title": "Encoded Jwt", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"title": "Response", "minLength": 1, "maxLength": 2083, "format": "uri", "type": "string"}}}}}, "description": "Shows all the dossiers associated with the corresponding businesscase_parkey\nand creates a redirect to the bekb_dossier list view.\n\nfor the encoding of the encoded_jwt see @create_dossier\n\nArgs:\nrequest (HttpRequest): The HTTP request from the client.\nencoded_jwt (str): The encoded JWT.\n\nReturns:\n    HttpResponse: The HTTP response."}}, "/partner/bekb/api/0.7/extra_types": {"get": {"operationId": "bekb_api_extra_types", "summary": "Extra Types", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "stub so the types are in the openapi scshema", "requestBody": {"content": {"application/json": {"schema": {"title": "Extra Types", "anyOf": [{"$ref": "#/components/schemas/DossierCreateJWT"}, {"$ref": "#/components/schemas/DossierShowJWT"}]}}}, "required": true}}}, "/partner/bekb/api/0.7/dossier/create": {"get": {"operationId": "bekb_api_create_dossier", "summary": "Create Dossier", "parameters": [{"in": "query", "name": "encoded_jwt", "schema": {"title": "Encoded Jwt", "type": "string"}, "required": true}], "responses": {"302": {"description": "Found", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}}, "description": "Creates a new Dossier based on the provided parameters\n\nThis endpoint is called via URL integration in the DBP (Digitales Berater Portal - Create a new Dossier).\nAfter the creation of the dossier, the user is redirected to the detail view of the newly created dossier.\n\nencoded_jwt is a jwt which contains the following parameters:\n* ***expiration*** defines the expiration time of the token datetime.utcnow().replace(tzinfo=timezone.utc) + timedelta(minutes=60)\n* ***businesscase_parkey*** corresponds to the business case to which the dossier is associated.\n* ***pers*** sets the dossier property to pers, which indicates that only users with pers attributes are allow to open the dossier\n* ***fico*** sets the username (email address) of the responsible financial coach of the dossier\n\n* ***current_user*** is the username of the person trying to create the dossier.\n    if the pers attribute is set, it is assumed that the current_user has the permission to view dossiers with the pers attribut of the dossier set to true\n\n```python\nimport jwt\nshared_secret = \"a secret shared between bekb and hypodossier\"\nparameters = {'exp': expiration, 'businesscase_parkey': '1234566', 'pers': True, \"team\": \"team1\", \"fico\": \"<EMAIL>\", \"current_user\": \"<EMAIL>\" }\nencoded_jwt = jwt.encode(parameters, shared_secret, algorithm='HS256')\n```"}}, "/partner/bekb/api/0.7/dossier/properties": {"put": {"operationId": "bekb_api_update_dossier_properties", "summary": "Update Dossier Properties", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Updates the properties of a Dossier.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the Dossier belongs.\n    updates (List[DossierProperties]): The list of Dossier properties to update.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"title": "Updates", "type": "array", "items": {"$ref": "#/components/schemas/DossierProperties"}}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/users": {"get": {"operationId": "bekb_api_list_hypodossier_users", "summary": "List Hypodossier Users", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}, "description": "Lists all HypoDossier users for a specific account.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account.\n\nReturns:\n    List[schemas.User]: The list of HypoDossier users for the specified account.", "security": [{"BEKBJWTAuth": []}]}, "put": {"operationId": "bekb_api_update_user_attributes", "summary": "Update User Attributes", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"title": "Response", "type": "string"}}}}}, "description": "Updates the attributes of a user.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the user belongs.\n    updates (List[schemas.UserAttributes]): The list of user attributes to update.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"title": "Updates", "type": "array", "items": {"$ref": "#/components/schemas/UserAttributes"}}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/business-parkeys": {"get": {"operationId": "bekb_api_list_business_parkeys", "summary": "List Business Parkeys", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"type": "string", "maxLength": 20}}}}}}, "description": "Returns a list of all business parkeys (Geschäftsfall Parkey) actively used at HypoDossier.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account.\n\nReturns:\n    List[Parkey]: The list of business parkeys.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/business-partners": {"put": {"operationId": "bekb_api_update_business_partners", "summary": "Update Business Partners", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates the information of business partners.\n\nArgs:\n    request (HttpRequest): The HTTP request from the client.\n    account_name (AccountName): The name of the account where the business partners belong.\n    updates (schemas.BusinessPartnerUpdates): The updates to be applied to the business partners.\n\nReturns:\n    int: HTTP status code indicating the result of the operation.\n    str: Empty string.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessPartnerUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/attributes": {"put": {"operationId": "bekb_api_create_or_update_attribute", "summary": "Create Or Update Attribute", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Creates or updates an attribute in the database.\nThe key of the attribute consists of the entity and the key\nof the attribute.\n\nTODO: löscht BEKB Attribute oder werden die einfach nicht mehr referenziert?\nen: does BEKB delete attributes or are they simply no longer referenced?\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    attribute_updates: Updates to the attributes.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttributeUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/businesscases": {"put": {"operationId": "bekb_api_update_list_of_businesscase", "summary": "Update List Of Businesscase", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates a list of business cases in the database.\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    updates: Updates to the business cases.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BusinessCaseUpdates"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/realestateproperties": {"put": {"operationId": "bekb_api_update_realestate_properties", "summary": "Update Realestate Properties", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Updates real estate properties in the database. The 'property_number' is assumed to be the\nunique key for each property.\n\nArgs:\n    request: HTTP request object.\n    account_name: Account name.\n    real_estate_properties: Real estate properties to be updated.\n\nReturns:\n    HTTP status code: 204 for successful update, 401 for unauthorized access.\n\nTODO:\n    Haben die Liegenschaften einen Status (falls die gelöscht/deaktiviert werden?) oder benötigen\n    wir ein Delete API Hypodossier geht davon aus, dass die property_number ein unique key ist\n    für das property\n\n    En: Do the properties have a status (in case they are deleted/deactivated?) or do we need a\n    Delete API Hypodossier assumes that the property_number is a unique key for the property", "requestBody": {"content": {"application/json": {"schema": {"title": "Real Estate Properties", "type": "array", "items": {"$ref": "#/components/schemas/RealEstateProperty"}}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/businesscases/collateral": {"put": {"operationId": "bekb_api_update_collateral_for_business_key", "summary": "Update Collateral For Business Key", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Update the collateral information for a business case (Geschäftsfall Parkey).\n\nDesigned to be used in batches\n\nAdditional descriptive information might be required and has to be decided by bekb\n\nArgs:\n    request: The incoming request.\n    account_name: The name of the account to update.\n    collaterals: The list of collateral data to update.\n\nReturns:\n    HTTP status code 204 and an empty string.", "requestBody": {"content": {"application/json": {"schema": {"title": "Collaterals", "type": "array", "items": {"$ref": "#/components/schemas/Collateral"}}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/dossier-ready-for-export": {"get": {"operationId": "bekb_api_show_dossier_ready_for_export", "summary": "Show Dossier Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/DossierExport"}}}}}}, "description": "Returns a list of dossier exports that are ready for export.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account whose exports are to be retrieved.\n\nReturns:\n    List[schemas.DossierExport]: A list of exports for the specified account.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/file": {"get": {"operationId": "bekb_api_download_file", "summary": "Download File", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}, {"in": "query", "name": "export_uuid", "schema": {"title": "Export U<PERSON>", "type": "string", "format": "uuid"}, "required": true}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "string", "format": "binary"}}}}}, "description": "Downloads a Dossier associated with a given account and export UUID.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    export_uuid: The unique identifier of the export.\n\nReturns:\n    StreamingHttpResponse: The file to be downloaded.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/export/feedback": {"post": {"operationId": "bekb_api_add_dossier_export_feedback", "summary": "Add Dossier Export <PERSON>", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "204": {"description": "No Content"}}, "description": "Adds feedback for a given export.\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    feedbacks: A list of DossierExportFeedback schema objects.\n\nReturns:\n    int, str: 204 status code and an empty string indicating successful operation.\n    HTTP 204 if update has been done successfully. HTTP 401 if not allowed.", "requestBody": {"content": {"application/json": {"schema": {"title": "Feedbacks", "type": "array", "items": {"$ref": "#/components/schemas/DossierExportFeedback"}}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/test/dossier/create/ready-for-export": {"post": {"operationId": "bekb_api_add_some_sample_dossier_ready_for_export", "summary": "Add Some Sample Dossier Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}, {"in": "query", "name": "count", "schema": {"title": "Count", "default": 1, "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK"}}, "description": "Create up to count (max 10, default 1) fake dossier in state ready for export\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    count: The number of fake dossiers to be created (default is 1).\n\nReturns:\n    int, Message: In case of a production account, returns a 403 status code and an error message.", "security": [{"BEKBJWTAuth": []}]}}, "/partner/bekb/api/0.7/test/dossier/process_ready_for_export": {"post": {"operationId": "bekb_api_process_ready_for_export", "summary": "Process Ready For Export", "parameters": [{"in": "query", "name": "account_name", "schema": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "required": true, "description": "An enumeration."}], "responses": {"403": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/Result_UUID_"}}}}}}, "description": "Checks whether a dossier is ready for export\n\nArgs:\n    request: The incoming request object.\n    account_name: The name of the account.\n    processing_request: A ProcessReadyForExportRequest schema object containing dossier UUIDs\n                         and 'not_updated_since' parameter.\n\nReturns:\n    int, function: In case of a production account, returns a 403 status code and an error message.\n    Otherwise, processes the dossiers and returns a 201 status code.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessReadyForExportRequest"}}}, "required": true}, "security": [{"BEKBJWTAuth": []}]}}}, "components": {"schemas": {"AccountName": {"title": "Account<PERSON><PERSON>", "description": "An enumeration.", "enum": ["bekbe", "bekbs", "bekbu", "bekbz", "bekbp"], "type": "string"}, "BusinessPartner": {"title": "BusinessPartner", "type": "object", "properties": {"parkey": {"title": "<PERSON><PERSON>", "maxLength": 20, "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "firstname": {"title": "Firstname", "maxLength": 255, "type": "string"}, "pers": {"title": "Pers", "type": "boolean"}}, "required": ["parkey", "name", "pers"]}, "Partner": {"title": "Partner", "type": "object", "properties": {"parkey": {"title": "<PERSON><PERSON>", "maxLength": 20, "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "firstname": {"title": "Firstname", "maxLength": 255, "type": "string"}}, "required": ["parkey", "name"]}, "Langugage": {"title": "Langugage", "description": "An enumeration.", "enum": ["de", "fr"], "type": "string"}, "User": {"title": "User", "type": "object", "properties": {"username": {"title": "Username", "description": "should be the email address as username", "maxLength": 255, "type": "string"}, "firstname": {"title": "Firstname", "maxLength": 255, "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "pers": {"title": "Pers", "default": false, "type": "boolean"}, "active": {"title": "Active", "default": true, "type": "boolean"}}, "required": ["username", "name"]}, "DossierCreateJWT": {"title": "DossierCreateJWT", "type": "object", "properties": {"exp": {"title": "Exp", "description": "Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular.", "type": "integer"}, "account_name": {"$ref": "#/components/schemas/AccountName"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "partner_partner": {"$ref": "#/components/schemas/Partner"}, "dossier_name": {"title": "Dossier Name", "maxLength": 255, "type": "string"}, "language": {"$ref": "#/components/schemas/Langugage"}, "fico": {"title": "Fico", "description": "The responsible FICO for the dossier", "allOf": [{"$ref": "#/components/schemas/User"}]}, "current_user": {"title": "Current User", "description": "The current user (from the DBP). Will be the owner of the dossier during creation.", "allOf": [{"$ref": "#/components/schemas/User"}]}}, "required": ["exp", "account_name", "business_partner", "dossier_name", "language", "fico", "current_user"]}, "DossierShowJWT": {"title": "DossierShowJWT", "type": "object", "properties": {"exp": {"title": "Exp", "description": "Number of seconds from 1970-01-01T00:00:00Z UTC until the specified UTC date/time, ignoring leap seconds. This is equivalent to the IEEE Std 1003.1, 2013 Edition [POSIX.1] definition 'Seconds Since the Epoch', in which each day is accounted for by exactly 86400 seconds, other than that non-integer values can be represented. See RFC 3339 [RFC3339] for details regarding date/times in general and UTC in particular.", "type": "integer"}, "account_name": {"$ref": "#/components/schemas/AccountName"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "partner_partner": {"$ref": "#/components/schemas/Partner"}, "language": {"$ref": "#/components/schemas/Langugage"}, "fico": {"title": "Fico", "description": "The responsible FICO for the dossier", "allOf": [{"$ref": "#/components/schemas/User"}]}, "current_user": {"title": "Current User", "description": "The current user (from the DBP). Will be the owner of the dossier.", "allOf": [{"$ref": "#/components/schemas/User"}]}}, "required": ["exp", "account_name", "business_partner", "language", "fico", "current_user"]}, "Message": {"title": "Message", "type": "object", "properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"]}, "DossierProperties": {"title": "DossierProperties", "type": "object", "properties": {"business_parkey": {"title": "Business Parkey", "maxLength": 20, "type": "string"}, "fico": {"title": "Fico", "description": "the responsible financial coach", "allOf": [{"$ref": "#/components/schemas/User"}]}}, "required": ["business_parkey"]}, "UserAttributes": {"title": "UserAttributes", "type": "object", "properties": {"username": {"title": "Username", "description": "The username (should be an email address) of the user to update", "maxLength": 255, "type": "string"}, "firstname": {"title": "Firstname", "maxLength": 255, "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "pers": {"title": "Pers", "description": "set to true if the user has access to pers dossiers", "type": "boolean"}, "active": {"title": "Active", "description": "Is the user active (True) or inactive (False)", "type": "boolean"}}, "required": ["username"]}, "BusinessPartnerUpdate": {"title": "BusinessPartnerUpdate", "type": "object", "properties": {"business_parkey": {"title": "Business Parkey", "maxLength": 20, "type": "string"}, "business_partner": {"$ref": "#/components/schemas/BusinessPartner"}, "partner_partner": {"$ref": "#/components/schemas/Partner"}}, "required": ["business_parkey"]}, "BusinessPartnerUpdates": {"title": "BusinessPartnerUpdates", "default": [], "type": "array", "items": {"$ref": "#/components/schemas/BusinessPartnerUpdate"}}, "Entity": {"title": "Entity", "description": "An enumeration.", "enum": ["BusinessStatus", "BusinessType", "CollateralType", "CollateralStatus", "PropertyType", "PropertyCollateralType", "RealEstatePropertyStatus"], "type": "string"}, "Attribute": {"title": "Attribute", "type": "object", "properties": {"entity": {"$ref": "#/components/schemas/Entity"}, "key": {"title": "Key", "maxLength": 40, "type": "string"}, "name_de": {"title": "Name De", "maxLength": 255, "type": "string"}, "name_fr": {"title": "Name Fr", "maxLength": 255, "type": "string"}}, "required": ["entity", "key", "name_de", "name_fr"]}, "AttributeUpdates": {"title": "AttributeUpdates", "type": "array", "items": {"$ref": "#/components/schemas/Attribute"}}, "BusinessCase": {"title": "BusinessCase", "description": "Geschäftsfall", "type": "object", "properties": {"business_parkey": {"title": "Business Parkey", "maxLength": 20, "type": "string"}, "business_number": {"title": "Business Number", "maxLength": 20, "type": "string"}, "business_type": {"title": "Business Type", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Immobilienfinanzierung, ...)", "maxLength": 40, "type": "string"}, "business_status": {"title": "Business Status", "description": "Geschäftsstatus (pendent, definitiv, ab<PERSON><PERSON><PERSON>, aufgehoben, auszahlungsgeprüft, nicht zustande gekommen", "maxLength": 40, "type": "string"}, "mutation_date": {"title": "Mutation Date", "type": "string", "format": "date"}, "mutation_user": {"title": "Mutation User", "maxLength": 50, "type": "string"}}, "required": ["business_parkey", "business_number", "business_type", "business_status", "mutation_date", "mutation_user"]}, "BusinessCaseUpdates": {"title": "BusinessCaseUpdates", "type": "array", "items": {"$ref": "#/components/schemas/BusinessCase"}}, "RealEstateProperty": {"title": "RealEstateProperty", "type": "object", "properties": {"property_partner": {"title": "Property Partner", "description": "Partner der Liegenschaft", "allOf": [{"$ref": "#/components/schemas/Partner"}]}, "property_number": {"title": "Property Number", "description": "Liegenschaftsnummer", "maxLength": 20, "type": "string"}, "property_type": {"title": "Property Type", "description": "Liegenschaftsobjektart (e.g. Eigentumswohnung, Garage/Abstellplatz)", "maxLength": 40, "type": "string"}, "address_street": {"title": "Address Street", "maxLength": 255, "type": "string"}, "address_street_nr": {"title": "Address Street Nr", "maxLength": 20, "type": "string"}, "address_zip": {"title": "Address Zip", "maxLength": 30, "type": "string"}, "address_city": {"title": "Address City", "maxLength": 255, "type": "string"}, "land_register_municipality": {"title": "Land Register Municipality", "description": "Grundbuch Gemeinde", "maxLength": 255, "type": "string"}, "land_register_id": {"title": "Land Register Id", "description": "Grundbuch Blatt-Nr.", "maxLength": 100, "type": "string"}, "status": {"title": "Status", "description": "RealEstatePropertyStatus Attribute", "maxLength": 40, "type": "string"}}, "required": ["property_partner", "property_number", "property_type", "address_zip", "address_city", "status"]}, "CollateralRealEstateProperty": {"title": "CollateralRealEstateProperty", "type": "object", "properties": {"property_number": {"title": "Property Number", "description": "Liegenschaftsnummer", "maxLength": 20, "type": "string"}, "property_collateral_type": {"title": "Property Collateral Type", "description": "Liegenschaftsdeckungstyp: Hauptdeckung | Nebendeckung | Verwaltungsgeschäft", "maxLength": 40, "type": "string"}}, "required": ["property_number", "property_collateral_type"]}, "Collateral": {"title": "Collateral", "description": "additional fields? sollen values mitgeliefert werden für GUI Mapping? partner parkey kommt separat", "type": "object", "properties": {"business_parkey": {"title": "Business Parkey", "description": "Geschäftsfall-Parkey of the collateral", "maxLength": 20, "type": "string"}, "business_number": {"title": "Business Number", "description": "Geschäfts-Nr.", "maxLength": 20, "type": "string"}, "collateral_number": {"title": "Collateral Number", "description": "Deckungs-Nr.", "maxLength": 20, "type": "string"}, "collateral_partner": {"title": "Collateral Partner", "description": "Partner des Deckungsgebers", "allOf": [{"$ref": "#/components/schemas/Partner"}]}, "collateral_type": {"title": "Collateral Type", "description": "e.g. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> intern", "maxLength": 40, "type": "string"}, "description": {"title": "Description", "description": "Bemerkung", "maxLength": 255, "type": "string"}, "collateral_status": {"title": "Collateral Status", "description": "Deckungsstatus Todo: ta<PERSON><PERSON><PERSON><PERSON> gebra<PERSON>t", "maxLength": 40, "type": "string"}, "policy_number": {"title": "Policy Number", "description": "Policennummer (im Fall der Deckungsart 'Personenversicherungen')", "maxLength": 255, "type": "string"}, "real_estate_properties": {"title": "Real Estate Properties", "type": "array", "items": {"$ref": "#/components/schemas/CollateralRealEstateProperty"}}}, "required": ["business_parkey", "business_number", "collateral_number", "collateral_partner", "collateral_type", "collateral_status"]}, "DossierExport": {"title": "DossierExport", "type": "object", "properties": {"dossier_uuid": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "export_uuid": {"title": "Export U<PERSON>", "type": "string", "format": "uuid"}}, "required": ["dossier_uuid", "export_uuid"]}, "ExportStatus": {"title": "ExportStatus", "description": "An enumeration.", "enum": ["success", "error"], "type": "string"}, "DossierExportFeedback": {"title": "DossierExportFeedback", "type": "object", "properties": {"export_uuid": {"title": "Export U<PERSON>", "type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ExportStatus"}, "message": {"title": "Message", "maxLength": 500, "type": "string"}}, "required": ["export_uuid", "status"]}, "Error": {"title": "Error", "type": "object", "properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"]}, "Result_UUID_": {"title": "Result[UUID]", "type": "object", "properties": {"data": {"title": "Data", "type": "string", "format": "uuid"}, "error": {"$ref": "#/components/schemas/Error"}}}, "ProcessReadyForExportRequest": {"title": "ProcessReadyForExportRequest", "type": "object", "properties": {"dossier_uuids": {"title": "<PERSON><PERSON><PERSON>", "type": "array", "items": {"type": "string", "format": "uuid"}}, "not_updated_since": {"title": "Not Updated Since", "type": "string", "format": "date-time"}}}}, "securitySchemes": {"BEKBJWTAuth": {"type": "http", "scheme": "bearer"}}}, "servers": null}
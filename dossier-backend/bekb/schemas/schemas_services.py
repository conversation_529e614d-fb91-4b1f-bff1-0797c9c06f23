from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict


class DossierValidationType(BaseModel):
    is_bekb_fipla: bool
    is_no_deal: Optional[bool]
    parkey_g: str  # business partner parkey
    parkey_p: str  # partner parkey
    businesscase_number: Optional[str]
    pers: bool

    model_config = ConfigDict(frozen=True)


class DocumentKeys(BaseModel):
    parkey_l: str = "0"  # property partner parkey
    parkey_d: str = "0"  # collateral partner parkey
    parkey_dok: str = "0"  # document parkey
    kre_key: str = "0"  # businesscase number
    lie_key: str = "0"  # property number
    dec_key: str = "0"  # collateral number
    ekd_nr: str

    model_config = ConfigDict(frozen=True)


class IndexFileEntry(BaseModel):
    scan_datum: str
    datum: str
    batch_id: UUID
    filename: str
    perscode: int
    seiten: int
    document_keys: DocumentKeys
    kurzform: str = "0"

    model_config = ConfigDict(frozen=True)


class IndexFileHeader(BaseModel):
    creation_datetime: datetime

    model_config = ConfigDict(frozen=True)

    def format(self) -> str:
        creation = self.creation_datetime
        return (
            f"COMMENT: OnDemand Generic Index File Format\n"
            f"COMMENT: This File is from Hypodossier AG\n"
            f"COMMENT: {creation.strftime('%d.%m.%Y')} - {creation.strftime('%H:%M')}\n"
            f"COMMENT:\n"
            f"CODEPAGE:819\n"
            f"COMMENT:\n"
        )

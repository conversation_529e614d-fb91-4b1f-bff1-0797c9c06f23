import json

import django
from datetime import timezone as datetime_timezone
import structlog
import random
from datetime import datetime, timedelta
from pathlib import Path
from tempfile import SpooledTemporaryFile
from typing import List
from urllib import parse
from urllib.parse import urlparse
from uuid import uuid4, UUID
from zipfile import ZipFile

import pytest
from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404
from django.urls import reverse
from django.utils import timezone
from faker import Faker
from faker.providers import date_time, address, person
from jwcrypto.jwk import JWK
from jwcrypto.jwt import JWT
from pydantic import TypeAdapter

import dossier.schemas as dossier_schema
from bekb.bekbload import load_document_category2ekd_mappings
from bekb.conftest import check_user
from bekb.schemas import schemas
from bekb.api import list_actively_used_business_parkeys
from bekb.collaterals import (
    collateral_assignment_status,
    get_collateral_requirements_satisfaction_options_for_account,
    get_collateral_requirement_satisfaction_options,
    CollateralAssigmentStatus,
    get_collaterals_by_businesscase,
)
from bekb.data import DATA_PATH
from bekb.fakes import (
    create_business_types,
    create_a_sample_account,
    create_sample_business_status,
    update_or_create_real_estate_property_status,
    update_or_create_property_collateral_types,
    update_or_create_real_estate_property_types,
    update_or_create_collateral_types,
    BekbAccountFactoryFaker,
    create_dossier_show_jwt,
    create_dossier_create_jwt,
    create_some_bekb_dossiers,
    handle_check_bekb_config,
    BekbInstanceType,
)
from bekb.models import (
    BEKBDossierProperties,
    Attribute,
    BusinessCase,
    RealEstateProperty,
    ExportFeedback,
    CollateralRealEstateProperty,
    Collateral,
    CollateralAssignment,
)
import bekb.schemas.schemas as bekb_schemas
from bekb.services import (
    assign_businesscase_to_dossier,
    sort_business_cases,
    get_fico_role,
    create_index_file,
    prepare_document_package_for_export,
    all_collaterals_mapped,
    set_pers,
    all_document_have_required_ekd_nr,
    load_bekb_attributes,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
)
from dossier.services import is_pers
from bekb.services import (
    map_user_to_schema,
    create_encoded_jwt,
    map_to_business_partner,
    map_partner_to_schema,
    get_fico,
)
from core.authentication import create_token, AuthenticatedClient
from core.schema import Result
from core.temporary_path import temporary_path
from dossier.api import create_semantic_dossier_simple
from dossier.duration_log import DurationLog
from dossier.fakes import add_some_fake_semantic_documents
from dossier.helpers import get_dossier_queryset
from dossier.models import (
    Account,
    Dossier,
    DossierUser,
    UserInvolvement,
    DocumentCategory,
    annotate_with_calculated_access_mode_dossier,
)
from django.conf import settings
from projectconfig.authentication import get_user_or_create
from semantic_document.models import SemanticDocument
from statemgmt.models import Status

logger = structlog.get_logger()

endpoint = "/partner/bekb/api/0.7"


def test_bekbe_account_loading(db, client: django.test.client.Client):
    bfac = BekbAccountFactoryFaker(update_attributes=True)
    assert bfac.account.key == "bekbe"

    assert bfac.account.enable_bekb_automatic_collateral
    assert bfac.account.enable_bekb_export
    assert bfac.account.enable_button_create
    assert bfac.account.enable_documents_delta_view
    assert not bfac.account.frontend_theme
    assert not bfac.account.photo_album_docx_template
    assert bfac.account.active_work_status_state_machine

    status_list = Status.objects.filter(
        state_machine=bfac.account.active_work_status_state_machine
    ).all()
    assert len(status_list) == 10

    # If it is just 37 then no loading took place
    # If it is 97 this is ok (but with unneeded uplicates for
    # CollateralType, BusinessType. Seems the changed the keys
    assert bfac.account.attribute_set.count() == 97


def test_show_dossier_api_basic(db, client: django.test.client.Client):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    assert account.dmf_endpoint == "https://www.localhost"

    test_business_parkey = "my test parkey"

    # already existing dossier should be updated with the new data, so lets create some dossier
    for dossier in range(3):
        bekb_account_factory.create_dossier(business_parkey=test_business_parkey)

    # Make sure all dossiers have a partner_partner set from the start
    for bekb_dossier in BEKBDossierProperties.objects.filter(
        business_partner__parkey=test_business_parkey
    ):
        assert bekb_dossier.partner_partner is not None

    show_data = create_dossier_show_jwt(bekb_account_factory, test_business_parkey)
    encoded_jwt = create_encoded_jwt(show_data.model_dump())

    url = reverse("bekb-api:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 302

    redirect_location = res.headers["Location"]

    # language is forwarded
    assert (
        redirect_location
        == f"{account.dmf_endpoint}/dashboard?business_parkey={parse.quote(show_data.business_partner.parkey)}&lang={show_data.language.value}"
    )

    # check all dossiers are updated
    for bekb_dossier in BEKBDossierProperties.objects.filter(
        business_partner__parkey=test_business_parkey
    ):
        # Make sure all dossiers still have a partner_partner set (and not deleted by the show_dossier)
        assert bekb_dossier.partner_partner is not None

        # business partner data must be updated
        assert (
            map_to_business_partner(bekb_dossier.business_partner)
            == show_data.business_partner
        )

        # check fico got updated
        assert map_user_to_schema(get_fico(bekb_dossier)) == show_data.fico

    # current user is updated
    assert (
        map_user_to_schema(
            DossierUser.objects.get(
                account=account, user__username=show_data.current_user.username
            )
        )
        == show_data.current_user
    )


def test_show_dossier_api_specific_dossier(db, client: django.test.client.Client):
    # If a dossier uuid is provided, show that dossier directly via the DMF
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    assert account.dmf_endpoint == "https://www.localhost"

    test_business_parkey = "my test parkey"

    # already existing dossier should be updated with the new data, so lets create some dossier

    bekb_dossier_properties_list = []
    for dossier in range(3):
        bekb_dossier_properties_list.append(
            bekb_account_factory.create_dossier(business_parkey=test_business_parkey)
        )

    show_data = create_dossier_show_jwt(bekb_account_factory, test_business_parkey)
    show_data.dossier_uuid = str(bekb_dossier_properties_list[0].dossier.uuid)
    encoded_jwt = create_encoded_jwt(show_data.model_dump())

    url = reverse("bekb-api:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 302

    redirect_location = res.headers["Location"]

    # language is forwarded
    assert (
        redirect_location
        == f"{account.dmf_endpoint}/dossier/{bekb_dossier_properties_list[0].dossier.uuid}/view/page?lang={show_data.language.value}"
    )


def test_show_dossier_api_with_partner_partner(db, client: django.test.client.Client):
    bekb_account_factory = BekbAccountFactoryFaker()
    bekbe_dossier = bekb_account_factory.create_dossier()

    business_parkey = bekbe_dossier.business_partner.parkey
    show_data = create_dossier_show_jwt(bekb_account_factory, business_parkey)
    show_data = show_data.model_copy(
        update=dict(partner_partner=bekb_account_factory.create_partner_partner())
    )

    # call the api
    encoded_jwt = create_encoded_jwt(show_data.model_dump())
    url = reverse("bekb-api:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 302

    bekbe_dossier.refresh_from_db()
    # partner must be updated
    assert (
        map_partner_to_schema(bekbe_dossier.partner_partner)
        == show_data.partner_partner
    )


def test_create_dossier_api(db, client: django.test.client.Client):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    default_create = create_dossier_create_jwt(bekb_account_factory)

    create_data_without_fico_firstname = default_create.model_copy()
    create_data_without_fico_firstname.fico.firstname = None

    create_datas = [default_create, create_data_without_fico_firstname]

    for create_data in create_datas:
        encoded_jwt = create_encoded_jwt(create_data.model_dump())
        url = reverse("bekb-api:create-dossier") + f"?encoded_jwt={encoded_jwt}"
        res = client.get(url)

        assert res.status_code == 302

        parsed = urlparse(res.headers["Location"])
        dossier_uuid = Path(parsed.path).parent.parent.name

        assert (
            res.headers["Location"]
            == f"{account.dmf_endpoint}/dossier/{dossier_uuid}/view/page?lang={create_data.language.value}"
        )

        check_user(create_data.fico, account)
        check_user(create_data.current_user, account)

        dossier = Dossier.objects.get(uuid=dossier_uuid, account=account)

        dossier_properties = BEKBDossierProperties.objects.get(dossier=dossier)

        if create_data.partner_partner is not None:
            assert (
                map_partner_to_schema(dossier_properties.partner_partner)
                == create_data.partner_partner
            )
        assert (
            map_to_business_partner(dossier_properties.business_partner)
            == create_data.business_partner
        )

        involvement: UserInvolvement
        for involvement in dossier.userinvolvement_set.all():
            print(
                dossier.uuid,
                involvement.user.user.username,
                involvement.dossier,
                involvement.role.key,
            )
        assert dossier.userinvolvement_set.count() == 2


# BEKB only
def test_create_or_update_attribute(db, bekb_api_client, testuser1_client):
    account = create_a_sample_account()

    # python manage.py dumpdata bekb.Attribute --format json
    bekb_attribute_json_export = """[{"model": "bekb.attribute", "pk": "00230fcd-a77f-44d0-968f-0c4030385c0d", "fields": {"created_at": "2022-11-29T08:43:16.143Z", "updated_at": "2022-11-29T08:43:16.143Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "B", "name_de": "Eigentumswohnung", "name_fr": "Eigentumswohnung fr"}}, {"model": "bekb.attribute", "pk": "0e0fdff8-2abc-41cf-bba2-927e19f58f82", "fields": {"created_at": "2022-11-29T08:37:47.773Z", "updated_at": "2022-11-29T08:37:47.773Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessType", "key": "NEW", "name_de": "Neugeschäft", "name_fr": "Neugeschäft FR"}}, {"model": "bekb.attribute", "pk": "2a6158fb-e3bf-4abc-8e5d-6d0d296c8018", "fields": {"created_at": "2022-11-29T08:40:34.415Z", "updated_at": "2022-11-29T08:45:05.184Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "CollateralType", "key": "1", "name_de": "Grundpfand", "name_fr": "Grundpfand fr"}}, {"model": "bekb.attribute", "pk": "56d14d5c-f52e-44ea-aed3-d1d7f586a5b1", "fields": {"created_at": "2022-11-29T08:37:00.473Z", "updated_at": "2022-11-29T08:37:00.473Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessStatus", "key": "ACTIVE", "name_de": "aktiv", "name_fr": "aktiv fr"}}, {"model": "bekb.attribute", "pk": "60491ccc-b149-4a8b-b7f1-9e1406006d5d", "fields": {"created_at": "2022-11-29T08:47:31.349Z", "updated_at": "2022-11-29T08:47:31.349Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyCollateralType", "key": "SECONDARY", "name_de": "Nebendeckung", "name_fr": "Nebendeckung fr"}}, {"model": "bekb.attribute", "pk": "66c9977f-7bb6-4851-9f7c-fb846d010b26", "fields": {"created_at": "2022-11-29T08:47:10.177Z", "updated_at": "2022-11-29T08:47:10.177Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyCollateralType", "key": "MAIN", "name_de": "Hauptdeckung", "name_fr": "Hauptdeckung fr"}}, {"model": "bekb.attribute", "pk": "796ba3e4-3d18-4141-9f6c-eec0462d9193", "fields": {"created_at": "2022-11-29T08:42:18.554Z", "updated_at": "2022-11-29T08:43:00.435Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "A", "name_de": "Einfamilienhaus", "name_fr": "Einfamilienhaus fr"}}, {"model": "bekb.attribute", "pk": "c2fab320-3641-42de-b0fb-827543dcd426", "fields": {"created_at": "2022-11-29T08:43:34.717Z", "updated_at": "2022-11-29T08:43:34.717Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "C", "name_de": "Garage", "name_fr": "Garage fr"}}, {"model": "bekb.attribute", "pk": "cf5bbd96-31e7-4827-a638-9d21764c9687", "fields": {"created_at": "2022-11-29T08:38:09.665Z", "updated_at": "2022-11-29T08:38:09.665Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessType", "key": "RESCHEDULE", "name_de": "Ablöser", "name_fr": "Ablöser fr"}}, {"model": "bekb.attribute", "pk": "dda52a91-12ae-4cc1-adec-773fac5bd348", "fields": {"created_at": "2022-11-29T08:37:26.246Z", "updated_at": "2022-11-29T08:37:26.246Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessStatus", "key": "DELETED", "name_de": "gelöscht", "name_fr": "geschlöscht fr"}}, {"model": "bekb.attribute", "pk": "e0a32d6a-1945-424a-8d79-56e760033034", "fields": {"created_at": "2022-11-29T08:44:10.297Z", "updated_at": "2022-11-29T08:44:10.297Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "F", "name_de": "Mehrfamilienhaus", "name_fr": "Mehrfamilienhaus fr"}}, {"model": "bekb.attribute", "pk": "e1441d3c-8a6a-4215-a61e-649c97a12701", "fields": {"created_at": "2022-11-29T08:41:14.082Z", "updated_at": "2022-11-29T08:44:56.733Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "CollateralType", "key": "2", "name_de": "Personenversicherung", "name_fr": "Personenversicherung fr"}}]"""

    # create an attribute which should be update
    Attribute.objects.create(
        entity=bekb_schemas.Entity.PropertyType,
        key="B",
        name_de="something else",
        name_fr="something else",
        account=account,
    )

    attributes = json.loads(bekb_attribute_json_export)
    attributes = [
        schemas.Attribute(
            entity=schemas.Entity[attribute.get("fields")["entity"]],
            key=attribute.get("fields").get("key"),
            name_de=attribute.get("fields").get("name_de"),
            name_fr=attribute.get("fields").get("name_fr"),
        )
        for attribute in attributes
    ]

    updates = schemas.AttributeUpdates(root=attributes)

    url = reverse("bekb-api:update-attributes") + f"?account_name={account.key}"

    assert testuser1_client.put(url).status_code == 400
    res = bekb_api_client.put(
        url, data=updates.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    stored_attributes = [
        schemas.Attribute(
            entity=schemas.Entity[attribute.entity],
            key=attribute.key,
            name_de=attribute.name_de,
            name_fr=attribute.name_fr,
        )
        for attribute in Attribute.objects.filter(account=account).all()
    ]

    def sort_attribute():
        return lambda x: f"{x.entity}{x.key}"

    assert attributes.sort(key=sort_attribute()) == stored_attributes.sort(
        key=sort_attribute()
    )


# BEKB only
def test_bekb_attribute_load(db, bekb_api_client):
    account = create_a_sample_account()

    updates = bekb_schemas.AttributeUpdates(
        root=TypeAdapter(List[bekb_schemas.Attribute]).validate_json(
            (DATA_PATH / "bekb_sample_load.json").read_text()
        )
    )
    assert len(updates.root) == 73

    url = reverse("bekb-api:update-attributes") + f"?account_name={account.key}"

    res = bekb_api_client.put(
        url, data=updates.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    assert Attribute.objects.filter(account=account).count() == 73


# BEKB only
def test_update_list_of_businesscase(db, bekb_api_client, testuser1_client):
    num_dossiers = 20
    account = create_a_sample_account()

    created_dossiers, _ = create_some_bekb_dossiers(account, num_dossiers)
    business_parkeys = list_actively_used_business_parkeys(account.key)

    assert len(business_parkeys) == num_dossiers

    faker = Faker()
    faker.add_provider(date_time)

    business_type = create_business_types(account)
    business_status = create_sample_business_status(account)

    business_cases = []
    for business_parkey in business_parkeys:
        for i in range(random.randint(0, 4)):
            business_cases.append(
                schemas.BusinessCase(
                    business_parkey=business_parkey,
                    business_number=faker.bothify("BN??######"),
                    business_type=random.choice(business_type).key,
                    business_status=random.choice(business_status).key,
                    mutation_date=faker.past_date(),
                    mutation_user=faker.bothify("??###"),
                )
            )
    business_cases = sort_business_cases(business_cases)
    updates = schemas.BusinessCaseUpdates(root=business_cases)

    url = reverse("bekb-api:update-businesscases") + f"?account_name={account.key}"

    assert testuser1_client.put(url).status_code == 400
    res = bekb_api_client.put(
        url, data=updates.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    current_business_cases = [
        schemas.BusinessCase(
            business_parkey=business_case.business_partner.parkey,
            business_number=business_case.business_number,
            business_type=business_case.business_type.key,
            business_status=business_case.business_status.key,
            mutation_date=business_case.mutation_date,
            mutation_user=business_case.mutation_user,
        )
        for business_case in BusinessCase.objects.filter(account=account).all()
    ]

    current_business_cases = sort_business_cases(current_business_cases)

    assert len(updates.root) == len(current_business_cases)

    assert updates.root == current_business_cases


# BEKB only
def test_update_collaterals(db, bekb_api_client, testuser1_client):
    """
    - Create some dossiers in db
    - Assign a businesscase to the dossiers (needed to update collaterals on this dossier)
    """
    num_dossiers = 20
    log = DurationLog("test_update_collaterals")

    bekb_account_factory = BekbAccountFactoryFaker()

    account = bekb_account_factory.account
    # bekb_dossier = bekb_account_factory.create_dossier()
    # bekb_dossiers: List[BEKBDossierProperties] = [bekb_dossier]
    created_dossiers, bekb_dossiers = create_some_bekb_dossiers(account, num_dossiers)

    updates = []

    for bekb_dossier in bekb_dossiers:
        business_case = bekb_account_factory.create_businesscase(bekb_dossier)

        assign_businesscase_to_dossier(bekb_dossier, business_case)

        # collateral_types = bekb_account_factory.collateral_types
        # required_collateral_type = #random.choice(collateral_types)

        collateral_1_prop = bekb_account_factory.create_collateral(
            bekb_dossier=bekb_dossier,
            business_case=business_case,
            collateral_type=bekb_account_factory.get_collateral_type("1"),
        )

        collateral_2_policy = bekb_account_factory.create_collateral(
            bekb_dossier=bekb_dossier,
            business_case=business_case,
            collateral_type=bekb_account_factory.get_collateral_type("2"),
        )

        collaterals: List[schemas.Collateral] = [collateral_1_prop, collateral_2_policy]
        # for bekb_dossier in bekb_dossiers:
        for c in collaterals:
            relation = CollateralRealEstateProperty.objects.filter(
                account=account, collateral=c
            ).all()

            related_properties_relation = [
                schemas.CollateralRealEstateProperty(
                    property_number=rel.realestate_property.property_number,
                    property_collateral_type=rel.property_collateral_type.key,
                )
                for rel in relation
            ]

            update = schemas.Collateral(
                business_parkey=bekb_dossier.business_partner.parkey,
                business_number=bekb_dossier.business_case.business_number,
                collateral_number=c.collateral_number,
                collateral_partner=schemas.Partner(**c.collateral_partner.__dict__),
                document_parkey="0",
                collateral_type=c.collateral_type.key,
                description=f"Updated Collateral {c.collateral_number}",
                collateral_status=c.collateral_status.key,
                policy_number=f"Updated {c.policy_number}" if c.policy_number else None,
                real_estate_properties=related_properties_relation,
            )
            updates.append(update)

    logger.info(f"Send updates: {len(updates)}")

    updates_collection = schemas.CollateralUpdates(root=updates)

    url = reverse("bekb-api:update-collateral") + f"?account_name={account.key}"

    assert testuser1_client.put(url).status_code == 400
    res = bekb_api_client.put(
        url, data=updates_collection.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    new_collaterals = list(Collateral.objects.filter(account=account).all())

    assert len(updates) == len(new_collaterals) == num_dossiers * 2

    for c in new_collaterals:
        assert c.description.startswith("Updated")

        real_estate_property_relations = list(
            CollateralRealEstateProperty.objects.filter(
                account=account, collateral=c
            ).all()
        )
        if c.collateral_type.key == "1":
            assert len(real_estate_property_relations) == 1
            assert (
                real_estate_property_relations[0].property_collateral_type is not None
            )
        else:
            assert real_estate_property_relations == []

        if c.collateral_type.key == "2":
            # This is a policy col
            assert c.policy_number.startswith("Updated")
        else:
            assert c.policy_number is None

    log.log_all_events()


# BEKB only
def test_update_realestate_properties(db, bekb_api_client, testuser1_client):
    account = create_a_sample_account()

    created_dossiers, _ = create_some_bekb_dossiers(account, 10)

    real_estate_property_status = update_or_create_real_estate_property_status(account)
    update_or_create_property_collateral_types(account)
    property_types = update_or_create_real_estate_property_types(account)

    faker = Faker(locale="ru_RU")
    faker.add_provider(address)
    faker.add_provider(date_time)

    properties = []
    for new_bekb_dossier in created_dossiers:
        properties.append(
            schemas.RealEstateProperty(
                property_partner=new_bekb_dossier.business_partner,
                property_type=random.choice(property_types).key,
                address_street=random.choice([None, faker.street_name()]),
                address_street_nr=random.choice([None, faker.building_number()]),
                address_zip=faker.postcode(),
                address_city=faker.city(),
                land_register_municipality=faker.city(),
                land_register_id=faker.bothify(
                    "Reg.Nr ??" + "#" * random.randint(1, 91)
                ),
                status=random.choice(real_estate_property_status).key,
                property_number=faker.bothify("PrN######"),
            )
        )

    updates = schemas.RealEstatePropertiesUpdates(root=properties)

    url = (
        reverse("bekb-api:update-realestateproperties") + f"?account_name={account.key}"
    )

    assert testuser1_client.put(url).status_code == 400
    res = bekb_api_client.put(
        url, data=updates.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    assert len(RealEstateProperty.objects.filter(account=account).all()) == len(
        properties
    )


# BEKB specific
def test_bekb_document_category_configuration(db):
    bekb_account_factory = BekbAccountFactoryFaker()
    doc_cat_map, mappings = handle_check_bekb_config(
        bekb_account_factory.account.key, BekbInstanceType.MORTGAGE
    )

    # These numbers have to be adjusted whenever a new document category is added for bekb
    # 240326 mt: PROOF_OF_INCOME, RENOVATIONS, BEKB_EKD118 have been added
    # 240507 mt: SHARE_REGISTER, PLATFORM_AGREEMENT added
    # 240523 mt: PROPERTY_BILL added
    # 240723 mt: 14 new doc cats added. now 305 instead of 291
    # 241022 mt: 1 new doc cat added 600-EKD142 myky-Dossier
    # 241023 mt: 1 new doc cat added 511-EKD130 Gründungsunterlagen

    """
    241230 added these 6:
    
273-EKD131,DEATH_CERTIFICATE,Sterbeurkunde,Death certificate,Acte décès,Certificato di morte,,0
700-EKD138,BEKB_FIPLA_RESULT,FiPla: FiPla Vorsorgeausweis,FiPla: Pension Certificate,PlaFi: Certificat de Prévoyance,FiPla: Certificato di Previdenza,,0
700-EKD139,BEKB_EKD139,FiPla: Diverses,FiPla: Miscellaneous,FiPla : Divers,FiPla: Varie,,0
745-EKD133,MORTGAGE_SUBORDINATION_AGREEMENT,Rangrücktrittserklärung,Subordination agreement,Déclaration postposition,Dichiarazione di postergazione,,0
786-EKD133,LETTER_COMMITMENT_NOTARY,Verpflichtungserklärung Notar,Letter of Commitment Notary,Lettre engagement notaire,Lettera impegno del notaio,,0
788-EKD133,CORRESPONDENCE_NOTARY,Korrespondenz Notar,Correspondence Notary,Currier - email notaire,Corrispondenza notaio,,0

    250103 added EKD134
    250224 added BEKB_FIPLA_FORM EKD120
    250314 added BEKB_TOTAL_ENGAGEMENT

    """
    num_categories_expected = 307 + 9

    assert len(doc_cat_map.items()) == num_categories_expected
    assert (
        len(mappings.items()) == num_categories_expected
    )  # Difference TAX_ATTACHMENTS has been removed

    doc_cat_map_keys = doc_cat_map.keys()
    mappings_keys = mappings.keys()

    assert doc_cat_map_keys == mappings_keys


# BEKB only
def test_update_or_create_collateral_types(db):
    account = create_a_sample_account()

    collateral_types = list(update_or_create_collateral_types(account))

    collateral_types = sorted(collateral_types, key=lambda x: int(x.key))

    assert len(collateral_types) == 11
    for t in collateral_types:
        print(t)
        assert t.name_de
        assert t.name_fr


# BEKB only
def test_find_collaterals_for_doccat(db):
    # This also loads initial document categories
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    collateral_types = {
        type.key: type for type in update_or_create_collateral_types(account)
    }
    requirements_satisfaction_options = get_collateral_requirement_satisfaction_options(
        collateral_types
    )

    # This one does not have an EKD mapping and therefore does not exist in the requirements_satisfaction_options
    # Status INVALID_FOR_EXPORT
    doccat = DocumentCategory.objects.filter(
        name="WHITE_PAGES", account=account
    ).first()
    assert doccat.name not in requirements_satisfaction_options

    # This one has one possible collateral type
    # Status COLLATERAL_MAPPED or COLLATERAL_MISSING
    doccat = DocumentCategory.objects.filter(
        name="MEETING_MINUTES_CONDOMINIUM", account=account
    ).first()
    assert doccat.name in requirements_satisfaction_options
    collaterals_valid = requirements_satisfaction_options[doccat.name]
    assert len(collaterals_valid) == 1

    # this one has two possible collateral types
    # Status COLLATERAL_MAPPED or COLLATERAL_MISSING
    doccat = DocumentCategory.objects.filter(name="BEKB_EKD09", account=account).first()
    assert doccat.name in requirements_satisfaction_options
    collaterals_valid = requirements_satisfaction_options[doccat.name]
    assert len(collaterals_valid) == 3

    # This one does not need any collateral
    # Status NO_COLLATERAL_REQUIRED
    doccat = DocumentCategory.objects.filter(
        name="AGREEMENT_CHARGE_IMMOVABLE_PROPERTY", account=account
    ).first()
    assert doccat.name in requirements_satisfaction_options
    collaterals_valid = requirements_satisfaction_options[doccat.name]
    assert len(collaterals_valid) == 0


# BEKB specific
def test_load_external_document_categories(db):
    account = create_a_sample_account()

    collateral_types = {
        type.key: type for type in update_or_create_collateral_types(account)
    }
    requirements_satisfaction_options = get_collateral_requirement_satisfaction_options(
        collateral_types
    )

    # This list only contains all document categories that require a collateral
    # All document categories without collaterals are not included
    # Order of the list is relevant and should be ascending here. Actuals will be sorted before comparison
    correct_result = {
        "ADDITIONAL_COST_ACCOUNT": ["1"],
        "ARCHITECT_CONTRACT": ["1"],
        "ASSUMPTION_DEBT_NOTICE": ["1"],
        "BEKB_EKD08": ["8"],
        "BEKB_EKD09": ["2", "8", "9"],
        "BEKB_EKD10": ["2", "8", "9"],
        "BEKB_EKD102": ["1"],
        "BEKB_EKD103": ["1"],
        "BEKB_EKD11": ["3"],
        "BEKB_EKD17": ["5"],
        "BEKB_EKD18": ["5"],
        "BEKB_EKD20": ["1"],
        "BEKB_EKD26": ["1"],
        "BEKB_EKD27": ["1"],
        "BEKB_EKD28": ["1"],
        "BEKB_EKD29": ["1"],
        "BEKB_EKD31": ["4"],
        "BEKB_EKD32": ["10"],
        "BEKB_EKD91": ["8", "9"],
        "BEKB_EKD92": ["8", "9"],
        "BEKB_EKD93": ["1"],
        "BEKB_EKD94": ["1"],
        "BEKB_EKD98": ["1"],
        "BUILDING_DESCRIPTION": ["1"],
        "BUILDING_RIGHTS_AGREEMENT": ["1"],
        "CONSTRUCTION_ACCOUNT": ["1"],
        "CONSTRUCTION_COMPANY_LIST": ["1"],
        "CONSTRUCTION_CONTRACT": ["1"],
        "CONSTRUCTION_COST_ESTIMATE": ["1"],
        "CONSTRUCTION_COST_SUMMARY": ["1"],
        "CONSTRUCTION_INSURANCE": ["1"],
        "CONSTRUCTION_PERMIT": ["1"],
        "CONSTRUCTION_PLAN": ["1"],
        "CONSTRUCTION_QUOTATION": ["1"],
        "CONTRACT_GENERAL_CONTRACTOR": ["1"],
        "CONTRACT_OF_SALE": ["1"],
        "CONTRACT_TOTAL_CONTRACTOR": ["1"],
        "EASEMENT_CONTRACT": ["1"],
        "EXTRACT_FROM_LAND_REGISTER": ["1"],
        "FOUNDATION_CERTIFICATE_CONDOMINIUM": ["1"],
        "GEAK_CERTIFICATE": ["1"],
        "LIST_OF_RENOVATIONS": ["1"],
        "MEETING_MINUTES_CONDOMINIUM": ["1"],
        "MINERGIE_CERTIFICATE": ["1"],
        "PENSION3A_INSURANCE_LETTER_REDEMPTION": ["2"],
        "PENSION3A_INSURANCE_STATEMENT": ["2"],
        "PLAN_ANY": ["1"],
        "PLAN_CADASTER": ["1"],
        "PLAN_FLOOR": ["1"],
        "PLAN_SITUATION": ["1"],
        "PLR_CADASTRE": ["1"],
        "PROJECT_BUDGET": ["1"],
        "PROPERTY_ACCOUNTS": ["1"],
        "PROPERTY_BILL": ["1"],
        "PROPERTY_INSURANCE": ["1"],
        "PROPERTY_TRUSTEE_CONTRACT": ["1"],
        "PROPERTY_VALUATION": ["1"],
        "PROPERTY_VALUATION_GOV": ["1"],
        "RENOVATIONS": ["1"],
        "RENOVATION_FUND": ["1"],
        "RENTAL_MISC": ["1"],
        "STATEMENT_VALUE_RATIO_PROPERTY": ["1"],
        "TENANCY_AGREEMENT": ["1"],
        "TENANT_DIRECTORY": ["1"],
        "USER_REGULATIONS_CONDOMINIUM": ["1"],
    }
    correct_result = dict(sorted(correct_result.items()))

    actual_options = {
        k: [i.key for i in v]
        for k, v in requirements_satisfaction_options.items()
        if len(v) > 0
    }
    for key, val in actual_options.items():
        actual_options[key] = sorted(val)

    assert actual_options == correct_result


# No idea whether Filpa needs this
@pytest.mark.parametrize(
    "document_category_key, ekd_nr, has_col, has_property",
    [
        ("BEKB_EKD16", "EKD16", False, False),
        ("CONSTRUCTION_QUOTATION", "EKD44", True, True),
        ("PLAN_FLOOR", "EKD35", True, True),
        ("PENSION3A_INSURANCE_STATEMENT", "EKD76", True, False),
        ("TAX_DECLARATION", "EKD53", False, False),
    ],
)
def test_create_index_file_deal(
    db, document_category_key, ekd_nr, has_col, has_property
):
    bekb_account_factory = BekbAccountFactoryFaker()

    bekb_dossier = bekb_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=1,
        valid_document_category_keys=[document_category_key],
        possible_work_status_keys=["READY_FOR_EXPORT_DEAL"],
    )

    assert bekb_dossier.dossier.work_status.key == "READY_FOR_EXPORT_DEAL"
    assert len(list(bekb_dossier.dossier.semantic_documents.all())) == 1
    sem_doc = bekb_dossier.dossier.semantic_documents.first()
    assert sem_doc.document_category.name == document_category_key

    qs_collateral = CollateralAssignment.objects.filter(
        account=bekb_dossier.dossier.account, semantic_document=sem_doc
    )
    if has_col:
        col_ass = qs_collateral.get()

        assert col_ass.collateral.collateral_number
        assert col_ass.collateral.business_partner.parkey
        col_parkey = col_ass.collateral.collateral_partner.parkey
        col_no = col_ass.collateral.collateral_number
        if has_property:
            assert col_ass.property
            prop_parkey = col_ass.property.property_partner.parkey
            prop_no = col_ass.property.property_number
        else:
            assert col_ass.property is None
            prop_parkey = 0
            prop_no = 0
    else:
        assert len(qs_collateral.all()) == 0
        col_parkey = 0
        col_no = 0
        prop_parkey = 0
        prop_no = 0

    with temporary_path() as temp_path:
        creation = datetime(2023, 1, 2, 14, 30, 23, tzinfo=datetime_timezone.utc)
        document_package = prepare_document_package_for_export(bekb_dossier, temp_path)
        index_file = create_index_file(
            uuid4(), bekb_dossier, creation, document_package
        )
        assert "GROUP_FILENAME:b'" not in index_file

        assert f"GROUP_FIELD_NAME:FORMULAR\nGROUP_FIELD_VALUE:{ekd_nr}\n" in index_file

        assert (
            f"GROUP_FIELD_NAME:PARKEY_D\nGROUP_FIELD_VALUE:{col_parkey}\n" in index_file
        )
        assert f"GROUP_FIELD_NAME:DEC_KEY\nGROUP_FIELD_VALUE:{col_no}\n" in index_file

        assert (
            f"GROUP_FIELD_NAME:PARKEY_L\nGROUP_FIELD_VALUE:{prop_parkey}\n"
            in index_file
        )
        assert f"GROUP_FIELD_NAME:LIE_KEY\nGROUP_FIELD_VALUE:{prop_no}\n" in index_file

        # Filename must be tested against the value from document_package and cannot be easily
        # created from the semantic document as there could be e.g. numbering as in "604_Grundbuchauszug_(1).pdf"
        for semdoc_export_file in document_package.values():
            filename = semdoc_export_file.filename
            assert (
                f"GROUP_FIELD_NAME:FILENAME\nGROUP_FIELD_VALUE:{filename}\n"
                in index_file
            )
            assert f"GROUP_FILENAME:{filename}" in index_file

        logger.info(index_file)


def test_create_index_file_no_deal(db):
    bekb_account_factory = BekbAccountFactoryFaker()

    bekb_dossier = bekb_account_factory.create_ready_for_export_dossier(
        min_num_documents=1,
        max_num_documents=10,
        possible_work_status_keys=["READY_FOR_EXPORT_NO_DEAL"],
    )

    print(
        Status.objects.filter(
            state_machine=bekb_dossier.account.active_work_status_state_machine
        ).all()
    )
    dossier = bekb_dossier.dossier

    assert dossier.semantic_documents.count() > 0

    bekb_dossier = BEKBDossierProperties.objects.get(uuid=bekb_dossier.uuid)

    creation = datetime(2023, 1, 2, 14, 30, 23, tzinfo=datetime_timezone.utc)

    with temporary_path() as temp_path:
        document_package = prepare_document_package_for_export(bekb_dossier, temp_path)
        index_file = create_index_file(
            uuid4(), bekb_dossier, creation, document_package
        )
        assert "GROUP_FILENAME:b'" not in index_file
        print(index_file)
        assert (
            """GROUP_FIELD_VALUE:EKD81
GROUP_FIELD_NAME:ORDNER
GROUP_FIELD_VALUE:EKD81
GROUP_FIELD_NAME:FORMNR
GROUP_FIELD_VALUE:EKD81"""
            in index_file
        )

        assert "GROUP_FIELD_NAME:PARKEY_P\nGROUP_FIELD_VALUE:" in index_file
        assert "GROUP_FIELD_NAME:PARKEY_D\nGROUP_FIELD_VALUE:" in index_file
        assert (
            """GROUP_FIELD_NAME:PARKEY_D
GROUP_FIELD_VALUE:0
GROUP_FIELD_NAME:PARKEY_L
GROUP_FIELD_VALUE:0
GROUP_FIELD_NAME:PARKEY_DOK
GROUP_FIELD_VALUE:0
GROUP_FIELD_NAME:KRE_KEY
GROUP_FIELD_VALUE:0
GROUP_FIELD_NAME:LIE_KEY
GROUP_FIELD_VALUE:0
GROUP_FIELD_NAME:DEC_KEY
GROUP_FIELD_VALUE:0"""
            in index_file
        )

        # Filename must be tested against the value from document_package and cannot be easily
        # created from the semantic document as there could be e.g. numbering as in "604_Grundbuchauszug_(1).pdf"
        for semdoc_export_file in document_package.values():
            filename = semdoc_export_file.filename
            assert (
                f"GROUP_FIELD_NAME:FILENAME\nGROUP_FIELD_VALUE:{filename}\n"
                in index_file
            )
            assert f"GROUP_FILENAME:{filename}" in index_file


def test_show_dossier_ready_for_export(db, bekb_api_client, testuser1_client):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account
    bekb_dossiers = []
    num_of_exports = 2
    for i in range(num_of_exports):
        bekb_dossier = bekb_account_factory.create_export_archive_available_dossier()
        bekb_dossiers.append(bekb_dossier)

    url = (
        reverse("bekb-api:show-dossier-ready-for-export")
        + f"?account_name={account.key}"
    )

    assert testuser1_client.get(url).status_code == 400
    res = bekb_api_client.get(url)
    assert res.status_code == 200
    dossier_exports = TypeAdapter(List[schemas.DossierExport]).validate_json(
        res.content
    )
    assert len(dossier_exports) == num_of_exports


def test_process_ready_for_export_api(db, bekb_api_client):
    # Since 240408 this runs with the new business case types and supports READY_FOR_EXPORT_FICO
    bekb_account_factory = BekbAccountFactoryFaker(update_statemgmt=True)
    account = bekb_account_factory.account

    ready_for_export_states = Status.objects.filter(
        state_machine=account.active_work_status_state_machine,
        key__in=VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
    ).all()

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == 0
    )

    for wsk in VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE:
        # We accept that this could create a random businesscase type that is not
        # valid with the chosen work_status. But we ignore it as it is not relevant
        # for this test
        bekb_account_factory.create_ready_for_export_dossier(
            possible_work_status_keys=[wsk]
        )

    num_of_exports = len(VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE)

    export_archive_available_state = Status.objects.get(
        state_machine=account.active_work_status_state_machine,
        key="EXPORT_ARCHIVE_AVAILABLE",
    )

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == num_of_exports
    )

    res = bekb_api_client.post(
        reverse("bekb-api:process-ready-for-export") + f"?account_name={account.key}",
        data="{}",
        content_type="application/json",
    )
    result = TypeAdapter(List[Result[UUID]]).validate_json(res.content)
    assert len(result) == num_of_exports
    assert res.status_code == 201

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == 0
    )
    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status=export_archive_available_state
        ).count()
        == num_of_exports
    )


def test_download_file(db, bekb_api_client, testuser1_client):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    bekb_dossier = bekb_account_factory.create_export_archive_available_dossier()

    export_uuid = bekb_dossier.dossier.bekbexport.uuid

    url = (
        reverse("bekb-api:download-file")
        + f"?account_name={account.key}&export_uuid={export_uuid}"
    )

    assert testuser1_client.get(url).status_code == 400
    res = bekb_api_client.get(url)
    assert res.status_code == 200

    content_disposition = res.headers["content-disposition"]
    assert (
        content_disposition
        == f'attachment; filename="05_hypodossier_bekbe_{bekb_dossier.dossier.bekbexport.file.created_at.strftime("%Y%m%d")}_{export_uuid}.zip"'
    )

    with SpooledTemporaryFile() as temp_file:
        for block in iter(res.streaming_content):
            temp_file.write(block)

        with ZipFile(temp_file, "r") as zip:
            filenames = [file.filename for file in zip.filelist]

        assert len(filenames) == bekb_dossier.dossier.semantic_documents.count() + 1


def test_add_dossier_export_feedback(db, bekb_api_client, testuser1_client):
    """
    Simulate receiving export feedback from BEKB. This should update the ExportFeedback entry.
    @param db:
    @param bekb_api_client:
    @param testuser1_client:
    @return: HTTP 204 if update has been done successfully. HTTP 401 if not allowed.
    """

    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    bekb_dossier_success = bekb_account_factory.create_export_archive_available_dossier(
        min_num_documents=1
    )
    assert bekb_dossier_success.dossier.expiry_date > timezone.now() + timedelta(days=2)
    export_uuid_success = bekb_dossier_success.dossier.bekbexport.uuid

    bekb_dossier_error = bekb_account_factory.create_export_archive_available_dossier()
    export_uuid_error = bekb_dossier_error.dossier.bekbexport.uuid

    feedbacks = schemas.DossierExportFeedbacks(
        root=[
            schemas.DossierExportFeedback(
                export_uuid=export_uuid_success,
                status=schemas.ExportStatus.SUCCESS,
            ),
            schemas.DossierExportFeedback(
                export_uuid=export_uuid_error,
                status=schemas.ExportStatus.ERROR,
                message="there was an error",
            ),
        ]
    )

    url = reverse("bekb-api:add-export-feedback") + f"?account_name={account.key}"

    assert testuser1_client.post(url).status_code == 400
    res = bekb_api_client.post(
        url, data=feedbacks.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    assert ExportFeedback.objects.filter(account=account).count() == 2

    bekb_dossier_success.refresh_from_db()
    assert bekb_dossier_success.dossier.expiry_date <= timezone.now() + timedelta(
        days=2
    )

    bekb_dossier_error.refresh_from_db()

    assert bekb_dossier_success.dossier.work_status.key == "EXPORT_DONE"
    assert bekb_dossier_error.dossier.work_status.key == "EXPORT_ERROR"

    # Now try to resend the same feedback. This is not normal, because feedbacks have already been processed.
    res2 = bekb_api_client.post(
        url, data=feedbacks.model_dump_json(), content_type="application/json"
    )
    body = res2.content
    print(f"{body = }")
    # assert res2.status_code == 200
    assert res2.status_code == 204


def test_update_dossier_properties_api(db, bekb_api_client, testuser1_client):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    fico_role, _ = get_fico_role(account)

    sample_dossier0 = bekb_account_factory.create_sample_dossier()
    sample_dossier0.pers = False
    fico0 = UserInvolvement.objects.filter(
        dossier=sample_dossier0.dossier, role=fico_role
    ).first()
    assert fico0 is not None
    sample_dossier0.save()

    sample_dossier1 = bekb_account_factory.create_sample_dossier()
    sample_dossier1.pers = True
    sample_dossier1.save()

    sample_dossier2 = bekb_account_factory.create_sample_dossier()
    sample_dossier2.pers = True
    # lets se if the update works for both dossier if they have the same partner
    sample_dossier2.business_partner = sample_dossier1.business_partner
    sample_dossier2.save()

    simple_pers_update = schemas.DossierPropertiesUpdates(
        root=[
            schemas.DossierProperties(
                business_parkey=sample_dossier1.business_partner.parkey,
            )
        ]
    )

    url = reverse("bekb-api:update-dossier-properties") + f"?account_name={account.key}"

    assert testuser1_client.put(url).status_code == 400
    res = bekb_api_client.put(
        url, data=simple_pers_update.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    # should be unchanged
    sample_dossier0.refresh_from_db()
    assert sample_dossier0.pers is False
    assert sample_dossier0.business_partner != sample_dossier1.business_partner

    username = "<EMAIL>"
    simple_user_update = schemas.DossierPropertiesUpdates(
        root=[
            schemas.DossierProperties(
                business_parkey=sample_dossier1.business_partner.parkey,
                fico=schemas.User(
                    username=username,
                    firstname="test",
                    name="test2",
                    pers=False,
                    active=False,
                ),
            )
        ]
    )

    res = bekb_api_client.put(
        url, data=simple_user_update.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    assert (
        UserInvolvement.objects.filter(dossier=sample_dossier0.dossier, role=fico_role)
        .first()
        .user
        == fico0.user
    )

    # should be changed
    updated_fico = User.objects.get(username=username)
    assert (
        UserInvolvement.objects.filter(dossier=sample_dossier1.dossier, role=fico_role)
        .first()
        .user.user
        == updated_fico
    )

    assert (
        UserInvolvement.objects.filter(dossier=sample_dossier2.dossier, role=fico_role)
        .first()
        .user.user
        == updated_fico
    )


def test_update_dossier_properties_api_with_incomplete_data(
    db, bekb_api_client, testuser1_client
):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    sample_dossier1 = bekb_account_factory.create_sample_dossier()
    sample_dossier1.pers = True
    sample_dossier1.save()

    username_of_fico = "newuser"

    simple_pers_update = schemas.DossierPropertiesUpdates(
        root=[
            schemas.DossierProperties(
                business_parkey=sample_dossier1.business_partner.parkey,
                fico=schemas.User(
                    username=username_of_fico,
                    firstname="test",
                    name="test2",
                    pers=False,
                    active=False,
                ),
            )
        ]
    )

    url = reverse("bekb-api:update-dossier-properties") + f"?account_name={account.key}"

    json_str = simple_pers_update.model_dump_json()
    json_str_corrupt = json_str.replace(f'"{username_of_fico}"', "null")
    res = bekb_api_client.put(
        url, data=json_str_corrupt, content_type="application/json"
    )
    assert res.status_code == 422


def test_create_username(db):
    account = Account.objects.create(key="unittest")
    dossier_user = get_user_or_create(account, "service account")
    assert dossier_user
    assert dossier_user.user.first_name == ""
    assert dossier_user.user.last_name == ""
    assert dossier_user.user.email == ""

    faker = Faker()
    faker.add_provider(person)
    firstname = faker.first_name()
    lastname = faker.last_name()
    email = faker.email()
    dossier_user = get_user_or_create(
        account, "service account", email, firstname, lastname
    )

    print(firstname, lastname, email)

    assert dossier_user.user.first_name == firstname
    assert dossier_user.user.last_name == lastname
    assert dossier_user.user.email == email


def test_create_simple_bekb_dossier(db):
    account = Account.objects.get(key="bekb test")
    account.enable_bekb_export = True
    account.save()
    dossier = get_object_or_404(
        annotate_with_calculated_access_mode_dossier(get_dossier_queryset()),
        uuid="2ea4fa0d-4e2c-48c1-9c6f-2b999cd287f3",
    )

    res = create_semantic_dossier_simple(dossier, False)

    for uuid, processed_file in res.processed_files.items():
        for number, page in processed_file.pages.items():
            page.image = "https://example.com/image"

    expected = dossier_schema.SemanticDossierSimple.model_validate_json(
        (DATA_PATH / "simple_bekb_dossier_1.json").read_text()
    )
    # TODO: disables the aggregated objects comparison because there
    for semantic_document in res.semantic_documents:
        semantic_document.aggregated_objects = []

    for semantic_document in expected.semantic_documents:
        semantic_document.aggregated_objects = []

    dossier_schema.SemanticDossierSimple.model_validate_json(res.model_dump_json())

    logger.info(f"actual max_expiry_date=={res.max_expiry_date}")
    logger.info(f"expect max_expiry_date=={expected.max_expiry_date}")
    # Disabled as has issues with minor diffs in timestamps
    # when run in CI
    # actual = res.json()
    # assert actual == expected.json()


def test_collateral_assignment_status(db):
    documents = SemanticDocument.objects.filter(
        dossier__name="Dossier von Juliane Ernst"
    ).all()
    bekb_dossier = documents[0].dossier.bekbdossierproperties
    options = get_collateral_requirements_satisfaction_options_for_account(
        bekb_dossier.account
    )

    all_collaterals_valid = get_collaterals_by_businesscase(
        account=bekb_dossier.account,
        business_partner=bekb_dossier.business_partner,
        business_case=bekb_dossier.business_case,
    )

    cass = [
        collateral_assignment_status(all_collaterals_valid, document, options)
        for document in documents
    ]
    assert sorted([cas.status for cas in cass]) == sorted(
        [
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.COLLATERAL_MAPPED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
            CollateralAssigmentStatus.Status.NO_COLLATERAL_REQUIRED,
        ]
    )


def test_bekb_all_collaterals_mapped(db):
    bekb_dossier = BEKBDossierProperties.objects.get(
        dossier__name="Dossier von Juliane Ernst"
    )
    assert all_collaterals_mapped(bekb_dossier) is True
    CollateralAssignment.objects.all().delete()
    assert all_collaterals_mapped(bekb_dossier) is False


def test_only_pers_user_has_access_to_pers_dossier_for_dossier_list(
    db, bekbuser1_client
):
    # Setup: Create 1 dossier and set it to pers
    bekb_account_factory = BekbAccountFactoryFaker()
    bekb_dossier = bekb_account_factory.create_dossier(pers=True)
    bekb_dossier.save()

    owner = bekb_dossier.dossier.owner
    owner.is_active = True
    owner.save()

    assert bekb_dossier.pers
    assert Dossier.objects.filter(account=bekb_account_factory.account).count() == 1

    # Verify than the user has same pers setting as the dossier
    du = DossierUser.objects.get(account=bekb_account_factory.account, user=owner)
    assert is_pers(du) is True

    # Test 1: Verify that pers user has access to pers dossier
    token = create_token(
        "testuser",
        "testuserfamily",
        owner.email,
        account_key=bekb_account_factory.account.key,
    )
    client = AuthenticatedClient(token)
    res = client.get(reverse("api:list_dossier") + "?number_page=0")
    assert res.status_code == 200
    dossiers = dossier_schema.PaginatedDossier.model_validate_json(res.content)
    assert dossiers.total_dossiers_count == 1

    # Test 2: Change dossier owner to non-pers and verify that he has no access anymore
    du = DossierUser.objects.get(account=bekb_account_factory.account, user=owner)
    set_pers(bekb_account_factory.account, owner, False)
    owner.save()
    assert is_pers(du) is False

    res = client.get(reverse("api:list_dossier") + "?number_page=0")

    assert res.status_code == 200
    dossiers = dossier_schema.PaginatedDossier.model_validate_json(res.content)
    assert dossiers.total_dossiers_count == 0

    # Test 3: Change dossier to non-pers and verify we have access again
    bekb_dossier.pers = False
    bekb_dossier.save()

    res = client.get(reverse("api:list_dossier") + "?number_page=0")

    assert res.status_code == 200
    dossiers = dossier_schema.PaginatedDossier.model_validate_json(res.content)
    assert dossiers.total_dossiers_count == 1


def test_all_document_have_required_ekd_nr(db):
    bekb_account_factory = BekbAccountFactoryFaker()
    bekb_dossier = bekb_account_factory.create_dossier()
    assert bekb_dossier.dossier.semantic_documents.count() == 0
    assert all_document_have_required_ekd_nr(bekb_dossier) is True


# @pytest.mark.flaky(max_runs=4)
def test_collateral_requirements_satisfaction_options(db):
    bekb_account_factory = BekbAccountFactoryFaker()

    start = timezone.now()
    options = get_collateral_requirements_satisfaction_options_for_account(
        bekb_account_factory.account
    )
    duration = timezone.now() - start
    duration_seconds = duration.total_seconds()
    logger.info(f"Duration of requirement_satisfaction_options={duration_seconds}")

    all_doccat_keys = list(options.keys())

    # 240724 mt: these are all document categories in the cataloge: 306 now, was 292 before
    # 2025-04-24: Updated these to fix flaky test
    all_doccat_keys_expected = [
        "ACCEPTANCE_OF_MORTAGE_OFFER",
        "ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY",
        "ADDITIONAL_COST_ACCOUNT",
        "AFFORDABILITY_CALCULATION",
        "AGREEMENT_CHARGE_IMMOVABLE_PROPERTY",
        "ANNUAL_REPORT_COMPANY",
        "ARCHITECT_CONTRACT",
        "ASSET_INCOME",
        "ASSUMPTION_DEBT_NOTICE",
        "AUDIT_REPORT_COMPANY",
        "AUTHORIZATION_EMAIL",
        "AUTHORIZATION_FOR_INQUIRIES",
        "BALANCE_SHEET_COMPANY",
        "BANK_DOCUMENT",
        "BANK_MISC",
        "BANK_STATEMENT_OF_INTEREST_CAPITAL",
        "BASE_CONTRACT",
        "BEKB_EKD06",
        "BEKB_EKD07",
        "BEKB_EKD08",
        "BEKB_EKD09",
        "BEKB_EKD10",
        "BEKB_EKD102",
        "BEKB_EKD103",
        "BEKB_EKD108",
        "BEKB_EKD11",
        "BEKB_EKD112",
        "BEKB_EKD118",
        "BEKB_EKD120",
        "BEKB_EKD121",
        "BEKB_EKD122",
        "BEKB_EKD123",
        "BEKB_EKD124",
        "BEKB_EKD125",
        "BEKB_EKD126",
        "BEKB_EKD134",
        "BEKB_EKD139",
        "BEKB_EKD142",
        "BEKB_EKD15",
        "BEKB_EKD16",
        "BEKB_EKD17",
        "BEKB_EKD18",
        "BEKB_EKD19",
        "BEKB_EKD20",
        "BEKB_EKD22",
        "BEKB_EKD24",
        "BEKB_EKD25",
        "BEKB_EKD26",
        "BEKB_EKD27",
        "BEKB_EKD28",
        "BEKB_EKD29",
        "BEKB_EKD31",
        "BEKB_EKD32",
        "BEKB_EKD56",
        "BEKB_EKD57",
        "BEKB_EKD59",
        "BEKB_EKD61",
        "BEKB_EKD62",
        "BEKB_EKD63",
        "BEKB_EKD64",
        "BEKB_EKD65",
        "BEKB_EKD66",
        "BEKB_EKD67",
        "BEKB_EKD68",
        "BEKB_EKD70",
        "BEKB_EKD71",
        "BEKB_EKD72",
        "BEKB_EKD73",
        "BEKB_EKD75",
        "BEKB_EKD79",
        "BEKB_EKD81",
        "BEKB_EKD82",
        "BEKB_EKD88",
        "BEKB_EKD89",
        "BEKB_EKD91",
        "BEKB_EKD92",
        "BEKB_EKD93",
        "BEKB_EKD94",
        "BEKB_EKD98",
        "BEKB_FIPLA_FORM",
        "BEKB_FIPLA_RESULT",
        "BEKB_TOTAL_ENGAGEMENT",
        "BILL_MISC",
        "BONUS_REGULATIONS",
        "BROKER_AUTHORIZATION",
        "BROKER_AUTHORIZATION_BANK_SECRECY",
        "BROKER_MANDATE",
        "BROKER_MISC",
        "BUDGET_CONTROL_COMPANY",
        "BUDGET_PLANNING",
        "BUILDING_DESCRIPTION",
        "BUILDING_RIGHTS_AGREEMENT",
        "BUILDING_RIGHTS_MISC",
        "BUILDING_RIGHT_INTEREST",
        "BUSINESS_PLAN_COMPANY",
        "CASH_FLOW_COMPANY",
        "CIVIL_STATUS_DOCUMENT",
        "COMPANY_MISC",
        "COMPENSATION_AGREEMENT",
        "CONDOMINIUM_MISC",
        "CONDOMINIUM_MIX",
        "CONFIRMATION_ALIMONY",
        "CONFIRMATION_OF_RESIDENCE",
        "CONSTRUCTION_ACCOUNT",
        "CONSTRUCTION_COMPANY_LIST",
        "CONSTRUCTION_CONTRACT",
        "CONSTRUCTION_COST_ESTIMATE",
        "CONSTRUCTION_COST_SUMMARY",
        "CONSTRUCTION_INSURANCE",
        "CONSTRUCTION_MISC",
        "CONSTRUCTION_PERMIT",
        "CONSTRUCTION_PLAN",
        "CONSTRUCTION_QUOTATION",
        "CONSTRUCTION_REGULATIONS",
        "CONSTRUCTION_REQUEST",
        "CONSUMER_LOAN",
        "CONTRACT_GENERAL_CONTRACTOR",
        "CONTRACT_OF_SALE",
        "CONTRACT_TOTAL_CONTRACTOR",
        "CORPORATE_BYLAWS",
        "CORRESPONDENCE_EMAIL",
        "CORRESPONDENCE_LETTER",
        "CORRESPONDENCE_NOTARY",
        "CREDITOR_CHANGE",
        "CREDITWORTHINESS_MISC",
        "CREDIT_CARD_BILL",
        "CREDIT_MISC",
        "CRIF_DATA_INFO",
        "CRIF_QUICK_CONSUMER_CHECK",
        "CRIF_TELEDATA",
        "CRIMINAL_RECORDS",
        "CV_CLIENT",
        "DAILY_ALLOWANCES",
        "DEATH_CERTIFICATE",
        "DEBT_CERTIFICATE",
        "DEBT_COLLECTION_INFORMATION",
        "DEBT_COLLECTION_INFORMATION_BILL",
        "DEBT_COLLECTION_INFORMATION_ORDER_BETREIBUNGSCHALTER_PLUS",
        "DEBT_COLLECTION_INFORMATION_ORDER_CRESURA",
        "DEBT_COLLECTION_INFORMATION_ORDER_TELEDATA",
        "DEBT_COLLECTION_INFORMATION_RECEIPT",
        "DEED_OF_GIFT",
        "DETERMINATION_OF_BENEFICIARY",
        "DIVIDENDS",
        "DIVORCE_CONVENTION",
        "DIVORCE_DECREE",
        "DIVORCE_DOCUMENT",
        "DIVORCE_MISC",
        "DIVORCE_SEPARATION_AGREEMENT",
        "DRAFT_CONTRACT_OF_SALE",
        "EASEMENT_CONTRACT",
        "EMPLOYMENT_CONFIRMATION",
        "EMPLOYMENT_CONTRACT",
        "ENERGY_CERTFICATE",
        "EXPENSE_REGULATIONS",
        "EXTRACT_AHV_ACCOUNT",
        "EXTRACT_FROM_LAND_REGISTER",
        "FILE_NOTE_FINANCING",
        "FINANCE_MISC",
        "FINANCIAL_STATEMENT_COMPANY",
        "FINANCING_CHECKLIST_DOCUMENTS",
        "FINANCING_CONFIRMATION",
        "FINANCING_FEES_LIST",
        "FINANCING_MISC",
        "FINANCING_OFFER",
        "FIN_REPORTING_COMPANY",
        "FOREIGN_NATIONAL_ID",
        "FOUNDATION_CERTIFICATE_CONDOMINIUM",
        "GEAK_CERTIFICATE",
        "GENERAL_INFO",
        "GIS_INFO",
        "HRA",
        "ID",
        "IDENTITY_MISC",
        "ID_OTHER",
        "IKO_CHECK",
        "INCOME_MISC",
        "INCOME_STATEMENT_COMPANY",
        "INCORPORATION_COMPANY",
        "INHERITANCE_ADVANCE",
        "INHERITANCE_CERTIFICATE",
        "INHERITANCE_MISC",
        "INHERITANCE_TESTAMENT",
        "INVESTMENT_PLAN_COMPANY",
        "IRREVOCABLE_PROMISES_TO_PAY",
        "LAND_REGISTER_BILL",
        "LAND_REGISTER_MISC",
        "LEASING_AGREEMENT",
        "LEGITIMATION_FDFA",
        "LETTER_COMMITMENT_NOTARY",
        "LIQUIDITY_PLAN_COMPANY",
        "LIST_OF_RENOVATIONS",
        "LOAN_AGREEMENT",
        "LOAN_AGREEMENT_SHAREHOLDER_COMPANY",
        "MARRIAGE_CONTRACT",
        "MEETING_MINUTES_CONDOMINIUM",
        "MINERGIE_CERTIFICATE",
        "MISC_CAT",
        "MORTGAGE_CONTRACT",
        "MORTGAGE_CONTRACT_CONFIRMATION",
        "MORTGAGE_DUE_NOTICE",
        "MORTGAGE_FRAMEWORK_CONTRACT",
        "MORTGAGE_MISC",
        "MORTGAGE_PRODUCT_CONFIRMATION",
        "MORTGAGE_REQUEST_FORM",
        "MORTGAGE_SUBORDINATION_AGREEMENT",
        "MORTGAGE_TERMINATION",
        "NOTARY_MISC",
        "PASSPORT_CH",
        "PASSPORT_DE",
        "PASSPORT_FR",
        "PASSPORT_IT",
        "PASSPORT_OTHER",
        "PAYSLIP",
        "PENSION3A_ACCOUNT",
        "PENSION3A_CREDIT_NOTE",
        "PENSION3A_INSURANCE_CONTRACT",
        "PENSION3A_INSURANCE_LETTER_REDEMPTION",
        "PENSION3A_INSURANCE_STATEMENT",
        "PENSION3_INSURANCE_APPLICATION",
        "PENSION3_REGULATIONS",
        "PENSION_CERTIFICATE",
        "PENSION_CERTIFICATE_AHV",
        "PENSION_CERTIFICATE_CLOSING_STATEMENT",
        "PENSION_CERTIFICATE_CREDIT_NOTE",
        "PENSION_CERTIFICATE_INFO",
        "PENSION_CERTIFICATE_LETTER",
        "PENSION_CERTIFICATE_SIM_ALL",
        "PENSION_CONTRIBUTION_CONFIRMATION",
        "PENSION_MISC",
        "PENSION_PAYMENT_AHV",
        "PENSION_PAYMENT_BVG",
        "PENSION_PLEDGE",
        "PENSION_REGULATIONS",
        "PENSION_SIMULATION1",
        "PENSION_WITHDRAWL",
        "PENSION_WITHDRAWL_PURPOSE_CONFIRMATION",
        "PERSON_MISC",
        "PILLAR_ONE_CALCULATION",
        "PILLAR_ONE_MISC",
        "PILLAR_THREE_MISC",
        "PILLAR_TWO_MISC",
        "PLAN_ANY",
        "PLAN_CADASTER",
        "PLAN_FLOOR",
        "PLAN_SITUATION",
        "PLATFORM_AGREEMENT",
        "PLEDGE_NOTICE",
        "PLR_CADASTRE",
        "POWER_OF_ATTORNEY",
        "PREPAYMENT_PENALTY",
        "PROJECT_BUDGET",
        "PROOF_OF_FUNDS",
        "PROOF_OF_INCOME",
        "PROPERTY_ACCOUNTS",
        "PROPERTY_BILL",
        "PROPERTY_DOCUMENTATION",
        "PROPERTY_INFO",
        "PROPERTY_INSURANCE",
        "PROPERTY_MISC",
        "PROPERTY_PHOTOS",
        "PROPERTY_TRUSTEE_CONTRACT",
        "PROPERTY_VALUATION",
        "PROPERTY_VALUATION_GOV",
        "PURCHASE_CONTRACT_REGISTRATION",
        "PURCHASE_MISC",
        "PURCHASE_PRICE_LIST_PROPERTY",
        "REGISTRATION_LAND_REGISTER",
        "RENOVATIONS",
        "RENOVATION_FUND",
        "RENTAL_MISC",
        "RESERVATION_CONTRACT",
        "RESERVATION_PAYMENT",
        "RESIDENCE_PERMIT",
        "RETIREMENT_ANALYSIS",
        "RISK_LIFE_INSURANCE",
        "SAFETY_CERTIFICATE_ELECTRICAL",
        "SALARY_ACCOUNT",
        "SALARY_BONUS",
        "SALARY_CERTIFICATE",
        "SALARY_CONFIRMATION",
        "SALARY_CONFIRMATION_13",
        "SALARY_CONFIRMATION_FORM",
        "SALES_DOCUMENTATION",
        "SCHUFA_BONITAETSCHECK",
        "SHARE_REGISTER",
        "SHORT_TIME_WORK",
        "SPECIMEN_SIGNATURE",
        "STATEMENT_OF_ASSETS",
        "STATEMENT_PENSION",
        "STATEMENT_VALUE_RATIO_PROPERTY",
        "TAX_ASSESSMENT",
        "TAX_ATTACHMENTS",
        "TAX_AT_SOURCE_CONFIRMATION",
        "TAX_BILL",
        "TAX_BUDGET",
        "TAX_CALCULATION",
        "TAX_DEBT_INVENTORY",
        "TAX_DECLARATION",
        "TAX_LIST_FINANCIAL_ASSETS",
        "TAX_MISC",
        "TENANCY_AGREEMENT",
        "TENANT_DIRECTORY",
        "TERMS_AND_CONDITIONS",
        "TRANSFER_AGREEMENT",
        "TRANSFER_OF_SECURITY",
        "TURNOVER_COMPANY",
        "UNEMPLOYMENT_SALARY_CERTIFICATE",
        "USER_REGULATIONS_CONDOMINIUM",
        "US_PERSON_FORM",
        "VESTED_BENEFITS_ACCOUNT",
        "VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT",
        "VESTED_BENEFITS_STATEMENT",
        "VOLUME_CALCULATION_SIA",
        "WORLD_CHECK",
        "ZEK_CHECK",
        "ZEK_INFO",
    ]

    # If the test fails, log the differences to help debugging
    if all_doccat_keys != all_doccat_keys_expected:
        logger.error("Document category lists don't match!")
        logger.error(f"Number of actual categories: {len(all_doccat_keys)}")
        logger.error(f"Number of expected categories: {len(all_doccat_keys_expected)}")

        # Find missing and extra categories
        missing_categories = set(all_doccat_keys_expected) - set(all_doccat_keys)
        extra_categories = set(all_doccat_keys) - set(all_doccat_keys_expected)

        if missing_categories:
            logger.error(f"Missing categories: {sorted(missing_categories)}")
        if extra_categories:
            logger.error(f"Extra categories: {sorted(extra_categories)}")

        # Find first difference in order
        for i, (actual, expected) in enumerate(
            zip(all_doccat_keys, all_doccat_keys_expected)
        ):
            if actual != expected:
                logger.error(
                    f"First difference at index {i}: '{actual}' != '{expected}'"
                )
                break

    assert all_doccat_keys == all_doccat_keys_expected

    # Get all document categories that have collateral requirements
    doccat_with_options = sorted(
        [doccat_key for doccat_key, options in options.items() if len(options) > 0]
    )

    # 240724 mt: these are all document categories that require a collateral
    doccat_with_options_expected = [
        "ADDITIONAL_COST_ACCOUNT",
        "ARCHITECT_CONTRACT",
        "ASSUMPTION_DEBT_NOTICE",
        "BEKB_EKD08",
        "BEKB_EKD09",
        "BEKB_EKD10",
        "BEKB_EKD102",
        "BEKB_EKD103",
        "BEKB_EKD11",
        "BEKB_EKD17",
        "BEKB_EKD18",
        "BEKB_EKD20",
        "BEKB_EKD26",
        "BEKB_EKD27",
        "BEKB_EKD28",
        "BEKB_EKD29",
        "BEKB_EKD31",
        "BEKB_EKD32",
        "BEKB_EKD91",
        "BEKB_EKD92",
        "BEKB_EKD93",
        "BEKB_EKD94",
        "BEKB_EKD98",
        "BUILDING_DESCRIPTION",
        "BUILDING_RIGHTS_AGREEMENT",
        "CONSTRUCTION_ACCOUNT",
        "CONSTRUCTION_COMPANY_LIST",
        "CONSTRUCTION_CONTRACT",
        "CONSTRUCTION_COST_ESTIMATE",
        "CONSTRUCTION_COST_SUMMARY",
        "CONSTRUCTION_INSURANCE",
        "CONSTRUCTION_PERMIT",
        "CONSTRUCTION_PLAN",
        "CONSTRUCTION_QUOTATION",
        "CONTRACT_GENERAL_CONTRACTOR",
        "CONTRACT_OF_SALE",
        "CONTRACT_TOTAL_CONTRACTOR",
        "EASEMENT_CONTRACT",
        "EXTRACT_FROM_LAND_REGISTER",
        "FOUNDATION_CERTIFICATE_CONDOMINIUM",
        "GEAK_CERTIFICATE",
        "LIST_OF_RENOVATIONS",
        "MEETING_MINUTES_CONDOMINIUM",
        "MINERGIE_CERTIFICATE",
        "PENSION3A_INSURANCE_LETTER_REDEMPTION",
        "PENSION3A_INSURANCE_STATEMENT",
        "PLAN_ANY",
        "PLAN_CADASTER",
        "PLAN_FLOOR",
        "PLAN_SITUATION",
        "PLR_CADASTRE",
        "PROJECT_BUDGET",
        "PROPERTY_ACCOUNTS",
        "PROPERTY_BILL",
        "PROPERTY_INSURANCE",
        "PROPERTY_TRUSTEE_CONTRACT",
        "PROPERTY_VALUATION",
        "PROPERTY_VALUATION_GOV",
        "RENOVATIONS",
        "RENOVATION_FUND",
        "RENTAL_MISC",
        "STATEMENT_VALUE_RATIO_PROPERTY",
        "TENANCY_AGREEMENT",
        "TENANT_DIRECTORY",
        "USER_REGULATIONS_CONDOMINIUM",
    ]

    # If the test fails, log the differences to help debugging
    if doccat_with_options != doccat_with_options_expected:
        logger.error("Document categories with options don't match!")
        logger.error(
            f"Number of actual categories with options: {len(doccat_with_options)}"
        )
        logger.error(
            f"Number of expected categories with options: {len(doccat_with_options_expected)}"
        )

        # Find missing and extra categories
        missing_categories = set(doccat_with_options_expected) - set(
            doccat_with_options
        )
        extra_categories = set(doccat_with_options) - set(doccat_with_options_expected)

        if missing_categories:
            logger.error(
                f"Missing categories with options: {sorted(missing_categories)}"
            )
        if extra_categories:
            logger.error(f"Extra categories with options: {sorted(extra_categories)}")

        # Find first difference in order
        for i, (actual, expected) in enumerate(
            zip(doccat_with_options, doccat_with_options_expected)
        ):
            if actual != expected:
                logger.error(
                    f"First difference at index {i}: '{actual}' != '{expected}'"
                )
                break

    assert doccat_with_options == doccat_with_options_expected
    assert (
        duration_seconds < 0.02
    ), f"get_collateral_requirements_satisfaction_options_for_account took {duration_seconds:.4f} seconds, which exceeds the 0.01 second threshold. This may indicate performance degradation."


def test_get_collaterals_by_businesscase(db):
    bekb_account_factory = BekbAccountFactoryFaker()
    d1c = bekb_account_factory.create_sample_dossier_personenversicherung()

    business_cases = list(
        BusinessCase.objects.filter(
            account=bekb_account_factory.account, business_partner=d1c.business_partner
        ).all()
    )
    assert len(business_cases) == 1
    collaterals_set = get_collaterals_by_businesscase(
        bekb_account_factory.account, d1c.business_partner, business_cases[0]
    )
    collaterals = list(collaterals_set)
    assert len(collaterals) == 1
    assert (
        collaterals[0].collateral_type.key
        == bekb_account_factory.attribute_collateral_personenversicherung.key
    )


def test_create_sample_dossier_two_hauptdeckung(db):
    bekb_account_factory = BekbAccountFactoryFaker()
    d = bekb_account_factory.create_sample_dossier_two_hauptdeckung()
    assert d
    assert d.business_case
    collaterals = get_collaterals_by_businesscase(
        d.account, d.business_partner, d.business_case
    )
    assert len(collaterals) == 2
    add_some_fake_semantic_documents(
        d.dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
    )


def test_load_bekb_attributes(db):
    bfac = BekbAccountFactoryFaker()
    load_bekb_attributes(bfac.account.key)

    attributes = list(Attribute.objects.filter(account=bfac.account).all())
    assert attributes
    assert len(attributes) == 97


@pytest.mark.django_db
def test_user_with_dossier_access_can_obtain_cdp_token(bekb_cdp_dossier):
    token = create_token(
        "BEKB API given name",
        "BEKB API family name",
        "<EMAIL>",
        roles=[settings.BEKB_API_ROLE, settings.NAME_OF_ROLE_MANAGER_FROM_JWT],
        account_key=bekb_cdp_dossier.account.key,  # had to explicitly set the account key to this so access check does not fail
    )

    bekbe_api_client = AuthenticatedClient(token)

    response = bekbe_api_client.get(
        reverse("bekb-api:cdp-dossier-access-token", args=[bekb_cdp_dossier.uuid])
    )
    assert response.status_code == 200
    jwt = JWT(
        jwt=response.json(),
        key=JWK.from_json(settings.INSTANCE_SIGNING_KEY),
        expected_type="JWS",
    )
    claims = json.loads(jwt.claims)
    assert claims["dossier_uuid"] == str(bekb_cdp_dossier.uuid)
    assert claims["iss"] == "dms"
    assert claims["aud"] == "cdp"
    assert claims["exp"] > claims["iat"]
    assert claims["field_set"] == bekb_cdp_dossier.account.cdp_field_set


def test_user_with_manager_role_but_no_api_user_role_cannot_access_cdp_token(
    db, bekb_cdp_dossier
):
    token = create_token(
        "BEKB user with manager role but no API role",
        "BEKB user with manager role but no API role",
        "<EMAIL>",
        roles=[settings.NAME_OF_ROLE_MANAGER_FROM_JWT],
        account_key=bekb_cdp_dossier.account.key,  # had to explicitly set the account key
    )
    bekbe_api_client = AuthenticatedClient(token)
    response = bekbe_api_client.get(
        reverse("bekb-api:cdp-dossier-access-token", args=[bekb_cdp_dossier.uuid])
    )
    assert response.status_code == 401


def test_user_with_api_role_but_no_manager_role_cannot_access_cdp_token(
    db, bekb_cdp_dossier
):
    token = create_token(
        "BEKB user with API role but no manager role",
        "BEKB user with API role but no manager role",
        "<EMAIL>",
        roles=[settings.BEKB_API_ROLE],
        account_key=bekb_cdp_dossier.account.key,  # had to explicitly set the account key
    )
    bekbe_api_client = AuthenticatedClient(token)
    response = bekbe_api_client.get(
        reverse("bekb-api:cdp-dossier-access-token", args=[bekb_cdp_dossier.uuid])
    )
    assert (
        response.status_code == 404
    )  # access to the api, but no dossiers are obtained after the access check


def test_list_dossiers(db):
    bekb_account_factory = BekbAccountFactoryFaker()
    account = bekb_account_factory.account

    token = create_token(
        "BEKB user with API role but no manager role",
        "BEKB user with API role but no manager role",
        "<EMAIL>",
        roles=[settings.BEKB_API_ROLE],
        account_key=account.key,  # had to explicitly set the account key
    )
    bekbe_api_client = AuthenticatedClient(token)

    test_business_parkey = "my test parkey"

    dossiers = []
    for dossier in range(3):
        d = bekb_account_factory.create_dossier(business_parkey=test_business_parkey)
        dossiers.append(d)

    d_prolongation = bekb_account_factory.create_sample_dossier_prolongation()
    d_prolongation.business_partner = dossiers[0].business_partner
    d_prolongation.save()

    # Make sure all dossiers have a partner_partner set from the start
    for bekb_dossier in BEKBDossierProperties.objects.filter(
        business_partner__parkey=test_business_parkey
    ):
        assert bekb_dossier.partner_partner is not None

    response = bekbe_api_client.get(
        reverse(
            "bekb-api:list-dossiers",
            kwargs={
                "account_name": account.key,
                "business_parkey": test_business_parkey,
            },
        )
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[bekb_schemas.DossierShow]).validate_json(response.content)

    assert len(parsed) == 4

    # Dossiers are sorted by creation date desc. So the last one added comes first
    assert parsed[0].businesscase_type_key == "PROLONGATION"
    assert parsed[0].assignee_firstname
    assert parsed[0].assignee_lastname
    assert parsed[0].assignee_username
    assert parsed[0].fico_firstname
    assert parsed[0].fico_lastname
    assert parsed[0].fico_username


def test_load_document_category2ekd_mappings_mortage():
    # Test MORTGAGE instance type
    result = load_document_category2ekd_mappings(BekbInstanceType.MORTGAGE)
    # assert len(result) == 316
    assert result == {
        "ACCEPTANCE_OF_MORTAGE_OFFER": "EKD133",
        "ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY": "EKD130",
        "ADDITIONAL_COST_ACCOUNT": "EKD42",
        "AFFORDABILITY_CALCULATION": "EKD133",
        "AGREEMENT_CHARGE_IMMOVABLE_PROPERTY": "EKD133",
        "ANNUAL_REPORT_COMPANY": "EKD130",
        "ARCHITECT_CONTRACT": "EKD34",
        "ASSET_INCOME": "EKD51",
        "ASSUMPTION_DEBT_NOTICE": "EKD33",
        "AUDIT_REPORT_COMPANY": "EKD113",
        "AUTHORIZATION_EMAIL": "EKD133",
        "AUTHORIZATION_FOR_INQUIRIES": "EKD133",
        "BALANCE_SHEET_COMPANY": "EKD130",
        "BANK_DOCUMENT": "EKD55",
        "BANK_MISC": "EKD55",
        "BANK_STATEMENT_OF_INTEREST_CAPITAL": "EKD55",
        "BASE_CONTRACT": "EKD133",
        "BEKB_EKD06": "EKD06",
        "BEKB_EKD07": "EKD07",
        "BEKB_EKD08": "EKD08",
        "BEKB_EKD09": "EKD09",
        "BEKB_EKD10": "EKD10",
        "BEKB_EKD102": "EKD102",
        "BEKB_EKD103": "EKD103",
        "BEKB_EKD108": "EKD108",
        "BEKB_EKD11": "EKD11",
        "BEKB_EKD112": "EKD112",
        "BEKB_EKD118": "EKD118",
        "BEKB_EKD120": "EKD120",
        "BEKB_EKD121": "EKD121",
        "BEKB_EKD122": "EKD122",
        "BEKB_EKD123": "EKD123",
        "BEKB_EKD124": "EKD124",
        "BEKB_EKD125": "EKD125",
        "BEKB_EKD126": "EKD126",
        "BEKB_EKD134": "EKD134",
        "BEKB_FIPLA_RESULT": "EKD56",  # EKD56 is correct for mortgage and EKD138 is correct for fipla
        "BEKB_EKD139": "EKD139",
        "BEKB_EKD142": "EKD142",
        "BEKB_EKD15": "EKD15",
        "BEKB_EKD16": "EKD16",
        "BEKB_EKD17": "EKD17",
        "BEKB_EKD18": "EKD18",
        "BEKB_EKD19": "EKD19",
        "BEKB_EKD20": "EKD20",
        "BEKB_EKD22": "EKD22",
        "BEKB_EKD24": "EKD24",
        "BEKB_EKD25": "EKD25",
        "BEKB_EKD26": "EKD26",
        "BEKB_EKD27": "EKD27",
        "BEKB_EKD28": "EKD28",
        "BEKB_EKD29": "EKD29",
        "BEKB_EKD31": "EKD31",
        "BEKB_EKD32": "EKD32",
        "BEKB_EKD56": "EKD56",
        "BEKB_EKD57": "EKD57",
        "BEKB_EKD59": "EKD59",
        "BEKB_EKD61": "EKD61",
        "BEKB_EKD62": "EKD62",
        "BEKB_EKD63": "EKD63",
        "BEKB_EKD64": "EKD64",
        "BEKB_EKD65": "EKD65",
        "BEKB_EKD66": "EKD66",
        "BEKB_EKD67": "EKD67",
        "BEKB_EKD68": "EKD68",
        "BEKB_EKD70": "EKD70",
        "BEKB_EKD71": "EKD71",
        "BEKB_EKD72": "EKD72",
        "BEKB_EKD73": "EKD73",
        "BEKB_EKD75": "EKD75",
        "BEKB_EKD79": "EKD79",
        "BEKB_EKD81": "EKD81",
        "BEKB_EKD82": "EKD82",
        "BEKB_EKD88": "EKD88",
        "BEKB_EKD89": "EKD89",
        "BEKB_EKD91": "EKD91",
        "BEKB_EKD92": "EKD92",
        "BEKB_EKD93": "EKD93",
        "BEKB_EKD94": "EKD94",
        "BEKB_EKD98": "EKD98",
        "BEKB_FIPLA_FORM": "EKD120",
        "BEKB_TOTAL_ENGAGEMENT": "EKD55",
        "BILL_MISC": "EKD131",
        "BONUS_REGULATIONS": "EKD51",
        "BROKER_AUTHORIZATION": "EKD119",
        "BROKER_AUTHORIZATION_BANK_SECRECY": "EKD119",
        "BROKER_MANDATE": "EKD119",
        "BROKER_MISC": "EKD119",
        "BUDGET_CONTROL_COMPANY": "EKD114",
        "BUDGET_PLANNING": "EKD114",
        "BUILDING_DESCRIPTION": "EKD36",
        "BUILDING_RIGHTS_AGREEMENT": "EKD50",
        "BUILDING_RIGHTS_MISC": "EKD127",
        "BUILDING_RIGHT_INTEREST": "EKD127",
        "BUSINESS_PLAN_COMPANY": "EKD115",
        "CASH_FLOW_COMPANY": "EKD130",
        "CIVIL_STATUS_DOCUMENT": "EKD131",
        "COMPANY_MISC": "EKD130",
        "COMPENSATION_AGREEMENT": "EKD51",
        "CONDOMINIUM_MISC": "EKD127",
        "CONDOMINIUM_MIX": "EKD127",
        "CONFIRMATION_ALIMONY": "EKD51",
        "CONFIRMATION_OF_RESIDENCE": "EKD131",
        "CONSTRUCTION_ACCOUNT": "EKD42",
        "CONSTRUCTION_COMPANY_LIST": "EKD46",
        "CONSTRUCTION_CONTRACT": "EKD34",
        "CONSTRUCTION_COST_ESTIMATE": "EKD44",
        "CONSTRUCTION_COST_SUMMARY": "EKD42",
        "CONSTRUCTION_INSURANCE": "EKD38",
        "CONSTRUCTION_MISC": "EKD127",
        "CONSTRUCTION_PERMIT": "EKD45",
        "CONSTRUCTION_PLAN": "EKD35",
        "CONSTRUCTION_QUOTATION": "EKD44",
        "CONSTRUCTION_REGULATIONS": "EKD127",
        "CONSTRUCTION_REQUEST": "EKD127",
        "CONSUMER_LOAN": "EKD51",
        "CONTRACT_GENERAL_CONTRACTOR": "EKD34",
        "CONTRACT_OF_SALE": "EKD33",
        "CONTRACT_TOTAL_CONTRACTOR": "EKD34",
        "CORPORATE_BYLAWS": "EKD130",
        "CORRESPONDENCE_EMAIL": "EKD133",
        "CORRESPONDENCE_LETTER": "EKD73",
        "CORRESPONDENCE_NOTARY": "EKD133",
        "CREDITOR_CHANGE": "EKD133",
        "CREDITWORTHINESS_MISC": "EKD74",
        "CREDIT_CARD_BILL": "EKD51",
        "CREDIT_MISC": "EKD131",
        "CRIF_DATA_INFO": "EKD74",
        "CRIF_QUICK_CONSUMER_CHECK": "EKD74",
        "CRIF_TELEDATA": "EKD74",
        "CRIMINAL_RECORDS": "EKD131",
        "CV_CLIENT": "EKD131",
        "DAILY_ALLOWANCES": "EKD51",
        "DEATH_CERTIFICATE": "EKD131",
        "DEBT_CERTIFICATE": "EKD133",
        "DEBT_COLLECTION_INFORMATION": "EKD60",
        "DEBT_COLLECTION_INFORMATION_BILL": "EKD60",
        "DEBT_COLLECTION_INFORMATION_ORDER_BETREIBUNGSCHALTER_PLUS": "EKD60",
        "DEBT_COLLECTION_INFORMATION_ORDER_CRESURA": "EKD60",
        "DEBT_COLLECTION_INFORMATION_ORDER_TELEDATA": "EKD60",
        "DEBT_COLLECTION_INFORMATION_RECEIPT": "EKD60",
        "DEED_OF_GIFT": "EKD131",
        "DETERMINATION_OF_BENEFICIARY": "EKD133",
        "DIVIDENDS": "EKD51",
        "DIVORCE_CONVENTION": "EKD58",
        "DIVORCE_DECREE": "EKD58",
        "DIVORCE_DOCUMENT": "EKD58",
        "DIVORCE_MISC": "EKD58",
        "DIVORCE_SEPARATION_AGREEMENT": "EKD58",
        "DRAFT_CONTRACT_OF_SALE": "EKD127",
        "EASEMENT_CONTRACT": "EKD33",
        "EMPLOYMENT_CONFIRMATION": "EKD51",
        "EMPLOYMENT_CONTRACT": "EKD51",
        "ENERGY_CERTFICATE": "EKD127",
        "EXPENSE_REGULATIONS": "EKD51",
        "EXTRACT_AHV_ACCOUNT": "EKD132",
        "EXTRACT_FROM_LAND_REGISTER": "EKD39",
        "FILE_NOTE_FINANCING": "EKD133",
        "FINANCE_MISC": "EKD131",
        "FINANCIAL_STATEMENT_COMPANY": "EKD113",
        "FINANCING_CHECKLIST_DOCUMENTS": "EKD133",
        "FINANCING_CONFIRMATION": "EKD133",
        "FINANCING_FEES_LIST": "EKD133",
        "FINANCING_MISC": "EKD133",
        "FINANCING_OFFER": "EKD133",
        "FIN_REPORTING_COMPANY": "EKD130",
        "FOREIGN_NATIONAL_ID": "EKD131",
        "FOUNDATION_CERTIFICATE_CONDOMINIUM": "EKD49",
        "GEAK_CERTIFICATE": "EKD41",
        "GENERAL_INFO": "EKD133",
        "GIS_INFO": "EKD127",
        "HRA": "EKD130",
        "ID": "EKD131",
        "IDENTITY_MISC": "EKD131",
        "ID_OTHER": "EKD131",
        "IKO_CHECK": "EKD74",
        "INCOME_MISC": "EKD51",
        "INCOME_STATEMENT_COMPANY": "EKD130",
        "INCORPORATION_COMPANY": "EKD130",
        "INHERITANCE_ADVANCE": "EKD131",
        "INHERITANCE_CERTIFICATE": "EKD131",
        "INHERITANCE_MISC": "EKD131",
        "INHERITANCE_TESTAMENT": "EKD131",
        "INVESTMENT_PLAN_COMPANY": "EKD114",
        "IRREVOCABLE_PROMISES_TO_PAY": "EKD116",
        "LAND_REGISTER_BILL": "EKD127",
        "LAND_REGISTER_MISC": "EKD133",
        "LEASING_AGREEMENT": "EKD51",
        "LEGITIMATION_FDFA": "EKD131",
        "LETTER_COMMITMENT_NOTARY": "EKD133",
        "LIQUIDITY_PLAN_COMPANY": "EKD114",
        "LIST_OF_RENOVATIONS": "EKD44",
        "LOAN_AGREEMENT": "EKD55",
        "LOAN_AGREEMENT_SHAREHOLDER_COMPANY": "EKD130",
        "MARRIAGE_CONTRACT": "EKD131",
        "MEETING_MINUTES_CONDOMINIUM": "EKD47",
        "MINERGIE_CERTIFICATE": "EKD40",
        "MISC_CAT": "EKD133",
        "MORTGAGE_CONTRACT": "EKD133",
        "MORTGAGE_CONTRACT_CONFIRMATION": "EKD133",
        "MORTGAGE_DUE_NOTICE": "EKD133",
        "MORTGAGE_FRAMEWORK_CONTRACT": "EKD133",
        "MORTGAGE_MISC": "EKD133",
        "MORTGAGE_PRODUCT_CONFIRMATION": "EKD78",
        "MORTGAGE_REQUEST_FORM": "EKD133",
        "MORTGAGE_SUBORDINATION_AGREEMENT": "EKD133",
        "MORTGAGE_TERMINATION": "EKD133",
        "NOTARY_MISC": "EKD133",
        "PASSPORT_CH": "EKD131",
        "PASSPORT_DE": "EKD131",
        "PASSPORT_FR": "EKD131",
        "PASSPORT_IT": "EKD131",
        "PASSPORT_OTHER": "EKD131",
        "PAYSLIP": "EKD51",
        "PENSION3A_ACCOUNT": "EKD55",
        "PENSION3A_CREDIT_NOTE": "EKD55",
        "PENSION3A_INSURANCE_CONTRACT": "EKD132",
        "PENSION3A_INSURANCE_LETTER_REDEMPTION": "EKD76",
        "PENSION3A_INSURANCE_STATEMENT": "EKD76",
        "PENSION3_INSURANCE_APPLICATION": "EKD132",
        "PENSION3_REGULATIONS": "EKD132",
        "PENSION_CERTIFICATE": "EKD54",
        "PENSION_CERTIFICATE_AHV": "EKD132",
        "PENSION_CERTIFICATE_CLOSING_STATEMENT": "EKD54",
        "PENSION_CERTIFICATE_CREDIT_NOTE": "EKD54",
        "PENSION_CERTIFICATE_INFO": "EKD54",
        "PENSION_CERTIFICATE_LETTER": "EKD54",
        "PENSION_CERTIFICATE_SIM_ALL": "EKD54",
        "PENSION_CONTRIBUTION_CONFIRMATION": "EKD132",
        "PENSION_MISC": "EKD132",
        "PENSION_PAYMENT_AHV": "EKD51",
        "PENSION_PAYMENT_BVG": "EKD51",
        "PENSION_PLEDGE": "EKD133",
        "PENSION_REGULATIONS": "EKD54",
        "PENSION_SIMULATION1": "EKD132",
        "PENSION_WITHDRAWL": "EKD54",
        "PENSION_WITHDRAWL_PURPOSE_CONFIRMATION": "EKD132",
        "PERSON_MISC": "EKD131",
        "PILLAR_ONE_CALCULATION": "EKD132",
        "PILLAR_ONE_MISC": "EKD132",
        "PILLAR_THREE_MISC": "EKD132",
        "PILLAR_TWO_MISC": "EKD54",
        "PLAN_ANY": "EKD35",
        "PLAN_CADASTER": "EKD37",
        "PLAN_FLOOR": "EKD35",
        "PLAN_SITUATION": "EKD37",
        "PLATFORM_AGREEMENT": "EKD119",
        "PLEDGE_NOTICE": "EKD133",
        "PLR_CADASTRE": "EKD39",
        "POWER_OF_ATTORNEY": "EKD133",
        "PREPAYMENT_PENALTY": "EKD133",
        "PROJECT_BUDGET": "EKD44",
        "PROOF_OF_FUNDS": "EKD55",
        "PROOF_OF_INCOME": "EKD51",
        "PROPERTY_ACCOUNTS": "EKD47",
        "PROPERTY_BILL": "EKD44",
        "PROPERTY_DOCUMENTATION": "EKD129",
        "PROPERTY_INFO": "EKD127",
        "PROPERTY_INSURANCE": "EKD38",
        "PROPERTY_MISC": "EKD127",
        "PROPERTY_PHOTOS": "EKD128",
        "PROPERTY_TRUSTEE_CONTRACT": "EKD12",
        "PROPERTY_VALUATION": "EKD43",
        "PROPERTY_VALUATION_GOV": "EKD43",
        "PURCHASE_CONTRACT_REGISTRATION": "EKD127",
        "PURCHASE_MISC": "EKD127",
        "PURCHASE_PRICE_LIST_PROPERTY": "EKD129",
        "REGISTRATION_LAND_REGISTER": "EKD133",
        "RENOVATIONS": "EKD44",
        "RENOVATION_FUND": "EKD47",
        "RENTAL_MISC": "EKD47",
        "RESERVATION_CONTRACT": "EKD127",
        "RESERVATION_PAYMENT": "EKD127",
        "RESIDENCE_PERMIT": "EKD131",
        "RETIREMENT_ANALYSIS": "EKD132",
        "RISK_LIFE_INSURANCE": "EKD132",
        "SAFETY_CERTIFICATE_ELECTRICAL": "EKD127",
        "SALARY_ACCOUNT": "EKD51",
        "SALARY_BONUS": "EKD51",
        "SALARY_CERTIFICATE": "EKD51",
        "SALARY_CONFIRMATION": "EKD51",
        "SALARY_CONFIRMATION_13": "EKD51",
        "SALARY_CONFIRMATION_FORM": "EKD51",
        "SALES_DOCUMENTATION": "EKD129",
        "SCHUFA_BONITAETSCHECK": "EKD74",
        "SHARE_REGISTER": "EKD130",
        "SHORT_TIME_WORK": "EKD51",
        "SPECIMEN_SIGNATURE": "EKD133",
        "STATEMENT_OF_ASSETS": "EKD55",
        "STATEMENT_PENSION": "EKD54",
        "STATEMENT_VALUE_RATIO_PROPERTY": "EKD49",
        "TAX_ASSESSMENT": "EKD53",
        "TAX_ATTACHMENTS": "EKD53",
        "TAX_AT_SOURCE_CONFIRMATION": "EKD53",
        "TAX_BILL": "EKD53",
        "TAX_BUDGET": "EKD53",
        "TAX_CALCULATION": "EKD53",
        "TAX_DEBT_INVENTORY": "EKD53",
        "TAX_DECLARATION": "EKD53",
        "TAX_LIST_FINANCIAL_ASSETS": "EKD53",
        "TAX_MISC": "EKD53",
        "TENANCY_AGREEMENT": "EKD47",
        "TENANT_DIRECTORY": "EKD47",
        "TERMS_AND_CONDITIONS": "EKD133",
        "TRANSFER_AGREEMENT": "EKD133",
        "TRANSFER_OF_SECURITY": "EKD133",
        "TURNOVER_COMPANY": "EKD130",
        "UNEMPLOYMENT_SALARY_CERTIFICATE": "EKD51",
        "USER_REGULATIONS_CONDOMINIUM": "EKD48",
        "US_PERSON_FORM": "EKD133",
        "VESTED_BENEFITS_ACCOUNT": "EKD55",
        "VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT": "EKD55",
        "VESTED_BENEFITS_STATEMENT": "EKD132",
        "VOLUME_CALCULATION_SIA": "EKD127",
        "WORLD_CHECK": "EKD131",
        "ZEK_CHECK": "EKD74",
        "ZEK_INFO": "EKD74",
    }


def test_load_document_category2ekd_mappings_fipla():

    # Test FIPLA instance type
    result = load_document_category2ekd_mappings(BekbInstanceType.FIPLA)
    # assert len(result) == 316
    assert result == {
        "ACCEPTANCE_OF_MORTAGE_OFFER": "EKD139",
        "ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY": "EKD139",
        "ADDITIONAL_COST_ACCOUNT": "EKD139",
        "AFFORDABILITY_CALCULATION": "EKD139",
        "AGREEMENT_CHARGE_IMMOVABLE_PROPERTY": "EKD139",
        "ANNUAL_REPORT_COMPANY": "EKD139",
        "ARCHITECT_CONTRACT": "EKD139",
        "ASSET_INCOME": "EKD139",
        "ASSUMPTION_DEBT_NOTICE": "EKD139",
        "AUDIT_REPORT_COMPANY": "EKD139",
        "AUTHORIZATION_EMAIL": "EKD139",
        "AUTHORIZATION_FOR_INQUIRIES": "EKD120",
        "BALANCE_SHEET_COMPANY": "EKD139",
        "BANK_DOCUMENT": "EKD124",
        "BANK_MISC": "EKD124",
        "BANK_STATEMENT_OF_INTEREST_CAPITAL": "EKD124",
        "BASE_CONTRACT": "EKD139",
        "BEKB_EKD06": "EKD139",
        "BEKB_EKD07": "EKD139",
        "BEKB_EKD08": "EKD139",
        "BEKB_EKD09": "EKD139",
        "BEKB_EKD10": "EKD139",
        "BEKB_EKD102": "EKD139",
        "BEKB_EKD103": "EKD139",
        "BEKB_EKD108": "EKD139",
        "BEKB_EKD11": "EKD139",
        "BEKB_EKD112": "EKD139",
        "BEKB_EKD118": "EKD139",
        "BEKB_EKD120": "EKD120",
        "BEKB_EKD121": "EKD121",
        "BEKB_EKD122": "EKD122",
        "BEKB_EKD123": "EKD123",
        "BEKB_EKD124": "EKD124",
        "BEKB_EKD125": "EKD139",
        "BEKB_EKD126": "EKD139",
        "BEKB_EKD134": "EKD134",
        "BEKB_FIPLA_RESULT": "EKD138",
        "BEKB_EKD139": "EKD139",
        "BEKB_EKD142": "EKD139",
        "BEKB_EKD15": "EKD139",
        "BEKB_EKD16": "EKD139",
        "BEKB_EKD17": "EKD139",
        "BEKB_EKD18": "EKD139",
        "BEKB_EKD19": "EKD139",
        "BEKB_EKD20": "EKD139",
        "BEKB_EKD22": "EKD139",
        "BEKB_EKD24": "EKD139",
        "BEKB_EKD25": "EKD139",
        "BEKB_EKD26": "EKD139",
        "BEKB_EKD27": "EKD139",
        "BEKB_EKD28": "EKD139",
        "BEKB_EKD29": "EKD139",
        "BEKB_EKD31": "EKD139",
        "BEKB_EKD32": "EKD139",
        "BEKB_EKD56": "EKD56",
        "BEKB_EKD57": "EKD139",
        "BEKB_EKD59": "EKD139",
        "BEKB_EKD61": "EKD139",
        "BEKB_EKD62": "EKD139",
        "BEKB_EKD63": "EKD139",
        "BEKB_EKD64": "EKD139",
        "BEKB_EKD65": "EKD139",
        "BEKB_EKD66": "EKD139",
        "BEKB_EKD67": "EKD139",
        "BEKB_EKD68": "EKD139",
        "BEKB_EKD70": "EKD139",
        "BEKB_EKD71": "EKD139",
        "BEKB_EKD72": "EKD139",
        "BEKB_EKD73": "EKD139",
        "BEKB_EKD75": "EKD139",
        "BEKB_EKD79": "EKD139",
        "BEKB_EKD81": "EKD139",
        "BEKB_EKD82": "EKD139",
        "BEKB_EKD88": "EKD139",
        "BEKB_EKD89": "EKD139",
        "BEKB_EKD91": "EKD139",
        "BEKB_EKD92": "EKD139",
        "BEKB_EKD93": "EKD139",
        "BEKB_EKD94": "EKD139",
        "BEKB_EKD98": "EKD139",
        "BEKB_FIPLA_FORM": "EKD120",
        "BEKB_TOTAL_ENGAGEMENT": "EKD124",
        "BILL_MISC": "EKD139",
        "BONUS_REGULATIONS": "EKD139",
        "BROKER_AUTHORIZATION": "EKD139",
        "BROKER_AUTHORIZATION_BANK_SECRECY": "EKD139",
        "BROKER_MANDATE": "EKD139",
        "BROKER_MISC": "EKD139",
        "BUDGET_CONTROL_COMPANY": "EKD139",
        "BUDGET_PLANNING": "EKD139",
        "BUILDING_DESCRIPTION": "EKD139",
        "BUILDING_RIGHTS_AGREEMENT": "EKD139",
        "BUILDING_RIGHTS_MISC": "EKD139",
        "BUILDING_RIGHT_INTEREST": "EKD139",
        "BUSINESS_PLAN_COMPANY": "EKD139",
        "CASH_FLOW_COMPANY": "EKD139",
        "CIVIL_STATUS_DOCUMENT": "EKD139",
        "COMPANY_MISC": "EKD139",
        "COMPENSATION_AGREEMENT": "EKD139",
        "CONDOMINIUM_MISC": "EKD139",
        "CONDOMINIUM_MIX": "EKD139",
        "CONFIRMATION_ALIMONY": "EKD139",
        "CONFIRMATION_OF_RESIDENCE": "EKD139",
        "CONSTRUCTION_ACCOUNT": "EKD139",
        "CONSTRUCTION_COMPANY_LIST": "EKD139",
        "CONSTRUCTION_CONTRACT": "EKD139",
        "CONSTRUCTION_COST_ESTIMATE": "EKD139",
        "CONSTRUCTION_COST_SUMMARY": "EKD139",
        "CONSTRUCTION_INSURANCE": "EKD139",
        "CONSTRUCTION_MISC": "EKD139",
        "CONSTRUCTION_PERMIT": "EKD139",
        "CONSTRUCTION_PLAN": "EKD139",
        "CONSTRUCTION_QUOTATION": "EKD139",
        "CONSTRUCTION_REGULATIONS": "EKD139",
        "CONSTRUCTION_REQUEST": "EKD139",
        "CONSUMER_LOAN": "EKD139",
        "CONTRACT_GENERAL_CONTRACTOR": "EKD139",
        "CONTRACT_OF_SALE": "EKD139",
        "CONTRACT_TOTAL_CONTRACTOR": "EKD139",
        "CORPORATE_BYLAWS": "EKD139",
        "CORRESPONDENCE_EMAIL": "EKD120",
        "CORRESPONDENCE_LETTER": "EKD120",
        "CORRESPONDENCE_NOTARY": "EKD139",
        "CREDITOR_CHANGE": "EKD139",
        "CREDITWORTHINESS_MISC": "EKD139",
        "CREDIT_CARD_BILL": "EKD139",
        "CREDIT_MISC": "EKD139",
        "CRIF_DATA_INFO": "EKD139",
        "CRIF_QUICK_CONSUMER_CHECK": "EKD139",
        "CRIF_TELEDATA": "EKD139",
        "CRIMINAL_RECORDS": "EKD139",
        "CV_CLIENT": "EKD139",
        "DAILY_ALLOWANCES": "EKD139",
        "DEATH_CERTIFICATE": "EKD139",
        "DEBT_CERTIFICATE": "EKD139",
        "DEBT_COLLECTION_INFORMATION": "EKD139",
        "DEBT_COLLECTION_INFORMATION_BILL": "EKD139",
        "DEBT_COLLECTION_INFORMATION_ORDER_BETREIBUNGSCHALTER_PLUS": "EKD139",
        "DEBT_COLLECTION_INFORMATION_ORDER_CRESURA": "EKD139",
        "DEBT_COLLECTION_INFORMATION_ORDER_TELEDATA": "EKD139",
        "DEBT_COLLECTION_INFORMATION_RECEIPT": "EKD139",
        "DEED_OF_GIFT": "EKD139",
        "DETERMINATION_OF_BENEFICIARY": "EKD139",
        "DIVIDENDS": "EKD139",
        "DIVORCE_CONVENTION": "EKD139",
        "DIVORCE_DECREE": "EKD139",
        "DIVORCE_DOCUMENT": "EKD139",
        "DIVORCE_MISC": "EKD139",
        "DIVORCE_SEPARATION_AGREEMENT": "EKD139",
        "DRAFT_CONTRACT_OF_SALE": "EKD139",
        "EASEMENT_CONTRACT": "EKD139",
        "EMPLOYMENT_CONFIRMATION": "EKD139",
        "EMPLOYMENT_CONTRACT": "EKD139",
        "ENERGY_CERTFICATE": "EKD139",
        "EXPENSE_REGULATIONS": "EKD139",
        "EXTRACT_AHV_ACCOUNT": "EKD121",
        "EXTRACT_FROM_LAND_REGISTER": "EKD139",
        "FILE_NOTE_FINANCING": "EKD139",
        "FINANCE_MISC": "EKD139",
        "FINANCIAL_STATEMENT_COMPANY": "EKD124",
        "FINANCING_CHECKLIST_DOCUMENTS": "EKD139",
        "FINANCING_CONFIRMATION": "EKD139",
        "FINANCING_FEES_LIST": "EKD139",
        "FINANCING_MISC": "EKD139",
        "FINANCING_OFFER": "EKD139",
        "FIN_REPORTING_COMPANY": "EKD139",
        "FOREIGN_NATIONAL_ID": "EKD139",
        "FOUNDATION_CERTIFICATE_CONDOMINIUM": "EKD139",
        "GEAK_CERTIFICATE": "EKD139",
        "GENERAL_INFO": "EKD139",
        "GIS_INFO": "EKD139",
        "HRA": "EKD139",
        "ID": "EKD139",
        "IDENTITY_MISC": "EKD139",
        "ID_OTHER": "EKD139",
        "IKO_CHECK": "EKD139",
        "INCOME_MISC": "EKD139",
        "INCOME_STATEMENT_COMPANY": "EKD139",
        "INCORPORATION_COMPANY": "EKD139",
        "INHERITANCE_ADVANCE": "EKD139",
        "INHERITANCE_CERTIFICATE": "EKD139",
        "INHERITANCE_MISC": "EKD139",
        "INHERITANCE_TESTAMENT": "EKD139",
        "INVESTMENT_PLAN_COMPANY": "EKD139",
        "IRREVOCABLE_PROMISES_TO_PAY": "EKD139",
        "LAND_REGISTER_BILL": "EKD139",
        "LAND_REGISTER_MISC": "EKD139",
        "LEASING_AGREEMENT": "EKD139",
        "LEGITIMATION_FDFA": "EKD139",
        "LETTER_COMMITMENT_NOTARY": "EKD139",
        "LIQUIDITY_PLAN_COMPANY": "EKD139",
        "LIST_OF_RENOVATIONS": "EKD139",
        "LOAN_AGREEMENT": "EKD124",
        "LOAN_AGREEMENT_SHAREHOLDER_COMPANY": "EKD139",
        "MARRIAGE_CONTRACT": "EKD139",
        "MEETING_MINUTES_CONDOMINIUM": "EKD139",
        "MINERGIE_CERTIFICATE": "EKD139",
        "MISC_CAT": "EKD139",
        "MORTGAGE_CONTRACT": "EKD139",
        "MORTGAGE_CONTRACT_CONFIRMATION": "EKD139",
        "MORTGAGE_DUE_NOTICE": "EKD139",
        "MORTGAGE_FRAMEWORK_CONTRACT": "EKD139",
        "MORTGAGE_MISC": "EKD139",
        "MORTGAGE_PRODUCT_CONFIRMATION": "EKD139",
        "MORTGAGE_REQUEST_FORM": "EKD139",
        "MORTGAGE_SUBORDINATION_AGREEMENT": "EKD139",
        "MORTGAGE_TERMINATION": "EKD139",
        "NOTARY_MISC": "EKD139",
        "PASSPORT_CH": "EKD139",
        "PASSPORT_DE": "EKD139",
        "PASSPORT_FR": "EKD139",
        "PASSPORT_IT": "EKD139",
        "PASSPORT_OTHER": "EKD139",
        "PAYSLIP": "EKD139",
        "PENSION3A_ACCOUNT": "EKD123",
        "PENSION3A_CREDIT_NOTE": "EKD123",
        "PENSION3A_INSURANCE_CONTRACT": "EKD123",
        "PENSION3A_INSURANCE_LETTER_REDEMPTION": "EKD123",
        "PENSION3A_INSURANCE_STATEMENT": "EKD123",
        "PENSION3_INSURANCE_APPLICATION": "EKD123",
        "PENSION3_REGULATIONS": "EKD123",
        "PENSION_CERTIFICATE": "EKD122",
        "PENSION_CERTIFICATE_AHV": "EKD121",
        "PENSION_CERTIFICATE_CLOSING_STATEMENT": "EKD122",
        "PENSION_CERTIFICATE_CREDIT_NOTE": "EKD122",
        "PENSION_CERTIFICATE_INFO": "EKD122",
        "PENSION_CERTIFICATE_LETTER": "EKD122",
        "PENSION_CERTIFICATE_SIM_ALL": "EKD122",
        "PENSION_CONTRIBUTION_CONFIRMATION": "EKD139",
        "PENSION_MISC": "EKD139",
        "PENSION_PAYMENT_AHV": "EKD121",
        "PENSION_PAYMENT_BVG": "EKD122",
        "PENSION_PLEDGE": "EKD139",
        "PENSION_REGULATIONS": "EKD122",
        "PENSION_SIMULATION1": "EKD121",
        "PENSION_WITHDRAWL": "EKD122",
        "PENSION_WITHDRAWL_PURPOSE_CONFIRMATION": "EKD139",
        "PERSON_MISC": "EKD139",
        "PILLAR_ONE_CALCULATION": "EKD121",
        "PILLAR_ONE_MISC": "EKD121",
        "PILLAR_THREE_MISC": "EKD123",
        "PILLAR_TWO_MISC": "EKD122",
        "PLAN_ANY": "EKD139",
        "PLAN_CADASTER": "EKD139",
        "PLAN_FLOOR": "EKD139",
        "PLAN_SITUATION": "EKD139",
        "PLATFORM_AGREEMENT": "EKD139",
        "PLEDGE_NOTICE": "EKD139",
        "PLR_CADASTRE": "EKD139",
        "POWER_OF_ATTORNEY": "EKD139",
        "PREPAYMENT_PENALTY": "EKD139",
        "PROJECT_BUDGET": "EKD139",
        "PROOF_OF_FUNDS": "EKD139",
        "PROOF_OF_INCOME": "EKD139",
        "PROPERTY_ACCOUNTS": "EKD139",
        "PROPERTY_BILL": "EKD139",
        "PROPERTY_DOCUMENTATION": "EKD139",
        "PROPERTY_INFO": "EKD139",
        "PROPERTY_INSURANCE": "EKD139",
        "PROPERTY_MISC": "EKD124",
        "PROPERTY_PHOTOS": "EKD139",
        "PROPERTY_TRUSTEE_CONTRACT": "EKD139",
        "PROPERTY_VALUATION": "EKD139",
        "PROPERTY_VALUATION_GOV": "EKD139",
        "PURCHASE_CONTRACT_REGISTRATION": "EKD139",
        "PURCHASE_MISC": "EKD139",
        "PURCHASE_PRICE_LIST_PROPERTY": "EKD139",
        "REGISTRATION_LAND_REGISTER": "EKD139",
        "RENOVATIONS": "EKD139",
        "RENOVATION_FUND": "EKD139",
        "RENTAL_MISC": "EKD124",
        "RESERVATION_CONTRACT": "EKD139",
        "RESERVATION_PAYMENT": "EKD139",
        "RESIDENCE_PERMIT": "EKD139",
        "RETIREMENT_ANALYSIS": "EKD139",
        "RISK_LIFE_INSURANCE": "EKD139",
        "SAFETY_CERTIFICATE_ELECTRICAL": "EKD139",
        "SALARY_ACCOUNT": "EKD139",
        "SALARY_BONUS": "EKD139",
        "SALARY_CERTIFICATE": "EKD124",
        "SALARY_CONFIRMATION": "EKD139",
        "SALARY_CONFIRMATION_13": "EKD139",
        "SALARY_CONFIRMATION_FORM": "EKD139",
        "SALES_DOCUMENTATION": "EKD139",
        "SCHUFA_BONITAETSCHECK": "EKD139",
        "SHARE_REGISTER": "EKD139",
        "SHORT_TIME_WORK": "EKD139",
        "SPECIMEN_SIGNATURE": "EKD139",
        "STATEMENT_OF_ASSETS": "EKD124",
        "STATEMENT_PENSION": "EKD122",
        "STATEMENT_VALUE_RATIO_PROPERTY": "EKD139",
        "TAX_ASSESSMENT": "EKD124",
        "TAX_ATTACHMENTS": "EKD124",
        "TAX_AT_SOURCE_CONFIRMATION": "EKD124",
        "TAX_BILL": "EKD124",
        "TAX_BUDGET": "EKD124",
        "TAX_CALCULATION": "EKD124",
        "TAX_DEBT_INVENTORY": "EKD124",
        "TAX_DECLARATION": "EKD124",
        "TAX_LIST_FINANCIAL_ASSETS": "EKD124",
        "TAX_MISC": "EKD124",
        "TENANCY_AGREEMENT": "EKD139",
        "TENANT_DIRECTORY": "EKD139",
        "TERMS_AND_CONDITIONS": "EKD139",
        "TRANSFER_AGREEMENT": "EKD139",
        "TRANSFER_OF_SECURITY": "EKD139",
        "TURNOVER_COMPANY": "EKD139",
        "UNEMPLOYMENT_SALARY_CERTIFICATE": "EKD139",
        "USER_REGULATIONS_CONDOMINIUM": "EKD139",
        "US_PERSON_FORM": "EKD139",
        "VESTED_BENEFITS_ACCOUNT": "EKD122",
        "VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT": "EKD122",
        "VESTED_BENEFITS_STATEMENT": "EKD122",
        "VOLUME_CALCULATION_SIA": "EKD139",
        "WORLD_CHECK": "EKD139",
        "ZEK_CHECK": "EKD139",
        "ZEK_INFO": "EKD139",
    }


def test_load_document_category2ekd_mappings_invalid_instance_type():
    # Test invalid instance type
    with pytest.raises(Exception, match="Invalid instance type"):
        load_document_category2ekd_mappings(None)

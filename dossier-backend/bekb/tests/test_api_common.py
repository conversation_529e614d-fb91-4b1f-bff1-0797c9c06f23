import json
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path
from tempfile import SpooledTemporaryFile
from typing import List
from urllib.parse import urlparse
from uuid import UUID
from zipfile import ZipFile

import django
import pytest
import structlog
from django.conf import settings
from django.urls import reverse
from django.utils import timezone
from ninja.errors import ValidationError
from pydantic import TypeAdapter
from pytest_django.asserts import assertContains

from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.conftest import check_user
from bekb.fakes import (
    BekbAccountFactoryFaker,
    create_dossier_create_jwt,
    create_dossier_show_jwt,
    create_some_users,
    create_some_bekb_dossiers,
)
from bekb.models import Partner, Attribute, BEKBDossierProperties
from bekb.schemas import schemas
from bekb.schemas.schemas import Entity, JWTAuthSchemaMortgage
from bekb.schemas.schemas_fipla import DossierCreateJWTFipla, JWTAuthSchemaFipla
from bekb.services import (
    create_encoded_jwt,
    update_or_create_user,
    map_to_business_partner,
    list_actively_used_business_parkeys,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
)
from bekb.services_api import create_dossier_from_jwt
from core.authentication import create_token_from_payload, AuthenticatedClient
from core.schema import Result
from dossier.models import Account, Dossier, UserInvolvement
from statemgmt.models import Status

logger = structlog.get_logger()


pytestmark = pytest.mark.django_db


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_create_dossier_api_with_expired_jwt(
    db,
    bekb_fipla_api_client,
    bekb_api_client,
    account_factory,
    url_namespace,
    is_fipla,
):

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    create_data = create_dossier_create_jwt(
        account_factory(update_statemgmt=True), is_fipla=is_fipla
    )
    create_data = create_data.model_copy(update=dict(exp=0))

    encoded_jwt = create_encoded_jwt(create_data.model_dump())

    url = reverse(f"{url_namespace}:create-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = api_client.get(url)

    assert res.status_code == 200

    assertContains(res, "Link not valid anymore")
    assertContains(res, "Link nicht mehr gültig")


@pytest.mark.parametrize(
    "account_factory,schema,is_fipla,account_type,assert_raises",
    [
        (
            BekbAccountFactoryFaker,
            schemas.DossierCreateJWTMortgage,
            False,
            "bekb",
            False,
        ),
        (
            BekbAccountFactoryFaker,
            schemas.DossierCreateJWTMortgage,
            False,
            "bekbfipla",
            True,
        ),
        (BekbFiplaAccountFactory, DossierCreateJWTFipla, True, "bekbfipla", False),
        (BekbFiplaAccountFactory, DossierCreateJWTFipla, True, "bekb", True),
    ],
)
def test_create_dossier_api_incorrect_account_type(
    db,
    client,
    account_factory,
    schema,
    is_fipla,
    account_type,
    assert_raises,
):
    # Test to make sure that the account type is correct
    account_fact = account_factory(update_statemgmt=True)

    create_data = create_dossier_create_jwt(account_fact, is_fipla=is_fipla)

    encoded_jwt = create_encoded_jwt(create_data.model_dump())

    if assert_raises:
        with pytest.raises(ValidationError):
            create_dossier_from_jwt(
                encoded_jwt,
                schema,
                use_fico=not is_fipla,
                account_type=account_type,
            )
    else:
        create_dossier_from_jwt(
            encoded_jwt,
            schema,
            use_fico=not is_fipla,
            account_type=account_type,
        )


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_show_dossier_api_with_expired_jwt(
    db, client, account_factory, url_namespace, is_fipla
):
    show_data = create_dossier_show_jwt(
        account_factory(update_statemgmt=True),
        "some parkey",
        is_fipla=is_fipla,
    )
    show_data = show_data.model_copy(update=dict(exp=0))

    # call the api
    encoded_jwt = create_encoded_jwt(show_data.model_dump())
    url = reverse(f"{url_namespace}:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 200

    assertContains(res, "Link not valid anymore")
    assertContains(res, "Link nicht mehr gültig")


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_show_dossier_api_with_invalid_signature(
    db, client, account_factory, url_namespace, is_fipla
):
    account = account_factory(update_statemgmt=True)
    show_data = create_dossier_show_jwt(account, "some parkey", is_fipla=is_fipla)

    # call the api
    encoded_jwt = create_encoded_jwt(show_data.model_dump(), "manipulated secreted")
    url = reverse(f"{url_namespace}:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 200

    assertContains(res, "Link not valid anymore")
    assertContains(res, "Link nicht mehr gültig")


@pytest.mark.parametrize(
    "url_namespace,account_key",
    [
        ("fipla-api", schemas.AccountNameFipla.bekbfiplae.value),
        ("bekb-api", schemas.AccountNameMortgage.bekbe.value),
    ],
)
def test_list_hypodossier_users(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    url_namespace,
    account_key,
):

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account, _ = Account.objects.get_or_create(
        defaults={"name": account_key},
        key=account_key,
        default_bucket_name="dms-default-bucket",
    )

    external_users = create_some_users(account)
    assert len(external_users) > 0

    # user in another account should not show up, so lets add the user in another account as well
    other_account, _ = Account.objects.get_or_create(
        key=account_key, defaults={"name": "other account"}
    )
    for user in external_users:
        update_or_create_user(user, other_account)

    # no user with email address ending on @hypodossier.ch should show up
    update_or_create_user(
        schemas.User(username="<EMAIL>", name="Example"), account
    )

    url = (
        reverse(f"{url_namespace}:list-hypodossier-users")
        + f"?account_name={account.key}"
    )

    assert testuser1_client.get(url).status_code == 400

    res = api_client.get(url)
    users = TypeAdapter(List[schemas.User]).validate_json(res.content)
    assert res.status_code == 200

    def sort_username(u):
        return u.username

    assert users.sort(key=sort_username) == external_users.sort(key=sort_username)

    assert len(users) == len(external_users)


@pytest.mark.parametrize(
    "url_namespace,account_key",
    [
        ("fipla-api", schemas.AccountNameFipla.bekbfiplae),
        ("bekb-api", schemas.AccountNameMortgage.bekbe),
    ],
)
def test_list_put_users(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    url_namespace,
    account_key,
):

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account = Account.objects.create(
        name=account_key, key=account_key, default_bucket_name="dms-default-bucket"
    )

    url = (
        reverse(f"{url_namespace}:list-hypodossier-users")
        + f"?account_name={account.key}"
    )

    # before we can change something, we have to create something
    some_users = create_some_users(account)

    # let's change all fields
    changed_users = [
        user.model_copy(
            update=dict(
                firstname="changed",
                name="changed",
                pers=not user.pers,
                active=not user.active,
            )
        )
        for user in some_users
    ]

    user_updates = schemas.UserUpdates(
        root=[schemas.UserAttributes(**user.model_dump()) for user in changed_users]
    )

    assert testuser1_client.put(url).status_code == 400
    res = api_client.put(
        url, data=user_updates.model_dump_json(), content_type="application/json"
    )

    assert res.status_code == 204
    assert res.content == b""

    res = api_client.get(url)
    users = TypeAdapter(List[schemas.User]).validate_json(res.content)

    assert users != some_users
    assert sorted(users, key=lambda x: x.username) == sorted(
        changed_users, key=lambda x: x.username
    )
    print(changed_users)

    # all attributes are optional, lets see if that is true
    empty_user_updates = schemas.UserUpdates(
        root=[schemas.UserAttributes(username=user.username) for user in changed_users]
    )
    res = api_client.put(
        url, data=empty_user_updates.model_dump_json(), content_type="application/json"
    )

    assert res.status_code == 204
    assert res.content == b""

    res = api_client.get(url)
    users = TypeAdapter(List[schemas.User]).validate_json(res.content)

    assert users != some_users
    assert sorted(users, key=lambda x: x.username) == sorted(
        changed_users, key=lambda x: x.username
    )


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_create_dossier_api_with_invalid_signature(
    db, client, account_factory, url_namespace, is_fipla
):

    account_factory = account_factory(update_statemgmt=True)

    create_data = create_dossier_create_jwt(account_factory, is_fipla=is_fipla)
    create_data = create_data.model_copy(update=dict(exp=0))

    encoded_jwt = create_encoded_jwt(create_data.model_dump(), "invalid secret")
    url = reverse(f"{url_namespace}:create-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)

    assert res.status_code == 200

    assertContains(res, "Link not valid anymore")
    assertContains(res, "Link nicht mehr gültig")


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_list_business_parkey(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    is_fipla,
):
    use_fico = not is_fipla

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account_factory = account_factory(update_statemgmt=True)
    created_dossiers, _ = create_some_bekb_dossiers(
        account_factory.account, number_of_dossier=20, use_fico=use_fico
    )

    unused_parter = Partner.objects.create(
        parkey="unused",
        account=account_factory.account,
        name="unused",
        firstname="unused",
    )

    url = (
        reverse(f"{url_namespace}:list-business-parkeys")
        + f"?account_name={account_factory.account.key}"
    )
    assert testuser1_client.get(url).status_code == 400
    start = timezone.now()
    res = api_client.get(url)
    assert res.status_code == 200
    end = timezone.now()

    logger.info("test_list_business_parkey", duration=end - start)

    actively_used_partners = TypeAdapter(List[schemas.Parkey]).validate_json(
        res.content
    )

    assert (
        actively_used_partners.sort()
        == [dossier.business_partner.parkey for dossier in created_dossiers].sort()
    )
    assert unused_parter not in actively_used_partners


@pytest.mark.parametrize(
    "account_factory,url_namespace,is_fipla",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_update_some_business_parkey(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    is_fipla,
):
    use_fico = not is_fipla

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account_factory = account_factory(update_statemgmt=True)
    account = account_factory.account
    dossier_count = 20
    created_dossiers, bekb_dossiers = create_some_bekb_dossiers(
        account, dossier_count, use_fico
    )

    updates = []
    for idx, dossier in enumerate(created_dossiers):
        updates.append(
            schemas.BusinessPartnerUpdate(
                business_parkey=dossier.business_partner.parkey,
                business_partner=schemas.BusinessPartner(
                    parkey=dossier.business_partner.parkey,
                    name=f"bp other name {idx}",
                    firstname=f"bp other firstname {idx}",
                    pers=not dossier.business_partner.pers,
                ),
                partner_partner=schemas.Partner(
                    parkey=f"another key {idx}",
                    name=f"pp other name {idx}",
                    firstname=f"pp other firstname {idx}",
                ),
            )
        )

    assert len(updates) == dossier_count

    # TODO: we should test that we do not touch another account
    url = (
        reverse(f"{url_namespace}:update-business-partners")
        + f"?account_name={account.key}"
    )
    assert testuser1_client.put(url).status_code == 400

    res = api_client.put(
        url,
        data=schemas.BusinessPartnerUpdates(root=updates).model_dump_json(),
        content_type="application/json",
    )
    assert res.status_code == 204

    # check after update, that the pers flag is updated
    for dossier in bekb_dossiers:
        pers_before = dossier.pers
        dossier.refresh_from_db()
        pers_after = dossier.pers
        assert pers_before != pers_after

    for update in updates:
        partner = Partner.objects.get(parkey=update.business_parkey, account=account)
        assert schemas.BusinessPartner(**partner.__dict__) == update.business_partner


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt,use_fico",
    [
        (BekbAccountFactoryFaker, "bekb-api", False, True),
        # (BekbFiplaAccountFactory, "fipla-api", True, False),
    ],
)
def test_create_or_update_attribute(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    update_statemgmt,
    use_fico,
):
    """
    This is not in use for fipla, only mortgage
    @param db:
    @param bekb_api_client:
    @param bekb_fipla_api_client:
    @param testuser1_client:
    @param account_factory:
    @param url_namespace:
    @param update_statemgmt:
    @param use_fico:
    @return:
    """

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account_factory = account_factory(update_statemgmt=update_statemgmt)
    account = account_factory.account

    # python manage.py dumpdata bekb.Attribute --format json
    bekb_attribute_json_export = """[{"model": "bekb.attribute", "pk": "00230fcd-a77f-44d0-968f-0c4030385c0d", "fields": {"created_at": "2022-11-29T08:43:16.143Z", "updated_at": "2022-11-29T08:43:16.143Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "B", "name_de": "Eigentumswohnung", "name_fr": "Eigentumswohnung fr"}}, {"model": "bekb.attribute", "pk": "0e0fdff8-2abc-41cf-bba2-927e19f58f82", "fields": {"created_at": "2022-11-29T08:37:47.773Z", "updated_at": "2022-11-29T08:37:47.773Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessType", "key": "NEW", "name_de": "Neugeschäft", "name_fr": "Neugeschäft FR"}}, {"model": "bekb.attribute", "pk": "2a6158fb-e3bf-4abc-8e5d-6d0d296c8018", "fields": {"created_at": "2022-11-29T08:40:34.415Z", "updated_at": "2022-11-29T08:45:05.184Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "CollateralType", "key": "1", "name_de": "Grundpfand", "name_fr": "Grundpfand fr"}}, {"model": "bekb.attribute", "pk": "56d14d5c-f52e-44ea-aed3-d1d7f586a5b1", "fields": {"created_at": "2022-11-29T08:37:00.473Z", "updated_at": "2022-11-29T08:37:00.473Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessStatus", "key": "ACTIVE", "name_de": "aktiv", "name_fr": "aktiv fr"}}, {"model": "bekb.attribute", "pk": "60491ccc-b149-4a8b-b7f1-9e1406006d5d", "fields": {"created_at": "2022-11-29T08:47:31.349Z", "updated_at": "2022-11-29T08:47:31.349Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyCollateralType", "key": "SECONDARY", "name_de": "Nebendeckung", "name_fr": "Nebendeckung fr"}}, {"model": "bekb.attribute", "pk": "66c9977f-7bb6-4851-9f7c-fb846d010b26", "fields": {"created_at": "2022-11-29T08:47:10.177Z", "updated_at": "2022-11-29T08:47:10.177Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyCollateralType", "key": "MAIN", "name_de": "Hauptdeckung", "name_fr": "Hauptdeckung fr"}}, {"model": "bekb.attribute", "pk": "796ba3e4-3d18-4141-9f6c-eec0462d9193", "fields": {"created_at": "2022-11-29T08:42:18.554Z", "updated_at": "2022-11-29T08:43:00.435Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "A", "name_de": "Einfamilienhaus", "name_fr": "Einfamilienhaus fr"}}, {"model": "bekb.attribute", "pk": "c2fab320-3641-42de-b0fb-827543dcd426", "fields": {"created_at": "2022-11-29T08:43:34.717Z", "updated_at": "2022-11-29T08:43:34.717Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "C", "name_de": "Garage", "name_fr": "Garage fr"}}, {"model": "bekb.attribute", "pk": "cf5bbd96-31e7-4827-a638-9d21764c9687", "fields": {"created_at": "2022-11-29T08:38:09.665Z", "updated_at": "2022-11-29T08:38:09.665Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessType", "key": "RESCHEDULE", "name_de": "Ablöser", "name_fr": "Ablöser fr"}}, {"model": "bekb.attribute", "pk": "dda52a91-12ae-4cc1-adec-773fac5bd348", "fields": {"created_at": "2022-11-29T08:37:26.246Z", "updated_at": "2022-11-29T08:37:26.246Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "BusinessStatus", "key": "DELETED", "name_de": "gelöscht", "name_fr": "geschlöscht fr"}}, {"model": "bekb.attribute", "pk": "e0a32d6a-1945-424a-8d79-56e760033034", "fields": {"created_at": "2022-11-29T08:44:10.297Z", "updated_at": "2022-11-29T08:44:10.297Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "PropertyType", "key": "F", "name_de": "Mehrfamilienhaus", "name_fr": "Mehrfamilienhaus fr"}}, {"model": "bekb.attribute", "pk": "e1441d3c-8a6a-4215-a61e-649c97a12701", "fields": {"created_at": "2022-11-29T08:41:14.082Z", "updated_at": "2022-11-29T08:44:56.733Z", "account": "c839512a-c71c-4f5c-9c74-7810a7ff2bd4", "entity": "CollateralType", "key": "2", "name_de": "Personenversicherung", "name_fr": "Personenversicherung fr"}}]"""

    # create an attribute which should be update
    Attribute.objects.update_or_create(
        account=account,
        entity=Entity.PropertyType,
        key="B",
        defaults={
            "name_de": "something else",
            "name_fr": "something else",
        },
    )

    attributes = json.loads(bekb_attribute_json_export)
    attributes = [
        schemas.Attribute(
            entity=schemas.Entity[attribute.get("fields")["entity"]],
            key=attribute.get("fields").get("key"),
            name_de=attribute.get("fields").get("name_de"),
            name_fr=attribute.get("fields").get("name_fr"),
        )
        for attribute in attributes
    ]

    updates = schemas.AttributeUpdates(root=attributes)

    url = reverse("bekb-api:update-attributes") + f"?account_name={account.key}"
    assert testuser1_client.put(url).status_code == 400
    res = api_client.put(
        url, data=updates.model_dump_json(), content_type="application/json"
    )
    assert res.status_code == 204

    stored_attributes = [
        schemas.Attribute(
            entity=schemas.Entity[attribute.entity],
            key=attribute.key,
            name_de=attribute.name_de,
            name_fr=attribute.name_fr,
        )
        for attribute in Attribute.objects.filter(account=account).all()
    ]

    def sort_attribute():
        return lambda x: f"{x.entity}{x.key}"

    assert attributes.sort(key=sort_attribute()) == stored_attributes.sort(
        key=sort_attribute()
    )


def test_create_dossier_api_fipla(db, client: django.test.client.Client):
    account_factory = BekbFiplaAccountFactory(update_statemgmt=True)
    account = account_factory.account

    default_create = create_dossier_create_jwt(account_factory, is_fipla=True)

    encoded_jwt = create_encoded_jwt(default_create.model_dump())
    url = reverse("fipla-api:create-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)

    assert res.status_code == 302

    parsed = urlparse(res.headers["Location"])
    dossier_uuid = Path(parsed.path).parent.parent.name

    assert (
        res.headers["Location"]
        == f"{account.dmf_endpoint}/dossier/{dossier_uuid}/view/page?lang={default_create.language.value}"
    )

    check_user(default_create.current_user, account)

    dossier = Dossier.objects.get(uuid=dossier_uuid, account=account)

    dossier_properties = BEKBDossierProperties.objects.get(dossier=dossier)

    assert (
        map_to_business_partner(dossier_properties.business_partner)
        == default_create.business_partner
    )

    involvement: UserInvolvement
    for involvement in dossier.userinvolvement_set.all():
        print(
            dossier.uuid,
            involvement.user.user.username,
            involvement.dossier,
            involvement.role.key,
        )
    assert dossier.userinvolvement_set.count() == 1


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt,use_fico",
    [
        (BekbAccountFactoryFaker, "bekb-api", False, True),
        (BekbFiplaAccountFactory, "fipla-api", True, False),
    ],
)
@pytest.mark.django_db(transaction=True)  # Avoid deadlock in parallel execution
def test_list_of_businesscase(
    db,
    bekb_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    update_statemgmt,
    use_fico,
):
    account_factory = account_factory(update_statemgmt=update_statemgmt)
    account = account_factory.account

    num_dossier = 3
    create_some_bekb_dossiers(account, num_dossier)

    bekb_dossiers = BEKBDossierProperties.objects.filter(account=account).all()

    assert BEKBDossierProperties.objects.filter(account=account).count() == num_dossier

    # Test: assign 2nd dossier to same business_partner as first -> number of business_partners is reduced by 1
    first_dossier = bekb_dossiers[0]
    snd_dossier = bekb_dossiers[1]

    first_dossier.business_partner = snd_dossier.business_partner
    assert first_dossier.business_partner == snd_dossier.business_partner
    assert first_dossier.business_partner.parkey == "gp 1"
    first_dossier.save()

    business_parkeys = list_actively_used_business_parkeys(account.key)
    assert len(business_parkeys) == num_dossier - 1


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt,use_fico",
    [
        (BekbAccountFactoryFaker, "bekb-api", False, True),
        (BekbFiplaAccountFactory, "fipla-api", True, False),
    ],
)
def test_list_of_businesscase_with_expired(
    db, account_factory, url_namespace, update_statemgmt, use_fico
):
    account_factory = account_factory(update_statemgmt=update_statemgmt)
    account = account_factory.account

    num_dossier = 3
    created_dossiers, created_bekb_dossiers = create_some_bekb_dossiers(
        account, num_dossier, use_fico=use_fico
    )

    business_parkeys_1 = list_actively_used_business_parkeys(account.key)
    assert len(business_parkeys_1) == num_dossier

    # Change one of the dossiers to be expired

    first_dossier = created_bekb_dossiers[0]
    assert first_dossier
    first_dossier.dossier.expiry_date = timezone.now() + timedelta(days=-1)
    first_dossier.dossier.name = "changed dossier"
    first_dossier.dossier.save()

    # Test that changed dossier is not returned as "active" anymore
    business_parkeys_2 = list_actively_used_business_parkeys(account.key)
    assert len(business_parkeys_2) == num_dossier - 1


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt",
    [
        (BekbAccountFactoryFaker, "bekb-api", False),
        (BekbFiplaAccountFactory, "fipla-api", True),
    ],
)
def test_show_dossier_ready_for_export(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    update_statemgmt,
):
    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    account_factory = account_factory(
        update_statemgmt=update_statemgmt, default_bucket_name="dms-default-bucket"
    )
    account = account_factory.account
    bekb_dossiers = []
    num_of_exports = 2
    for i in range(num_of_exports):
        bekb_dossier = account_factory.create_export_archive_available_dossier()
        bekb_dossiers.append(bekb_dossier)

    url = (
        reverse(f"{url_namespace}:show-dossier-ready-for-export")
        + f"?account_name={account.key}"
    )

    assert testuser1_client.get(url).status_code == 400
    res = api_client.get(url)
    assert res.status_code == 200
    dossier_exports = TypeAdapter(List[schemas.DossierExport]).validate_json(
        res.content
    )
    assert len(dossier_exports) == num_of_exports


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt,EXPORT_STATUS_KEYS",
    [
        (
            BekbAccountFactoryFaker,
            "bekb-api",
            True,
            VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
        ),
        (
            BekbFiplaAccountFactory,
            "fipla-api",
            True,
            VALID_READY_FOR_EXPORT_STATUS_KEYS_FIPLA,
        ),
    ],
)
def test_process_ready_for_export_api(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    account_factory,
    url_namespace,
    update_statemgmt,
    EXPORT_STATUS_KEYS,
):

    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client

    # Since 240408 this runs with the new business case types and supports READY_FOR_EXPORT_FICO
    account_factory = account_factory(
        update_statemgmt=update_statemgmt, default_bucket_name="dms-default-bucket"
    )
    account = account_factory.account

    ready_for_export_states = Status.objects.filter(
        state_machine=account.active_work_status_state_machine,
        key__in=EXPORT_STATUS_KEYS,
    ).all()

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == 0
    )

    for wsk in EXPORT_STATUS_KEYS:
        # We accept that this could create a random businesscase type that is not
        # valid with the chosen work_status. But we ignore it as it is not relevant
        # for this test
        account_factory.create_ready_for_export_dossier(possible_work_status_keys=[wsk])

    num_of_exports = len(EXPORT_STATUS_KEYS)

    export_archive_available_state = Status.objects.get(
        state_machine=account.active_work_status_state_machine,
        key="EXPORT_ARCHIVE_AVAILABLE",
    )

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == num_of_exports
    )

    res = api_client.post(
        reverse(f"{url_namespace}:process-ready-for-export")
        + f"?account_name={account.key}",
        data="{}",
        content_type="application/json",
    )
    result = TypeAdapter(List[Result[UUID]]).validate_json(res.content)
    assert len(result) == num_of_exports
    assert res.status_code == 201

    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status__in=ready_for_export_states
        ).count()
        == 0
    )
    assert (
        BEKBDossierProperties.objects.filter(
            account=account, dossier__work_status=export_archive_available_state
        ).count()
        == num_of_exports
    )


@pytest.mark.parametrize(
    "account_factory,url_namespace,update_statemgmt",
    [
        (
            BekbAccountFactoryFaker,
            "bekb-api",
            True,
        ),
        (
            BekbFiplaAccountFactory,
            "fipla-api",
            True,
        ),
    ],
)
def test_download_file(
    db,
    bekb_api_client,
    bekb_fipla_api_client,
    testuser1_client,
    account_factory,
    url_namespace,
    update_statemgmt,
):
    if url_namespace == "fipla-api":
        api_client = bekb_fipla_api_client
    else:
        api_client = bekb_api_client
    account_factory = account_factory(
        update_statemgmt=update_statemgmt, default_bucket_name="dms-default-bucket"
    )
    account = account_factory.account

    bekb_dossier = account_factory.create_export_archive_available_dossier()

    export_uuid = bekb_dossier.dossier.bekbexport.uuid

    url = (
        reverse(f"{url_namespace}:download-file")
        + f"?account_name={account.key}&export_uuid={export_uuid}"
    )

    assert testuser1_client.get(url).status_code == 400
    res = api_client.get(url)
    assert res.status_code == 200

    content_disposition = res.headers["content-disposition"]

    if url_namespace == "bekb-api":
        assert (
            content_disposition
            == f'attachment; filename="05_hypodossier_bekbe_{bekb_dossier.dossier.bekbexport.file.created_at.strftime("%Y%m%d")}_{export_uuid}.zip"'
        )
    else:
        assert (
            content_disposition
            == f'attachment; filename="05_hypodossier_bekbfiplae_{bekb_dossier.dossier.bekbexport.file.created_at.strftime("%Y%m%d")}_{export_uuid}.zip"'
        )

    with SpooledTemporaryFile() as temp_file:
        for block in iter(res.streaming_content):
            temp_file.write(block)

        with ZipFile(temp_file, "r") as zip:
            filenames = [file.filename for file in zip.filelist]

        assert len(filenames) == bekb_dossier.dossier.semantic_documents.count() + 1


@pytest.mark.parametrize(
    "account_factory,schema,url_namespace,status_code",
    [
        (
            BekbAccountFactoryFaker,
            JWTAuthSchemaMortgage,
            "bekb-api",
            200,
        ),
        (
            BekbAccountFactoryFaker,
            JWTAuthSchemaMortgage,
            "fipla-api",
            400,
        ),
        (
            BekbFiplaAccountFactory,
            JWTAuthSchemaFipla,
            "fipla-api",
            200,
        ),
        (
            BekbFiplaAccountFactory,
            JWTAuthSchemaFipla,
            "bekb-api",
            400,
        ),
    ],
)
def test_api_auth(db, account_factory, schema, url_namespace, status_code):
    # Test that the api is protected by the correct account name
    bekb_account_factory = account_factory()
    account = bekb_account_factory.account

    jwt = schema(
        aud="account",
        email_verified=True,
        preferred_username="<EMAIL>",
        user_roles=[settings.BEKB_API_ROLE],
        account_key=account.key,
    )

    data = jwt.model_dump()

    token = create_token_from_payload(data)
    bekbe_api_client = AuthenticatedClient(token)

    response = bekbe_api_client.get(
        reverse(
            f"{url_namespace}:list-dossiers",
            kwargs={
                "account_name": account.key,
                "business_parkey": "my test parkey",
            },
        )
    )

    assert response.status_code == status_code

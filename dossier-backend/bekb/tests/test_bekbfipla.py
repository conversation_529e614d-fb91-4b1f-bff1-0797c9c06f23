from pathlib import Path
from typing import List
from urllib.parse import urlparse

import pytest
import django
from django.urls import reverse
from faker import Faker
from pydantic import TypeAdapter

from bekb.bekb_instance_type import BekbInstanceType
from bekb.bekbfipla_factory import BekbFiplaAccountFactory
from bekb.collaterals import get_collateral_requirement_satisfaction_options
from bekb.fakes import (
    handle_check_ekd_ids_and_external_titles,
    create_dossier_show_jwt,
    create_dossier_create_jwt,
    handle_check_bekb_config,
    update_or_create_collateral_types,
)
from bekb.models import BEKBDossierProperties
from bekb.schemas.schemas import (
    DossierShow,
    AccountNameFipla,
)
from bekb.schemas.schemas_fipla import JWTAuthSchemaFipla
from bekb.services import (
    create_encoded_jwt,
    map_to_business_partner,
    map_user_to_schema,
)
from urllib import parse

from bekb.conftest import check_user
from core.authentication import (
    Authenticated<PERSON><PERSON>,
    create_token_from_payload,
)
from dossier.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Dossier, UserInvolvement
from django.conf import settings


def test_load_bekbfiplae_account(db):
    """
    Set up bekbe Fipla with
    - new document categries
    @return:
    """

    Faker.seed(234342)
    bfac = BekbFiplaAccountFactory(update_statemgmt=True)

    account_key = bfac.account.key
    assert account_key == AccountNameFipla.bekbfiplae.value
    handle_check_ekd_ids_and_external_titles(account_key)


def test_list_dossiers(db):
    fipla_account_factory = BekbFiplaAccountFactory()
    account = fipla_account_factory.account

    jwt = JWTAuthSchemaFipla(
        aud="account",
        email_verified=True,
        preferred_username="<EMAIL>",
        # email="<EMAIL>",
        # given_name="BEKB user with API role but no manager role",
        # family_name="BEKB user with API role but no manager role",
        user_roles=[settings.BEKB_API_ROLE],
        account_key=account.key,
    )
    token = create_token_from_payload(jwt.model_dump())
    fipla_api_client = AuthenticatedClient(token)

    test_business_parkey = "my test parkey"

    for dossier in range(3):
        fipla_account_factory.create_dossier(business_parkey=test_business_parkey)

    response = fipla_api_client.get(
        reverse(
            "fipla-api:list-dossiers",
            kwargs={
                "account_name": account.key,
                "business_parkey": test_business_parkey,
            },
        )
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[DossierShow]).validate_json(response.content)

    assert len(parsed) == 3


@pytest.mark.parametrize("valid_jwt", [True, False])
def test_jwt_validation(db, valid_jwt):
    bekb_account_factory = BekbFiplaAccountFactory()
    account = bekb_account_factory.account

    jwt = JWTAuthSchemaFipla(
        aud="account",
        email_verified=True,
        preferred_username="<EMAIL>",
        user_roles=[settings.BEKB_API_ROLE],
        account_key=account.key,
    )

    data = jwt.model_dump()

    # Remove required field
    if not valid_jwt:
        data.pop("preferred_username")

    token = create_token_from_payload(data)
    bekbe_api_client = AuthenticatedClient(token)

    response = bekbe_api_client.get(
        reverse(
            "fipla-api:list-dossiers",
            kwargs={
                "account_name": account.key,
                "business_parkey": "my test parkey",
            },
        )
    )

    if valid_jwt:
        assert response.status_code == 200
    else:
        assert response.status_code == 400
        assert response.json() == {
            "detail": "{\"message\": \"Invalid JWT format\", \"details\": \"[{'type': 'missing', 'loc': ('preferred_username',), 'msg': 'Field required', 'input': {'aud': 'account', 'email_verified': True, 'user_roles': ['bekb_api'], 'email': None, 'given_name': None, 'family_name': None, 'account_key': 'bekbfiplae'}, 'url': 'https://errors.pydantic.dev/2.10/v/missing'}]\"}"
        }


def test_show_dossier_api_basic(db, client: django.test.client.Client):
    account_factory = BekbFiplaAccountFactory(update_statemgmt=True)
    account = account_factory.account

    assert account.dmf_endpoint == "https://fipla.bekbe.test.hypodossier.ch"

    test_business_parkey = "my test parkey"

    # already existing dossier should be updated with the new data, so lets create some dossier
    for dossier in range(3):
        account_factory.create_dossier(business_parkey=test_business_parkey)

    # Make sure all dossiers have a partner_partner is not set
    for bekb_dossier in BEKBDossierProperties.objects.filter(
        business_partner__parkey=test_business_parkey
    ):
        assert bekb_dossier.partner_partner is None

    show_data = create_dossier_show_jwt(
        account_factory, test_business_parkey, is_fipla=True
    )
    encoded_jwt = create_encoded_jwt(show_data.model_dump())

    url = reverse("fipla-api:show-dossier") + f"?encoded_jwt={encoded_jwt}"
    res = client.get(url)
    assert res.status_code == 302

    redirect_location = res.headers["Location"]

    # language is forwarded
    assert (
        redirect_location
        == f"{account.dmf_endpoint}/dashboard?business_parkey={parse.quote(show_data.business_partner.parkey)}&lang={show_data.language.value}"
    )

    # check all dossiers are updated
    for bekb_dossier in BEKBDossierProperties.objects.filter(
        business_partner__parkey=test_business_parkey
    ):
        # Partner_partner is not set
        assert bekb_dossier.partner_partner is None

        # business partner data must be updated
        assert (
            map_to_business_partner(bekb_dossier.business_partner)
            == show_data.business_partner
        )

    # current user is updated
    assert (
        map_user_to_schema(
            DossierUser.objects.get(
                account=account, user__username=show_data.current_user.username
            )
        )
        == show_data.current_user
    )


# Todo: verify with Manuel and then apply DRY principle
def test_create_dossier_api(db, client: django.test.client.Client):
    account_factory = BekbFiplaAccountFactory(update_statemgmt=True)
    account = account_factory.account

    default_create = create_dossier_create_jwt(account_factory, is_fipla=True)

    create_datas = [default_create]

    for create_data in create_datas:
        encoded_jwt = create_encoded_jwt(create_data.model_dump())
        url = reverse("fipla-api:create-dossier") + f"?encoded_jwt={encoded_jwt}"
        res = client.get(url)

        assert res.status_code == 302

        parsed = urlparse(res.headers["Location"])
        dossier_uuid = Path(parsed.path).parent.parent.name

        assert (
            res.headers["Location"]
            == f"{account.dmf_endpoint}/dossier/{dossier_uuid}/view/page?lang={create_data.language.value}"
        )

        check_user(create_data.current_user, account)

        dossier = Dossier.objects.get(uuid=dossier_uuid, account=account)

        dossier_properties = BEKBDossierProperties.objects.get(dossier=dossier)

        assert (
            map_to_business_partner(dossier_properties.business_partner)
            == create_data.business_partner
        )

        involvement: UserInvolvement
        for involvement in dossier.userinvolvement_set.all():
            print(
                dossier.uuid,
                involvement.user.user.username,
                involvement.dossier,
                involvement.role.key,
            )
        # Changed user involvement from 2 -> 1
        assert dossier.userinvolvement_set.count() == 1


def test_fipla_document_category_configuration(db):
    fipla_account_factory = BekbFiplaAccountFactory()
    doc_cat_map, mappings = handle_check_bekb_config(
        fipla_account_factory.account.key, BekbInstanceType.FIPLA
    )

    """
    These numbers have to be adjusted whenever a new document category is added for fipla
    240326 mt: PROOF_OF_INCOME, RENOVATIONS, BEKB_EKD118 have been added
    240507 mt: SHARE_REGISTER, PLATFORM_AGREEMENT added
    240523 mt: PROPERTY_BILL added
    240723 mt: 14 new doc cats added. now 305 instead of 291
    241022 mt: 1 new doc cat added 600-EKD142 myky-Dossier
    241023 mt: 1 new doc cat added 511-EKD130 Gründungsunterlagen
    241230 added these 6:
        273-EKD131,DEATH_CERTIFICATE,Sterbeurkunde,Death certificate,Acte décès,Certificato di morte,,0
        700-EKD138,BEKB_FIPLA_RESULT,FiPla: FiPla Vorsorgeausweis,FiPla: Pension Certificate,PlaFi: Certificat de Prévoyance,FiPla: Certificato di Previdenza,,0
        700-EKD139,BEKB_EKD139,FiPla: Diverses,FiPla: Miscellaneous,FiPla : Divers,FiPla: Varie,,0
        745-EKD133,MORTGAGE_SUBORDINATION_AGREEMENT,Rangrücktrittserklärung,Subordination agreement,Déclaration postposition,Dichiarazione di postergazione,,0
        786-EKD133,LETTER_COMMITMENT_NOTARY,Verpflichtungserklärung Notar,Letter of Commitment Notary,Lettre engagement notaire,Lettera impegno del notaio,,0
        788-EKD133,CORRESPONDENCE_NOTARY,Korrespondenz Notar,Correspondence Notary,Currier - email notaire,Corrispondenza notaio,,0
    250103 added EKD139
    250222 mt: 1 new doc cat added 200-EKD120 Fragebogen Finanzplanung BEKB_FIPLA_FORM
    250314 mt: added 700-EKD55 BEKB_TOTAL_ENGAGEMENT

    """
    assert len(doc_cat_map.items()) == 307 + 9
    assert (
        len(mappings.items()) == 307 + 9
    )  # Difference TAX_ATTACHMENTS has been removed

    doc_cat_map_keys = doc_cat_map.keys()
    mappings_keys = mappings.keys()

    assert doc_cat_map_keys == mappings_keys


def test_load_external_document_categories(db):
    account_factory = BekbFiplaAccountFactory(update_statemgmt=True)
    account = account_factory.account

    collateral_types = {
        type.key: type for type in update_or_create_collateral_types(account)
    }
    requirements_satisfaction_options = get_collateral_requirement_satisfaction_options(
        collateral_types
    )

    # This list only contains all document categories that require a collateral
    # All document categories without collaterals are not included
    # Order of the list is relevant and should be ascending here. Actuals will be sorted before comparison
    correct_result = {
        "ADDITIONAL_COST_ACCOUNT": ["1"],
        "ARCHITECT_CONTRACT": ["1"],
        "ASSUMPTION_DEBT_NOTICE": ["1"],
        "BEKB_EKD08": ["8"],
        "BEKB_EKD09": ["2", "8", "9"],
        "BEKB_EKD10": ["2", "8", "9"],
        "BEKB_EKD102": ["1"],
        "BEKB_EKD103": ["1"],
        "BEKB_EKD11": ["3"],
        "BEKB_EKD17": ["5"],
        "BEKB_EKD18": ["5"],
        "BEKB_EKD20": ["1"],
        "BEKB_EKD26": ["1"],
        "BEKB_EKD27": ["1"],
        "BEKB_EKD28": ["1"],
        "BEKB_EKD29": ["1"],
        "BEKB_EKD31": ["4"],
        "BEKB_EKD32": ["10"],
        "BEKB_EKD91": ["8", "9"],
        "BEKB_EKD92": ["8", "9"],
        "BEKB_EKD93": ["1"],
        "BEKB_EKD94": ["1"],
        "BEKB_EKD98": ["1"],
        "BUILDING_DESCRIPTION": ["1"],
        "BUILDING_RIGHTS_AGREEMENT": ["1"],
        "CONSTRUCTION_ACCOUNT": ["1"],
        "CONSTRUCTION_COMPANY_LIST": ["1"],
        "CONSTRUCTION_CONTRACT": ["1"],
        "CONSTRUCTION_COST_ESTIMATE": ["1"],
        "CONSTRUCTION_COST_SUMMARY": ["1"],
        "CONSTRUCTION_INSURANCE": ["1"],
        "CONSTRUCTION_PERMIT": ["1"],
        "CONSTRUCTION_PLAN": ["1"],
        "CONSTRUCTION_QUOTATION": ["1"],
        "CONTRACT_GENERAL_CONTRACTOR": ["1"],
        "CONTRACT_OF_SALE": ["1"],
        "CONTRACT_TOTAL_CONTRACTOR": ["1"],
        "EASEMENT_CONTRACT": ["1"],
        "EXTRACT_FROM_LAND_REGISTER": ["1"],
        "FOUNDATION_CERTIFICATE_CONDOMINIUM": ["1"],
        "GEAK_CERTIFICATE": ["1"],
        "LIST_OF_RENOVATIONS": ["1"],
        "MEETING_MINUTES_CONDOMINIUM": ["1"],
        "MINERGIE_CERTIFICATE": ["1"],
        "PENSION3A_INSURANCE_LETTER_REDEMPTION": ["2"],
        "PENSION3A_INSURANCE_STATEMENT": ["2"],
        "PLAN_ANY": ["1"],
        "PLAN_CADASTER": ["1"],
        "PLAN_FLOOR": ["1"],
        "PLAN_SITUATION": ["1"],
        "PLR_CADASTRE": ["1"],
        "PROJECT_BUDGET": ["1"],
        "PROPERTY_ACCOUNTS": ["1"],
        "PROPERTY_BILL": ["1"],
        "PROPERTY_INSURANCE": ["1"],
        "PROPERTY_TRUSTEE_CONTRACT": ["1"],
        "PROPERTY_VALUATION": ["1"],
        "PROPERTY_VALUATION_GOV": ["1"],
        "RENOVATIONS": ["1"],
        "RENOVATION_FUND": ["1"],
        "RENTAL_MISC": ["1"],
        "STATEMENT_VALUE_RATIO_PROPERTY": ["1"],
        "TENANCY_AGREEMENT": ["1"],
        "TENANT_DIRECTORY": ["1"],
        "USER_REGULATIONS_CONDOMINIUM": ["1"],
    }
    correct_result = dict(sorted(correct_result.items()))

    actual_options = {
        k: [i.key for i in v]
        for k, v in requirements_satisfaction_options.items()
        if len(v) > 0
    }
    for key, val in actual_options.items():
        actual_options[key] = sorted(val)

    assert actual_options == correct_result

import json

import django

from core.helpers import compare_datastructures
from bekb.schemas.data import DATA_PATH


def test_openapi_schema(client: django.test.client.Client):
    # Test to ensure that the underlying json Api schema has not changed
    # Caution: this test will break if a docstring is changed for one of the api functions
    res = client.get("/partner/bekb/api/0.7/openapi.json")
    assert res.status_code == 200

    with open(DATA_PATH / "bekb_openapi_3_1_0_2025_02_20.json") as file:
        target_data = json.load(file)

    res_data = res.json()

    compare_datastructures(res_data, target_data)

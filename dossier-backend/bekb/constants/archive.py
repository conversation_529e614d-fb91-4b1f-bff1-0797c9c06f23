from typing import List

# This file contains the configuration for the auto-grouping feature which is applied in the
# archiving process for BEKB, triggered by feature flag
# ENABLE_BEKB_ARCHIVE_GROUPING_BY_DOCUMENT_CATEGORY

PLAN_ANY = [
    "PLAN_ANY",
    "PLA<PERSON>_FLOOR",
    "CONSTRUCTION_PLAN",
]

PLAN_SITUATION = [
    "PLAN_SITUATION",
    "PLAN_CADASTER",
]

EXTRACT_FROM_LAND_REGISTER = [
    "EXTRACT_FROM_LAND_REGISTER",
    "PLR_CADASTRE",
]

PILLAR_TWO_MISC = [
    "PENSION_CERTIFICATE",
    "PENSION_REGULATIONS",
    "STATEMENT_PENSION",
    "PENSION_WITHDRAWL",
    "PENSION_CERTIFICATE_SIM_ALL",
    "PENSION_CERTIFICATE_LETTER",
    "PENSION_CERTIFICATE_INFO",
    "PENSION_CERTIFICATE_CREDIT_NOTE",
    "PENSION_CERTIFICATE_CLOSING_STATEMENT",
    "PILLAR_TWO_MISC",
]

PROOF_OF_INCOME = [
    "SALARY_CERTIFICATE",
    "SALARY_CONFIRMATION",
    "EMPLOYMENT_CONFIRMATION",
    "EMPLOYMENT_CONTRACT",
    "COMPENSATION_AGREEMENT",
    "BONUS_REGULATIONS",
    "EXPENSE_REGULATIONS",
    "PAYSLIP",
    "SALARY_ACCOUNT",
    "SALARY_CONFIRMATION_FORM",
    "SALARY_BONUS",
    "SALARY_CONFIRMATION_13",
    "PENSION_PAYMENT_AHV",
    "PENSION_PAYMENT_BVG",
    "UNEMPLOYMENT_SALARY_CERTIFICATE",
    "SHORT_TIME_WORK",
    "DAILY_ALLOWANCES",
    "ASSET_INCOME",
    "DIVIDENDS",
    "CONFIRMATION_ALIMONY",
    "INCOME_MISC",
    "LEASING_AGREEMENT",
    "CONSUMER_LOAN",
    "CREDIT_CARD_BILL",
]

RENOVATIONS = [
    "LIST_OF_RENOVATIONS",
    "CONSTRUCTION_COST_ESTIMATE",
    "CONSTRUCTION_QUOTATION",
    "PROJECT_BUDGET",
    "BILL_MISC",
]

ALL_GROUP_TYPES: dict[str, List[str]] = {
    "PROOF_OF_INCOME": PROOF_OF_INCOME,
    "PILLAR_TWO_MISC": PILLAR_TWO_MISC,
    "PLAN_ANY": PLAN_ANY,
    "PLAN_SITUATION": PLAN_SITUATION,
    "EXTRACT_FROM_LAND_REGISTER": EXTRACT_FROM_LAND_REGISTER,
    "RENOVATIONS": RENOVATIONS,
}

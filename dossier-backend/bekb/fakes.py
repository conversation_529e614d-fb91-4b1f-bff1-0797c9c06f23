import json
import random
from datetime import timed<PERSON>ta
from typing import Optional, List, Tuple, Dict

import structlog
from django.utils import timezone as django_timezone
from faker import Faker
from faker.providers import person, address, internet, date_time, lorem

from assets import ASSETS_PATH
from bekb.bekb_instance_type import BekbInstanceType
from bekb.bekbload import (
    load_document_category2ekd_mappings,
    load_bekb_external_document_categories,
)
from bekb.collaterals import (
    get_collateral_requirement_satisfaction_options,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN,
    COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS,
    COLLATERAL_TYPE_KEY_GRUNDPFAND,
)
from bekb.data import DATA_PATH
from bekb.models import (
    Attribute,
    BEKBDossierProperties,
    BusinessCase,
    Collateral,
    RealEstateProperty,
    CollateralAssignment,
    CollateralRealEstateProperty,
)
from bekb.schemas import schemas
from bekb.schemas.schemas_fipla import DossierShowJWTFipla, DossierCreateJWTFipla
from bekb.services import (
    create_bekb_dossier,
    update_or_create_user,
    create_bekb_export_file,
    load_bekb_attributes,
    VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
)
from doccheck import DOCCHECK_PATH
from doccheck.export import (
    import_doccheck,
    sync_doccat_names_from_account,
    sync_businesscase_types_from_account,
)
from doccheck.models import DocCheck
from dossier.doc_cat_helpers import load_document_categories_from_path
from dossier.fakes import add_some_fake_semantic_documents
from dossier.helpers_timezone import create_faker_past_datetime_with_timezone
from dossier.models import (
    Account,
    DossierRole,
    Dossier,
    BusinessCaseType,
    DocumentCategory,
)
from dossier.services import (
    change_dossier_work_status,
    create_expiration_date,
    create_businesscase_type_import,
)
from statemgmt import STATEMGMT_PATH
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine, Status

logger = structlog.get_logger()

PATH_CURRENT_BEKB_STATEMGMT_EXPORT = (
    STATEMGMT_PATH / "configurations/bekb/bekb_state_machine_20240420.json"
)


def create_business_types(account) -> List[Attribute]:
    # Attribute table is a BEKB specific table
    # Hence this is unique to BEKB
    values = []
    business_type, _ = Attribute.objects.update_or_create(
        dict(name_de="Neugeschäft", name_fr="Neugeschäft fr"),
        account=account,
        key="NEW",
        entity=Attribute.Entity.BusinessType.value,
    )
    values.append(business_type)
    business_type, _ = Attribute.objects.update_or_create(
        dict(name_de="Ablöser", name_fr="Ablöser fr"),
        account=account,
        key="RESCHEDULE",
        entity=Attribute.Entity.BusinessType.value,
    )
    values.append(business_type)
    return values


def create_sample_business_status(account) -> List[Attribute]:
    values = []
    value, _ = Attribute.objects.update_or_create(
        dict(
            entity=Attribute.Entity.BusinessStatus.value,
            name_de="aktiv",
            name_fr="aktiv fr",
        ),
        account=account,
        key="ACTIVE",
        entity=Attribute.Entity.BusinessStatus.value,
    )
    values.append(value)

    value, _ = Attribute.objects.update_or_create(
        dict(name_de="gelöscht", name_fr="gelöscht fr"),
        account=account,
        key="DELETED",
        entity=Attribute.Entity.BusinessStatus.value,
    )
    values.append(value)
    return values


def create_a_sample_account() -> Account:
    account_key = schemas.AccountNameMortgage.bekbe
    account = Account.objects.create(
        name=account_key, key=account_key, default_bucket_name="dms-default-bucket"
    )
    return account


def create_some_users(account):
    external_users = [
        schemas.User(
            username="<EMAIL>",
            firstname="firstname active",
            name="lastname active",
            pers=False,
            active=True,
        ),
        schemas.User(
            username="<EMAIL>",
            firstname="firstname not active",
            name="lastname not active",
            pers=False,
            active=False,
        ),
        schemas.User(
            username="<EMAIL>",
            firstname="firstname not active and pers",
            name="lastname not active and pers",
            pers=True,
            active=False,
        ),
    ]
    for user in external_users:
        update_or_create_user(user, account)
    return external_users


def update_or_create_real_estate_property_status(account) -> List[Attribute]:
    # Unique to BEKB and possibly ZKB
    Attribute.objects.update_or_create(
        dict(name_de="aktiv", name_fr="aktiv fr"),
        account=account,
        key="ACTIVE",
        entity=Attribute.Entity.RealEstatePropertyStatus.value,
    )
    Attribute.objects.update_or_create(
        dict(name_de="gelöscht", name_fr="gelöscht fr"),
        account=account,
        key="DELETED",
        entity=Attribute.Entity.RealEstatePropertyStatus.value,
    )
    real_estate_property_status = Attribute.objects.filter(
        entity=Attribute.Entity.RealEstatePropertyStatus.value, account=account
    ).all()
    return list(real_estate_property_status)


def update_or_create_property_collateral_types(account) -> List[Attribute]:
    # Collaterals are unique to BEKB
    # Once a document is finished and goes to the archive, we can attach a collateral to it
    # The collateral is an asset that is used to secure the loan (e.g. a house)
    # There are rules which collateral can be used for which loan
    Attribute.objects.update_or_create(
        dict(name_de=COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT, name_fr="fr-Hauptdeckung"),
        account=account,
        key="0",  # This is "0" in prod
        entity=Attribute.Entity.PropertyCollateralType.value,
    )
    Attribute.objects.update_or_create(
        dict(name_de=COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN, name_fr="fr-Nebendeckung"),
        account=account,
        key="1",  # This is '1' in prod
        entity=Attribute.Entity.PropertyCollateralType.value,
    )
    Attribute.objects.update_or_create(
        dict(
            name_de=COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS,
            name_fr="fr-Verwaltungsgeschäft",
        ),
        account=account,
        key="2",  # This is the same in prod
        entity=Attribute.Entity.PropertyCollateralType.value,
    )
    return list(
        Attribute.objects.filter(
            entity=Attribute.Entity.PropertyCollateralType.value, account=account
        ).all()
    )


def update_or_create_real_estate_property_types(account) -> List[Attribute]:
    # BEKB specific, might be needed for ZKB
    classifier_exported_property_type = [
        {
            "uuid": "45ebeb88-2b82-4a5e-80bd-c323deb8e558",
            "id": "A",
            "name": "Einfamilienhaus",
        },
        {
            "uuid": "ff999970-6f3a-460f-9cf8-c8ce2f95d26a",
            "id": "B",
            "name": "Eigentumswohnung",
        },
        {"uuid": "679327d8-e3cf-4e34-89fc-ec57990cfceb", "id": "C", "name": "Garage"},
        {
            "uuid": "2ed43da0-1d95-4cb6-8f76-c8c26363d842",
            "id": "D",
            "name": "Sonderobjekt",
        },
        {
            "uuid": "ce489cd6-f3e7-40e7-9e22-2f7776092fc5",
            "id": "E",
            "name": "Zweifamilienhaus",
        },
        {
            "uuid": "91ddc15b-206f-42f9-9e31-8d7ff2744d26",
            "id": "F",
            "name": "Mehrfamilienhaus",
        },
        {
            "uuid": "81eb1ed3-8241-4d6f-b344-6ec31c880a43",
            "id": "G",
            "name": "Wohn- und Geschäftshaus",
        },
        {
            "uuid": "3e732c20-3c5d-452a-851f-83299f1b6ce9",
            "id": "H",
            "name": "Geschäftsliegenschaft",
        },
        {
            "uuid": "e04147bf-023f-4a02-a810-4f2bbc4d7712",
            "id": "I",
            "name": "Gewerbeliegenschaft",
        },
        {"uuid": "755174fe-9e73-4f7b-b471-91fd57f2ad86", "id": "J", "name": "Hotel"},
        {
            "uuid": "e62c2f86-ac59-4b10-9bff-6a33b1496224",
            "id": "K",
            "name": "Restaurant/Gasthof",
        },
        {
            "uuid": "8c71c86e-78a7-416b-b4a5-27d9a4dd3b32",
            "id": "L",
            "name": "Industrieliegenschaft",
        },
        {"uuid": "1df2e5bb-ccc8-49c9-bbee-db882b96b995", "id": "M", "name": "Bauland"},
        {
            "uuid": "05d741da-da81-4079-88a6-3ff1f44bd5e5",
            "id": "N",
            "name": "Landwirtschaft",
        },
        {
            "uuid": "ce07fb87-7834-4e4f-814e-9ea7ecc6d3ed",
            "id": "O",
            "name": "Alters-/Pflegeheim Spital",
        },
    ]

    for property_type_from_json in classifier_exported_property_type:
        Attribute.objects.update_or_create(
            dict(
                name_de=property_type_from_json["name"],
                name_fr=f"{property_type_from_json['name']} fr",
            ),
            account=account,
            key=property_type_from_json["id"],
            entity=Attribute.Entity.PropertyType.value,
        )

    return list(
        Attribute.objects.filter(
            entity=Attribute.Entity.PropertyType.value, account=account
        ).all()
    )


def create_some_dossiercreatejwt(number_of_dossier):
    created_dossiers = []
    for i in range(number_of_dossier):
        dossier_create_data = schemas.DossierCreateJWTMortgage(
            exp=int((django_timezone.now() + timedelta(minutes=60)).timestamp()),
            account_name=schemas.AccountNameMortgage.bekbe,
            business_partner=schemas.BusinessPartner(
                parkey=f"gp {i}",
                name=f"testname {i}",
                firstname=f"test firstname {i}",
                pers=random.choice([True, False]),
            ),
            partner_partner=schemas.Partner(
                parkey=f"pp {i}",
                name=f"geschäftspartner name {i}",
                firstname=f"test geschäftspartner firstname {i}",
            ),
            dossier_name=schemas.DossierName(f"my dossier name  {i}"),
            language=random.choice(list(schemas.Langugage)),
            pers=random.choice([True, False]),
            fico=schemas.User(
                username=f"testfico{i}@test.bekb.ch",
                firstname="Willi",
                name="Ombrecht",
                pers=False,
                active=True,
            ),
            current_user=schemas.User(
                username=f"somother{i}@test.bekb.ch",
                firstname="Willi",
                name="Ombrecht",
                pers=random.choice([True, False]),
                active=True,
            ),
        )
        created_dossiers.append(dossier_create_data)
    return created_dossiers


def update_or_create_collateral_types(account):
    """
    Create or update BEKB collateral types from json file which can be exported from central classifier instance.
    There are 11 collateral types and they are not expected to change often.
    """
    collateral_type_data = json.loads(
        (DATA_PATH / "bekb_collateral_types.json").read_text()
    )
    for collateral_type in collateral_type_data:
        Attribute.objects.update_or_create(
            dict(
                name_de=collateral_type["name"], name_fr=f"{collateral_type['name']} fr"
            ),
            account=account,
            key=collateral_type["id"],
            entity=Attribute.Entity.CollateralType.value,
        )

    return list(
        Attribute.objects.filter(
            entity=Attribute.Entity.CollateralType.value, account=account
        ).all()
    )


def update_or_create_simple_business_states(account):
    # This is specific to BEKB
    Attribute.objects.update_or_create(
        dict(name_de="aktiv", name_fr="aktiv fr"),
        account=account,
        key="ACTIVE",
        entity=Attribute.Entity.BusinessStatus.value,
    )

    Attribute.objects.update_or_create(
        dict(name_de="gelöscht", name_fr="gelöscht fr"),
        account=account,
        key="DELETED",
        entity=Attribute.Entity.BusinessStatus.value,
    )
    return Attribute.objects.filter(
        entity=Attribute.Entity.BusinessStatus.value, account=account
    ).all()


def load_bekb_document_categories(account_key: str):
    account = Account.objects.get(key=account_key)

    # Load older BEKB doc cat export, then apply all patches
    # This can be replaced by a newer full export
    filename = (
        DATA_PATH
        / "document_categories"
        / "241230_new_finpla_docs/before/CoreDocumentCategory-2024-12-30.json"
    )
    filename = None
    load_document_categories_from_path(account, filename)

    document_categories_json_path_custom = (
        ASSETS_PATH / "document_category/bekb/DocumentCategory-2025-03-15bekb.json"
    )
    load_document_categories_from_path(
        account, document_categories_json_path=document_categories_json_path_custom
    )
    handle_update_ekd_ids_and_external_titles(
        account_key, BekbInstanceType.MORTGAGE, dry_run=False
    )

    # # Add PROOF_OF_INCOME, RENOVATIONS, BEKB_EKD118
    # add_doccats_for_new_businesscases(account.key, False)
    #
    # # Add myky dossier document category
    # add_bekbconf_ekd142(account.key, False, False)


def handle_update_ekd_ids_and_external_titles(
    account_key: str, instance_type: BekbInstanceType, dry_run: bool
):

    account = Account.objects.get(key=account_key)
    assert account
    mappings = load_document_category2ekd_mappings(instance_type)
    assert mappings

    ext_doccats = load_bekb_external_document_categories()
    ext_doccats_map = {}
    for d in ext_doccats:
        ext_doccats_map[d["docid_external"]] = d
    assert ext_doccats
    assert len(ext_doccats) == len(ext_doccats_map)

    doc_cats = DocumentCategory.objects.filter(account=account)
    changed_id = []
    changed_external = []
    for dc in doc_cats:
        changed = False
        ekd_mapping_found = dc.name in mappings.keys()
        if ekd_mapping_found:
            new_ekd = mappings[dc.name]

            if new_ekd in dc.id:
                logger.info(
                    "Skip update of id, because same EKD number already set",
                    id=dc.id,
                    name=dc.name,
                    de=dc.de,
                    new_ekd=new_ekd,
                )
            else:
                old_id = dc.id
                if "-" in old_id:
                    # This one already had an EKD number so update it
                    old_id = old_id.split("-")[0]
                    logger.warn(
                        "CHANGED EXISTING EKD NUMBER",
                        old_id=old_id,
                        dc_id=dc.id,
                        new_id=f"{old_id}-{new_ekd}",
                        name=dc.name,
                        de=dc.de,
                    )
                dc.id = f"{old_id}-{new_ekd}"
                changed = True
                changed_id.append(dc)
                logger.info(
                    "Changed id", old_id=old_id, new_id=dc.id, name=dc.name, de=dc.de
                )
        else:
            logger.info("No mapping found for doc cat", name=dc.name, de=dc.de)

        if not ekd_mapping_found:
            logger.debug(
                "Skip update because no ekd found", id=dc.id, name=dc.name, de=dc.de
            )
        elif dc.de_external:
            logger.info(
                "Skip update of external_de, because already set",
                id=dc.id,
                name=dc.name,
                de=dc.de,
            )
        elif new_ekd in ext_doccats_map:
            old_de_external = dc.de_external
            new_de_external = ext_doccats_map[new_ekd]["de_external"]
            dc.de_external = new_de_external
            dc.fr_external = ext_doccats_map[new_ekd]["fr_external"]
            changed = True
            changed_external.append(dc)
            logger.info(
                "Update external_de",
                old_de_external=old_de_external,
                new_de_external=dc.de_external,
                name=dc.name,
                de=dc.de,
            )
        else:
            logger.info(
                "No external doc cat found, documents with this cat will not be archived.",
                name=dc.name,
            )

        if changed and not dry_run:
            dc.save()

    logger.info(
        "changed doc cats",
        num_doc_cats=len(doc_cats),
        num_changed_ids=len(changed_id),
        num_changed_external=len(changed_external),
        dry_run=dry_run,
    )
    if changed_id:
        logger.info("changed ids", changed_id=changed_id)


def get_doc_cat_names_ignored_bekb():
    # These document categories are configured but will never be archived
    # So ignore them for mapping test
    doc_cat_names_ignored = [
        "UNKNOWN_DE",
        "UNKNOWN_EN",
        "UNKNOWN_FR",
        "UNKNOWN_IT",
        "UNKNOWN",
        "WHITE_PAGES",
        "TAX_ATTACHMENTS",
        "IMMUTABLE_XLS",
        "TAX_ATTACHMENTS",
        "PROCESSING_ERROR",
        "BEKB_FET",
        "ZKB_GVZ_VIEWER",
        "BEKB_GRUNDSATZ",
        "MB_SOKO",
        "DEBT_COLLECTION_INFORMATION_ORDER_VD",  # legacy, removed 231108
    ]

    # These document categories are configured but BEKB wants that they are intentionally
    # skipped during export. Therefore no mapping is needed
    doc_cat_names_intentionally_ignored_by_bekb = [
        "DONATION_CONFIRMATION",
        "DAYCARE_CONFIRMATION",
        "HEALTH_INSURANCE",
        "YELLOW_IDENTIFICATION_POST",
    ]
    return doc_cat_names_ignored + doc_cat_names_intentionally_ignored_by_bekb


def handle_check_ekd_ids_and_external_titles(account_key: str):
    account = Account.objects.get(key=account_key)
    assert account
    doc_cats = DocumentCategory.objects.filter(account=account).order_by("id")
    assert doc_cats

    dc_without_ekd = []
    dc_without_external_title = []
    dc_ignored = []
    for dc in doc_cats:
        if dc.name not in get_doc_cat_names_ignored_bekb():
            if "EKD" not in dc.id:
                dc_without_ekd.append(dc)
            if not dc.de_external or not dc.fr_external:
                dc_without_external_title.append(dc)
        else:
            dc_ignored.append(dc)

    logger.info(
        f"Found {len(dc_without_ekd)} document categories without EKD number. Also found {len(dc_ignored)} ignored categories (ok, if they have no EKD)"
    )
    for dc in dc_without_ekd:
        logger.info("  No EKD: ", dc=dc)
    logger.info(
        f"Found {len(dc_without_external_title)} document categories without external title"
    )
    for dc in dc_without_external_title:
        logger.info("  No external title: ", dc=dc)


def handle_check_bekb_config(
    account_key: str, instance_type: BekbInstanceType
) -> Tuple[Dict[str, DocumentCategory], Dict[str, str]]:
    """

    @param instance_type:
    @param account_key:
    @return: 2 Dicts. First Dict is document_category_key -> DocumentCategory,
    second is document_category_key -> EKDxxx number
    """
    account = Account.objects.get(key=account_key)
    assert account

    doc_cats_all: List[DocumentCategory] = list(
        DocumentCategory.objects.filter(account__key=account_key).all()
    )
    doc_cats_all.sort(key=lambda dc: dc.id)
    doc_cat_map = {}

    for dc in doc_cats_all:
        if dc.name not in get_doc_cat_names_ignored_bekb():
            doc_cat_map[dc.name] = dc

    mappings = load_document_category2ekd_mappings(instance_type)

    # These mappings have once been configured but the document category is no longer available.
    # They can be ignored
    doc_cat_mappings_legacy = ["TAX_ATTACHMENTS"]

    # These 2 are hardcoded for FIPLA and do not exist in json config or in mortgage doc catalog
    doc_cat_mappings_legacy += [
        "BEKB_EKD121_SIMULATION1_REQUEST",
        "BEKB_EKD121_SPLITTING_APPLICATION",
    ]

    logger.info(f"Ignore legacy mappings: {doc_cat_mappings_legacy}")
    for dc_key in doc_cat_mappings_legacy:
        if dc_key in mappings:
            mappings.pop(dc_key)
        if dc_key in doc_cat_map:
            doc_cat_map.pop(dc_key)

    missing_in_doc_cat_list = []
    for doc_cat_name, dc in mappings.items():
        if doc_cat_name not in doc_cat_map.keys():
            missing_in_doc_cat_list.append(doc_cat_name)
    assert (
        not missing_in_doc_cat_list
    ), f"{missing_in_doc_cat_list} missing in document categories list. They need to be added to the document categories."

    missing_in_doc_cat_mapping = []
    for doc_cat_name, dc in doc_cat_map.items():
        if doc_cat_name not in mappings:
            missing_in_doc_cat_mapping.append(doc_cat_name)
    assert (
        not missing_in_doc_cat_mapping
    ), f"{missing_in_doc_cat_mapping} missing in load_document_category2ekd_mappings. They need to be added to classifier and then exported to bekb/data/document_category_mapping: https://classifier.swarm-v2.hypodossier.ch/admin/bekb/bekbdocumentcategorymapping/"

    doc_cat_keys_in_doc_cat_list = doc_cat_map.keys()
    doc_cat_keys_in_ekd_mapping = mappings.keys()

    missing_doc_cat_key_in_doc_cat_list = [
        key
        for key in doc_cat_keys_in_ekd_mapping
        if key not in doc_cat_keys_in_doc_cat_list
    ]
    missing_doc_cat_key_in_mapping = [
        key
        for key in doc_cat_keys_in_doc_cat_list
        if key not in doc_cat_keys_in_ekd_mapping
    ]

    if missing_doc_cat_key_in_doc_cat_list:
        logger.info(
            f"doc cat with mapping but not in DocumentCategory: {missing_doc_cat_key_in_doc_cat_list}"
        )
    if missing_doc_cat_key_in_mapping:
        logger.info(
            f"doc cat in DocumentCategory but mapping missing: {missing_doc_cat_key_in_mapping}"
        )

    return doc_cat_map, mappings


PATH_CURRENT_BEKB_BUSINESSCASE_TYPES = (
    DATA_PATH / "businesscase_types/mortgage/bekb_businesscase_types_20240326.json"
)


class BekbAccountFactoryFaker:
    def __init__(
        self,
        update_statemgmt: bool = False,
        update_doccheck: bool = False,
        update_attributes: bool = False,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
        default_bucket_name: Optional[str] = None,
    ):
        """
        Create or update test environment for BEKB.

        @param account: If this is set then use the provided account. Else set up or update account
        @param update_statemgmt:
        @param update_doccheck:
        """

        if account is None:
            if account_key is None:
                account_key = schemas.AccountNameMortgage.bekbe.value

            state_machine_name = "Dossier Status BEKB Initial"
            state_machine = StateMachine.objects.filter(name=state_machine_name).first()
            if update_statemgmt or state_machine is None:
                p = PATH_CURRENT_BEKB_STATEMGMT_EXPORT
                assert p.exists()
                state_machine = update_state_machine(p, state_machine_name)

            assert state_machine is not None

            doc_check_key = account_key
            if update_doccheck:
                p = DOCCHECK_PATH / "configurations/bekb_doccheck_240522.json"
                assert p.exists()
                import_doccheck(p, doc_check_key)

            doccheck = DocCheck.objects.filter(key=doc_check_key).first()
            if update_doccheck:
                assert doccheck is not None

            account, created = Account.objects.update_or_create(
                dict(
                    name=f"Account {account_key}",
                    default_bucket_name="dms-default-bucket",
                    active_work_status_state_machine=state_machine,
                    active_doc_check=doccheck,
                    dmf_endpoint="https://www.localhost",
                ),
                key=account_key,
            )

            if update_attributes:
                load_bekb_attributes(account.key)

            # These settings are different from prod
            account.enable_button_create = True

            # These settings are the same as in prod
            account.enable_dossier_search = False  # don't know what this is for

            account.default_dossier_expiry_duration_days = 1080
            account.max_dossier_expiry_duration_days = 1080
            account.show_document_category_external = True
            account.show_business_case_type = True
            account.enable_rendering_structure_tab = False
            account.enable_rendering_structure_details_tab = True
            account.enable_rendering_bekb_mortgage_archiving_tab = True

            account.enable_download_document = True
            account.enable_bekb_export = True
            account.enable_bekb_automatic_collateral = True

            account.enable_download_extraction_excel = False
            account.enable_dossier_permission = False
            account.enable_semantic_page_image_lazy_loading = True
            account.enable_rendering_plans_tab = True
            account.enable_documents_delta_view = True

            account.enable_dossier_assignment = True
            account.enable_dossier_assignment_to_someone_else = True

            account.frontend_theme = ""
            account.photo_album_docx_template = ""
            account.valid_dossier_languages = ["De", "Fr"]
            account.valid_ui_languages = ["de", "fr", "en"]

            if default_bucket_name is not None:
                account.default_bucket_name = default_bucket_name

            account.save()

        self.account = account

        self.load_initial_document_categories()

        self.collateral_types = update_or_create_collateral_types(account)
        for ct in self.collateral_types:
            if ct.name_de == "Grundpfand":
                self.attribute_collateral_grundpfand = ct
            if ct.name_de == "Personenversicherung":
                self.attribute_collateral_personenversicherung = ct
        assert self.attribute_collateral_grundpfand
        assert self.attribute_collateral_personenversicherung

        for ct in self.collateral_types:
            if ct.key == COLLATERAL_TYPE_KEY_GRUNDPFAND:
                self.collateral_type_grundpfand = ct
                break
        assert self.collateral_type_grundpfand

        # Dict[id of collateral -> Attribute of collateral]
        self.collateral_types_map = {
            ctype.key: ctype for ctype in self.collateral_types
        }
        self.business_types = create_business_types(account)
        self.real_estate_property_status = update_or_create_real_estate_property_status(
            account
        )

        Attribute.objects.update_or_create(
            dict(name_de="aktiv", name_fr="aktiv fr"),
            account=account,
            key="ACTIVE",
            entity=Attribute.Entity.CollateralStatus.value,
        )

        Attribute.objects.update_or_create(
            dict(name_de="gelöscht", name_fr="gelöscht fr"),
            account=account,
            key="DELETED",
            entity=Attribute.Entity.CollateralStatus.value,
        )

        self.collateral_status_list = list(
            Attribute.objects.filter(
                entity=Attribute.Entity.CollateralStatus.value, account=account
            ).all()
        )

        self.property_types = update_or_create_real_estate_property_types(account)

        self.property_collateral_types = update_or_create_property_collateral_types(
            account
        )
        assert self.property_collateral_types
        for pct in self.property_collateral_types:
            if pct.name_de == COLLATERAL_PROPERTY_TYPE_NAME_DE_HAUPT:
                self.property_collateral_type_haupt = pct
            elif pct.name_de == COLLATERAL_PROPERTY_TYPE_NAME_DE_VERWALTUNGS:
                self.property_collateral_type_verwaltungs = pct
            elif pct.name_de == COLLATERAL_PROPERTY_TYPE_NAME_DE_NEBEN:
                self.property_collateral_type_neben = pct
        assert self.property_collateral_type_haupt
        assert self.property_collateral_type_verwaltungs
        assert self.property_collateral_type_neben

        self.business_states = update_or_create_simple_business_states(account)
        assert self.business_states

        DossierRole.objects.get_or_create(
            defaults=dict(
                name_de="FICO", name_fr="FICO", name_en="FICO", name_it="FICO"
            ),
            key="FICO",
            user_selectable=False,
            show_separate_filter=True,
            account=account,
        )

        DossierRole.objects.get_or_create(
            defaults=dict(
                name_de="Zuständiger",
                name_fr="Responsable",
                name_en="Assignee",
                name_it="Assegnatario",
            ),
            key="ASSIGNEE",
            user_selectable=True,
            show_separate_filter=True,
            account=account,
        )

        faker = Faker(locale="de_CH")
        faker.add_provider(person)
        faker.add_provider(address)
        faker.add_provider(internet)
        faker.add_provider(date_time)
        faker.add_provider(lorem)
        self.faker = faker

        self.businesscase_types: List[BusinessCaseType] = (
            create_businesscase_type_import(
                PATH_CURRENT_BEKB_BUSINESSCASE_TYPES, account_key
            )
        )

        if update_doccheck:
            # Must be done after document categories are loaded in account
            sync_doccat_names_from_account(
                account_key=account.key, doc_check_key=doc_check_key
            )

            # Must be done after business case types are loaded for account
            sync_businesscase_types_from_account(
                account_key=account.key, doc_check_key=doc_check_key
            )

    def load_initial_document_categories(self):

        num_expected_categories = 329

        load_bekb_document_categories(self.account.key)

        self.document_categories = list(
            DocumentCategory.objects.filter(account=self.account).all()
        )

        assert (
            len(self.document_categories) == num_expected_categories
        ), f"Instead of {num_expected_categories}, {len(self.document_categories)} have been found "

        return self.document_categories

    def create_dossier(
        self,
        business_parkey=None,
        current_user: schemas.User = None,
        pers: bool = random.choice([True, False]),
        dossier_name: str = None,
    ) -> BEKBDossierProperties:
        business_partner = self.create_business_partner(
            parkey=business_parkey, pers=pers
        )
        partner_partner = self.create_partner_partner()

        fico = self.create_user(pers=pers)

        if current_user is None:
            current_user = self.create_user(pers=pers)

        if dossier_name is None:
            dossier_name = (
                f"Dossier von {business_partner.firstname} {business_partner.name}"
            )

        return create_bekb_dossier(
            self.account,
            schemas.DossierCreateJWTMortgage(
                exp=0,
                account_name=schemas.AccountNameMortgage(self.account.key),
                business_partner=business_partner,
                partner_partner=partner_partner,
                dossier_name=dossier_name,
                language=random.choice(list(schemas.Langugage)),
                fico=fico,
                current_user=current_user,
            ),
            use_fico=True,
        )

    def create_business_partner(
        self, parkey=None, pers: bool = random.choice([True, False])
    ):
        if parkey is None:
            effective_parkey = self.faker.bothify("#########")
        else:
            effective_parkey = parkey

        business_partner = schemas.BusinessPartner(
            parkey=effective_parkey,
            name=self.faker.last_name(),
            firstname=self.faker.first_name(),
            pers=pers,
        )
        return business_partner

    def create_fico(self, pers: bool = random.choice([True, False])):
        return self.create_user(pers=pers)

    def create_user(self, pers: bool = random.choice([True, False])):
        current_user = schemas.User(
            firstname=self.faker.first_name(),
            name=self.faker.last_name(),
            username=self.faker.email(domain=f"{self.account.key}.ch"),
            active=random.choice([True, False]),
            pers=pers,
        )
        return current_user

    def create_partner_partner(self) -> Optional[schemas.Partner]:
        partner_partner = schemas.Partner(
            parkey=self.faker.bothify("#########"),
            name=self.faker.last_name(),
            firstname=self.faker.first_name(),
        )
        return partner_partner

    def create_businesscase(self, bekb_dossier: BEKBDossierProperties):
        business_number = self.faker.bothify("#########")

        # Check if the generated business_number is unique for the account and business_partner:
        if BusinessCase.objects.filter(
            account=self.account,
            business_partner=bekb_dossier.business_partner,
            business_number=business_number,
        ).exists():
            # If it's not unique, generate a new one:
            business_number = self.faker.bothify("#########")

        be, _ = BusinessCase.objects.get_or_create(
            account=self.account,
            business_partner=bekb_dossier.business_partner,
            business_number=business_number,
            business_type=random.choice(self.business_types),
            business_status=random.choice(self.business_states),
            mutation_date=self.faker.past_date(),
            # mutation_date=datetime.combine(
            #     self.faker.past_date(), datetime.min.time()
            # ).replace(tzinfo=datetime_timezone.utc),
            mutation_user=self.faker.bothify("???##"),
        )

        return be

    def create_collateral(
        self,
        bekb_dossier: BEKBDossierProperties,
        business_case: BusinessCase,
        collateral_type: List[Optional[Attribute]] = None,
        property_collateral_types: Optional[List[Attribute]] = None,
    ) -> Collateral:
        if collateral_type is None:
            collateral_type = random.choice(self.collateral_types)

        if property_collateral_types is None:
            property_collateral_types = self.property_collateral_types

        description = None
        # if random.randint(0, 3) > 2:
        description = self.faker.sentence(5)

        policy_number = None
        if collateral_type.key == "2":
            policy_number = self.faker.bothify("Police Nr. ##?##?##?")

        collateral = Collateral.objects.create(
            account=self.account,
            business_partner=bekb_dossier.business_partner,
            businesscase=business_case,
            collateral_number=self.faker.bothify("#########"),
            collateral_partner=bekb_dossier.business_partner,  ## todo can be another partner
            document_parkey="0",
            collateral_type=collateral_type,
            description=description,
            collateral_status=random.choice(self.collateral_status_list),
            policy_number=policy_number,
        )

        if collateral_type.key == COLLATERAL_TYPE_KEY_GRUNDPFAND:
            realestate_property, _ = RealEstateProperty.objects.update_or_create(
                dict(
                    property_partner=bekb_dossier.business_partner,
                    property_type=random.choice(self.property_types),
                    address_street=self.faker.street_name(),
                    address_street_nr=(
                        self.faker.building_number() if random.random() > 0.8 else None
                    ),
                    address_zip=self.faker.postcode(),
                    address_city=self.faker.city(),
                    land_register_municipality=(
                        self.faker.city() if random.random() > 0.8 else None
                    ),
                    land_register_id=(
                        self.faker.bothify("Reg.Nr ??######")
                        if random.random() > 0.8
                        else None
                    ),
                    status=random.choice(self.real_estate_property_status),
                ),
                account=self.account,
                property_number=self.faker.bothify("PrN######"),
            )

            CollateralRealEstateProperty.objects.create(
                account=self.account,
                property_collateral_type=random.choice(property_collateral_types),
                collateral=collateral,
                realestate_property=realestate_property,
            )

        collateral.save()
        return collateral

    def create_ready_for_export_dossier(
        self,
        min_num_documents: int = 0,
        max_num_documents: int = 10,
        possible_work_status_keys: List[
            str
        ] = VALID_READY_FOR_EXPORT_STATUS_KEYS_MORTGAGE,
        valid_document_category_keys: Optional[List[str]] = None,
        allow_empty_docs: bool = True,
        max_pages=10,
        min_num_pages=1,
        dossier_name: str = None,
    ) -> BEKBDossierProperties:

        assert possible_work_status_keys

        # Create a dossier with 80% probability non-pers and 20% probability pers
        bekb_dossier: BEKBDossierProperties = self.create_dossier(
            pers=random.randint(0, 10) > 8,
            dossier_name=dossier_name,
        )
        dossier = bekb_dossier.dossier
        num_docs = random.randint(min_num_documents, max_num_documents)
        semantic_documents = add_some_fake_semantic_documents(
            dossier,
            num_docs=num_docs,
            valid_document_category_keys=valid_document_category_keys,
            allow_empty_docs=allow_empty_docs,
            max_pages=max_pages,
            min_num_pages=min_num_pages,
        )
        business_case = self.create_businesscase(bekb_dossier)

        # Only use non-empty mappings here for the export
        requirements_satisfaction_options = (
            get_collateral_requirement_satisfaction_options(
                self.collateral_types_map, add_empty_mappings=False
            )
        )

        mappings = load_document_category2ekd_mappings(BekbInstanceType.MORTGAGE)

        for semantic_document in semantic_documents:
            # documents without ekd numbers should not be archived
            if semantic_document.document_category.name not in mappings.keys():
                semantic_document.delete()
                continue

            options = requirements_satisfaction_options.get(
                semantic_document.document_category.name
            )
            # satisfy the requirement with a random option

            if options is None:
                # So no collateral is required
                continue

            required_collateral_type = random.choice(options)

            collateral: Collateral = self.create_collateral(
                bekb_dossier, business_case, required_collateral_type
            )
            realestate_property = None
            if collateral.collateral_type.key == "1":
                realestate_property, _ = RealEstateProperty.objects.update_or_create(
                    dict(
                        property_partner=bekb_dossier.business_partner,
                        property_type=random.choice(self.property_types),
                        address_street=self.faker.street_name(),
                        address_street_nr=(
                            self.faker.building_number()
                            if random.random() > 0.8
                            else None
                        ),
                        address_zip=self.faker.postcode(),
                        address_city=self.faker.city(),
                        land_register_municipality=(
                            self.faker.city() if random.random() > 0.8 else None
                        ),
                        land_register_id=(
                            self.faker.bothify("Reg.Nr ??######")
                            if random.random() > 0.8
                            else None
                        ),
                        status=random.choice(self.real_estate_property_status),
                    ),
                    account=self.account,
                    property_number=self.faker.bothify("PrN######"),
                )

                CollateralRealEstateProperty.objects.create(
                    account=self.account,
                    property_collateral_type=random.choice(
                        self.property_collateral_types
                    ),
                    collateral=collateral,
                    realestate_property=realestate_property,
                )

            CollateralAssignment.objects.create(
                account=self.account,
                semantic_document=semantic_document,
                collateral=collateral,
                property=realestate_property,
            )

        possible_work_status_list = list(
            Status.objects.filter(key__in=possible_work_status_keys).all()
        )

        dossier.work_status = random.choice(possible_work_status_list)

        dossier.save()
        bekb_dossier.business_case = business_case
        bekb_dossier.save()

        return bekb_dossier

    def create_export_archive_available_dossier(
        self,
        min_num_documents: int = 0,
        max_num_documents: int = 10,
        possible_work_status_keys=["READY_FOR_EXPORT_NO_DEAL", "READY_FOR_EXPORT_DEAL"],
        valid_document_category_keys=None,
    ):
        bekb_dossier = self.create_ready_for_export_dossier(
            min_num_documents=min_num_documents,
            max_num_documents=max_num_documents,
            valid_document_category_keys=valid_document_category_keys,
            possible_work_status_keys=possible_work_status_keys,
        )

        create_bekb_export_file(bekb_dossier, is_bekb_fipla=False)
        dossier = bekb_dossier.dossier

        assert self.account.active_work_status_state_machine is not None
        new_status = self.get_status_by_key("EXPORT_ARCHIVE_AVAILABLE")

        change_dossier_work_status(
            dossier, dict(is_system=True, is_user=False), new_status
        )
        dossier.save()
        return bekb_dossier

    def get_status_by_key(self, key: str):
        status = Status.objects.get(
            state_machine=self.account.active_work_status_state_machine, key=key
        )
        return status

    def create_sample_dossier(
        self,
        pers: bool = random.randint(0, 10) > 8,
        dossier_name: str = None,
        num_business_cases: int = random.randint(0, 10),
        num_collaterals: int = random.randint(0, 10),
        assign_business_case_to_dossier: bool = random.randint(0, 1),
        collateral_type: List[Optional[Attribute]] = None,
        property_collateral_types: List[Optional[Attribute]] = None,
        add_semantic_documents: bool = True,
        valid_document_category_keys: Optional[List[str]] = None,
    ):
        new_bekb_dossier = self.create_dossier(pers=pers, dossier_name=dossier_name)
        new_bekb_dossier.created_at = create_faker_past_datetime_with_timezone(
            self.faker
        )
        new_bekb_dossier.dossier.created_at = new_bekb_dossier.created_at

        business_cases = []
        for i in range(num_business_cases):
            business_case = self.create_businesscase(new_bekb_dossier)
            for i in range(num_collaterals):
                self.create_collateral(
                    new_bekb_dossier,
                    business_case,
                    collateral_type=collateral_type,
                    property_collateral_types=property_collateral_types,
                )
            business_cases.append(business_case)

        if len(business_cases) > 0:
            if assign_business_case_to_dossier:
                selected_business_case = random.choice(business_cases)
                new_bekb_dossier.business_case = selected_business_case
                new_bekb_dossier.save()

        if add_semantic_documents:
            add_some_fake_semantic_documents(
                new_bekb_dossier.dossier,
                valid_document_category_keys=valid_document_category_keys,
            )
        return new_bekb_dossier

    # grundpfand == mortgage
    def create_sample_dossier_grundpfand(
        self, property_collateral_type: Optional[Attribute] = None
    ):
        if property_collateral_type is None:
            property_collateral_type = self.property_collateral_type_haupt

        return self.create_sample_dossier(
            pers=False,
            dossier_name=f"Dossier #01x (1 businesscase, 1 collateral grundpfand of pct={property_collateral_type.name_de}) {self.faker.last_name()}",
            num_business_cases=1,
            num_collaterals=1,
            collateral_type=self.attribute_collateral_grundpfand,
            property_collateral_types=[property_collateral_type],
            assign_business_case_to_dossier=True,
            add_semantic_documents=False,
        )

    def create_sample_dossier_personenversicherung(self):
        return self.create_sample_dossier(
            pers=False,
            dossier_name=f"Dossier #11 (1 businesscase, 1 matching collateral personenversicherung) {self.faker.last_name()}",
            num_business_cases=1,
            num_collaterals=1,
            collateral_type=self.attribute_collateral_personenversicherung,
            assign_business_case_to_dossier=True,
            add_semantic_documents=False,
        )

    def create_sample_dossier_two_hauptdeckung(self):
        d = self.create_sample_dossier(
            pers=False,
            dossier_name=f"Dossier #40 (1 businesscase, 1 matching collateral grundpfand with 2 real estate props assignable) {self.faker.last_name()}",
            num_business_cases=1,
            num_collaterals=0,
            collateral_type=self.attribute_collateral_grundpfand,
            assign_business_case_to_dossier=True,
            add_semantic_documents=False,
        )

        # Add 2 collaterals which both can be assigned
        self.create_collateral(
            d,
            d.business_case,
            collateral_type=self.collateral_type_grundpfand,
            property_collateral_types=[self.property_collateral_type_haupt],
        )
        self.create_collateral(
            d,
            d.business_case,
            collateral_type=self.collateral_type_grundpfand,
            property_collateral_types=[self.property_collateral_type_haupt],
        )
        return d

    def create_sample_dossier_prolongation(self):
        d = self.create_sample_dossier(
            pers=False,
            dossier_name="Dossier #S40 State Prolongation",
            num_business_cases=1,
            num_collaterals=0,
            collateral_type=self.attribute_collateral_grundpfand,
            assign_business_case_to_dossier=True,
            add_semantic_documents=False,
        )

        # Did not understand why I cannot assign this to d.dossier.businesscase_type
        # So load fresh from the db and assign
        bct = BusinessCaseType.objects.get(account=d.account, key="PROLONGATION")
        assert bct
        logger.info(f"Set bct to {bct} for {d.dossier.name}")
        dd = Dossier.objects.get(uuid=d.dossier.uuid)
        dd.businesscase_type = bct
        dd.save()

        # Add 1 collateral which be assigned
        self.create_collateral(
            d,
            d.business_case,
            collateral_type=self.collateral_type_grundpfand,
            property_collateral_types=[self.property_collateral_type_haupt],
        )

        add_some_fake_semantic_documents(
            d.dossier,
            num_docs=3,
            allow_empty_docs=False,
            valid_document_category_keys=["EXTRACT_FROM_LAND_REGISTER"],
        )

        return d

    def get_collateral_type(self, key: str):
        for t in self.collateral_types:
            if t.key == key:
                return t


def create_dossier_show_jwt(bekb_account_factory, parkey, *, is_fipla=False):
    schema_class = DossierShowJWTFipla if is_fipla else schemas.DossierShowJWTMortgage
    business_partner = bekb_account_factory.create_business_partner(parkey=parkey)
    common_data = {
        "exp": create_expiration_date(),
        "account_name": bekb_account_factory.account.key,
        "business_partner": business_partner,
        "language": schemas.Langugage.fr,
        "current_user": bekb_account_factory.create_user(),
    }

    if not is_fipla:
        common_data["fico"] = bekb_account_factory.create_fico()

    return schema_class(**common_data)


def create_dossier_create_jwt(factory, *, is_fipla=False):
    schema_class = (
        DossierCreateJWTFipla if is_fipla else schemas.DossierCreateJWTMortgage
    )
    common_data = {
        "exp": create_expiration_date(),
        "account_name": factory.account.key,
        "business_partner": factory.create_business_partner(),
        "partner_partner": factory.create_partner_partner(),
        "dossier_name": schemas.DossierName("my dossier name"),
        "language": random.choice(list(schemas.Langugage)),
        "current_user": factory.create_user(),
    }

    if not is_fipla:
        common_data["fico"] = factory.create_fico()

    return schema_class(**common_data)


def create_some_bekb_dossiers(account, number_of_dossier, use_fico=True):
    created_dossiers = create_some_dossiercreatejwt(number_of_dossier)
    bekb_dossiers = []
    for dossier_create_data in created_dossiers:
        bekb_dossiers.append(
            create_bekb_dossier(account, dossier_create_data, use_fico=use_fico)
        )

    return created_dossiers, bekb_dossiers

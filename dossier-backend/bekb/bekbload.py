import json
from typing import List

from bekb.bekb_instance_type import BekbInstanceType
from bekb.data import DATA_PATH


def load_bekb_external_document_categories() -> List[dict[str, str]]:
    """
    Load titles of the external document categories for BEKB. The source of truth for this is

    https://classifier.swarm-v2.hypodossier.ch/admin/bekb/bekbdocumentcategoryexternal/
    (exported from here)

    @return: List[Dict[str, str]]: List of external document categories, each of them has their data in a dict.
    Relevant fields are
        - "docid_external" (e.g. "EKD08")
        - "de_external" (e.g. "Genereller Pfandvertrag")
        - "fr_external" (e.g. "Contrat général de nantissement")
        - "collateral_type" (e.g. "8" or "9,8,2")
    """
    current_filename = "BEKBDocumentCategoryExternal-2025-03-10.json"
    data = json.loads(
        (DATA_PATH / "external_document_categories" / current_filename).read_text()
    )
    return data


def load_document_category2ekd_mappings(
    instance_type: BekbInstanceType,
) -> dict[str, str]:
    """
    Read mappings from mappings file.
    @return: Dict["name of document category", "EKD number"], e.g. ("ACCEPTANCE_OF_MORTGAGE_OFFER", "EKD133")
    """

    if instance_type == BekbInstanceType.MORTGAGE:
        filename = "mortgage/BEKBDocumentCategoryMapping-2025-03-14mortgage.json"
    elif instance_type == BekbInstanceType.FIPLA:
        filename = "fipla/BEKBDocumentCategoryMapping-2025-01-03fipla.json"
    else:
        raise Exception(f"Invalid instance type {instance_type}")
    d = {
        mapping["hd_docname"]: mapping["bekb_docid"]
        for mapping in json.loads(
            (DATA_PATH / "document_category_mapping" / filename).read_text()
        )
    }
    return dict(sorted(d.items()))

# Generated by Django 3.2.16 on 2022-12-05 08:41

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('dossier', '0044_auto_20221205_0941'),
        ('semantic_document', '0012_alter_semanticpage_document_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='Attribute',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('entity', models.CharField(choices=[('BusinessStatus', 'BusinessStatus'), ('BusinessType', 'BusinessType'), ('CollateralType', 'CollateralType'), ('CollateralStatus', 'CollateralStatus'), ('PropertyType', 'PropertyType'), ('PropertyCollateralType', 'PropertyCollateralType'), ('RealEstatePropertyStatus', 'RealEstatePropertyStatus')], max_length=30)),
                ('key', models.CharField(max_length=40)),
                ('name_de', models.CharField(max_length=255)),
                ('name_fr', models.CharField(max_length=255)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
        ),
        migrations.CreateModel(
            name='BEKBDossierExport',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('dossier', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='bekbexport', to='dossier.dossier')),
                ('file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='dossier.dossierfile')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BusinessCase',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_number', models.CharField(max_length=20)),
                ('mutation_date', models.DateField()),
                ('mutation_user', models.CharField(max_length=40)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
            options={
                'ordering': ['business_partner', 'business_number'],
            },
        ),
        migrations.CreateModel(
            name='Collateral',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('collateral_number', models.CharField(max_length=20)),
                ('document_parkey', models.CharField(blank=True, max_length=20, null=True)),
                ('description', models.CharField(blank=True, max_length=255, null=True)),
                ('policy_number', models.CharField(blank=True, max_length=255, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Partner',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('parkey', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('firstname', models.CharField(blank=True, max_length=255, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
            ],
        ),
        migrations.CreateModel(
            name='RealEstateProperty',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('property_number', models.CharField(max_length=255)),
                ('address_street', models.CharField(max_length=255)),
                ('address_street_nr', models.CharField(max_length=20)),
                ('address_zip', models.CharField(max_length=30)),
                ('address_city', models.CharField(max_length=255)),
                ('land_register_municipality', models.CharField(max_length=255)),
                ('land_register_id', models.CharField(max_length=20)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('property_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.partner')),
                ('property_type', models.ForeignKey(limit_choices_to={'entity': 'PropertyType'}, on_delete=django.db.models.deletion.CASCADE, related_name='property_type_real_estate_properties', to='bekb.attribute')),
                ('status', models.ForeignKey(blank=True, limit_choices_to={'entity': 'RealEstatePropertyStatus'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='status_real_estate_properties', to='bekb.attribute')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ExportFeedback',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.IntegerField(choices=[(0, 'Error'), (1, 'Success')])),
                ('message', models.TextField(blank=True, max_length=500, null=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('export', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.bekbdossierexport')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CollateralRealEstateProperty',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('collateral', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.collateral')),
                ('property_collateral_type', models.ForeignKey(limit_choices_to={'entity': 'PropertyCollateralType'}, on_delete=django.db.models.deletion.CASCADE, related_name='collateral_type_real_estate_properties', to='bekb.attribute')),
                ('realestate_property', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.realestateproperty')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CollateralAssignment',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('collateral', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.collateral')),
                ('property', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='bekb.realestateproperty')),
                ('semantic_document', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='semantic_document.semanticdocument')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='collateral',
            name='business_partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business_partner_collaterals', to='bekb.partner'),
        ),
        migrations.AddField(
            model_name='collateral',
            name='businesscase',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.businesscase'),
        ),
        migrations.AddField(
            model_name='collateral',
            name='collateral_partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collateral_partner_collaterals', to='bekb.partner'),
        ),
        migrations.AddField(
            model_name='collateral',
            name='collateral_status',
            field=models.ForeignKey(limit_choices_to={'entity': 'CollateralStatus'}, on_delete=django.db.models.deletion.CASCADE, related_name='collateral_status_collaterals', to='bekb.attribute'),
        ),
        migrations.AddField(
            model_name='collateral',
            name='collateral_type',
            field=models.ForeignKey(limit_choices_to={'entity': 'CollateralType'}, on_delete=django.db.models.deletion.CASCADE, related_name='collateral_type_collaterals', to='bekb.attribute'),
        ),
        migrations.AddField(
            model_name='businesscase',
            name='business_partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bekb.partner'),
        ),
        migrations.AddField(
            model_name='businesscase',
            name='business_status',
            field=models.ForeignKey(limit_choices_to={'entity': 'BusinessStatus'}, on_delete=django.db.models.deletion.CASCADE, related_name='business_status_businesscases', to='bekb.attribute'),
        ),
        migrations.AddField(
            model_name='businesscase',
            name='business_type',
            field=models.ForeignKey(limit_choices_to={'entity': 'BusinessType'}, on_delete=django.db.models.deletion.CASCADE, related_name='business_type_businesscases', to='bekb.attribute'),
        ),
        migrations.CreateModel(
            name='BEKBDossierProperties',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pers', models.BooleanField(default=False)),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dossier.account')),
                ('business_case', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='bekb.businesscase')),
                ('business_partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='business', to='bekb.partner')),
                ('dossier', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='dossier.dossier')),
                ('partner_partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='partner', to='bekb.partner')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddConstraint(
            model_name='partner',
            constraint=models.UniqueConstraint(fields=('account', 'parkey'), name='unique_account_parkey'),
        ),
        migrations.AddConstraint(
            model_name='businesscase',
            constraint=models.UniqueConstraint(fields=('account', 'business_partner', 'business_number'), name='unique_account_business_partner_business_number'),
        ),
        migrations.AddConstraint(
            model_name='attribute',
            constraint=models.UniqueConstraint(fields=('account', 'entity', 'key'), name='unique_account_key_entity'),
        ),
    ]

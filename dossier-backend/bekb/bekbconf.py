import structlog

from dossier.models import Account, DocumentCategory

logger = structlog.get_logger()

"""
Procedure for new document categories for bekb

1. Add new category here, also configure required collateral type(s): 
https://classifier.swarm-v2.hypodossier.ch/admin/bekb/bekbdocumentcategoryexternal/

2. (Optional and only needed if collateral types have been configured in step 1) Export all categories as 'json' here and save them to dossier-backend/bekb/data/external_document_categories
https://classifier.swarm-v2.hypodossier.ch/admin/bekb/bekbdocumentcategoryexternal/export/?q=

3. Add link between internal and external document category here and export as "json" into 
folder bekb/data/external_document_categories
https://classifier.swarm-v2.hypodossier.ch/admin/bekb/bekbdocumentcategorymapping/export/?

4. Run tests locally - some will fail because we test for an exact number of document categories for BEKB
-> adjust expected number to new correct value 
"""


def optionally_create_doccat(
    account: Account, dc: DocumentCategory, dry_run: bool, override: bool = True
):
    num_cats = DocumentCategory.objects.filter(account=account, name=dc.name).count()
    if num_cats > 1:
        raise Exception(
            f"Duplicate category with account={account.key} and cat_name={dc.name}"
        )

    found = num_cats > 0

    if found:
        if override:
            name = dc.name
            defaults = dc.__dict__
            for key in [
                "name",
                "_state",
                "uuid",
                "account_id",
                "created_at",
                "updated_at",
            ]:
                if key in defaults:
                    del defaults[key]
            DocumentCategory.objects.update_or_create(
                defaults, account=account, name=name
            )
            return True
        else:
            logger.info(
                f"Document Category {dc.name} already exists in account {account.key}"
            )
            return False
    else:
        if dry_run:
            logger.info(f"Skip creation of doccat {dc.name} because dry_run={dry_run}")
            return False
        else:
            dc.save()
            logger.info(f"Created new doccat: {dc}")
            return True


def add_bekbconf_ekd16(account_key: str, dry_run: bool):
    """
    Add new document category. If it already exists, do nothing

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    account = Account.objects.get(key=account_key)
    logger.info(f"account={account}, dry_run={dry_run}")

    title_de = "Auftrag zu Verwaltungshypotheken Dritter"
    title_fr = "Mandat d'hypothèques administratives de tiers"
    dc = DocumentCategory(
        id="700-EKD16",
        name="BEKB_EKD16",
        account=account,
        de=title_de,
        fr=title_fr,
        de_external=title_de,
        fr_external=title_fr,
    )

    optionally_create_doccat(account, dc, dry_run)


def add_bekbconf_ekd142(account_key: str, dry_run: bool, override: bool):
    """
    Add new document category. If it already exists, do nothing

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    account = Account.objects.get(key=account_key)
    logger.info(f"account={account}, dry_run={dry_run}")

    title_de = "myky-Dossier"
    title_fr = "Dossier myky"
    dc = DocumentCategory(
        id="600-EKD142",
        name="BEKB_EKD142",
        account=account,
        de=title_de,
        en=title_de,
        fr=title_fr,
        it=title_de,
        de_external=title_de,
        fr_external=title_fr,
    )

    added_142 = optionally_create_doccat(account, dc, dry_run, override)

    dc2 = DocumentCategory(
        id="511-EKD130",
        name="INCORPORATION_COMPANY",
        account=account,
        de="Gründungsunterlagen Firma",
        en="Incorporation documents of company",
        fr="Création de l'entreprise",
        it="Costituzione della società",
        de_external="Firma Diverses",
        fr_external="Entreprise divers",
    )
    added_incorporation = optionally_create_doccat(account, dc2, dry_run, override)

    logger.info(
        "add_bekbconf_ekd142",
        added_142=added_142,
        added_incorporation=added_incorporation,
    )


def add_doccats_for_new_businesscases(
    account_key: str, dry_run: bool, override: bool = True
):
    """
    Add new document category. If it already exists, do nothing

    @param account_key: Identifies account
    @param dry_run: If True, nothing is updated. Default False
    @return: Nothing. Prints out feedback
    """
    account = Account.objects.get(key=account_key)
    print(f"account={account}, dry_run={dry_run}")

    new_doc_cat_data = [
        [
            "700-EKD118",
            "BEKB_EKD118",
            "Saldierungsunterlagen",
            "Cancellation documents",
            "Documents de résiliation",
            "Documenti di bilanciamento",
            "Saldierungsunterlagen",
            "Cancellation documents",
            "Documents de résiliation",
            "Documenti di bilanciamento",
        ],
        [
            "692-EKD44",
            "RENOVATIONS",
            "Renovationen",
            "Renovations",
            "Rénovations",
            "Ristrutturazioni",
            "Renovationsarbeiten / Kostenvoranschlag",
            "Travaux de rénovation / devis",
            "Coût de rénovation / Devis",
            "Lavori di ristrutturazione / stima dei costi",
        ],
        [
            "368-EKD51",
            "PROOF_OF_INCOME",
            "Einkommensnachweise",
            "Proof of income",
            "Justificatifs de revenus",
            "Prova di reddito",
            "Einkommensnachweis",
            "Proof of income",
            "Justificatif de revenu",
            "Prova di reddito",
        ],
        [
            "690-EKD44",
            "PROPERTY_BILL",
            "Rechnung Liegenschaft",
            "Invoice property",
            "Facture immeuble",
            "Fattura di proprietà",
            "Rechnung für Kosten im Zusammenhang mit einer Liegenschaft (nicht Liegenschaftsabrechnung)",
            "",
            "",
            "",
        ],
    ]

    num_added = 0
    for d in new_doc_cat_data:
        doc_id, key, de, en, fr, it, de_ext, en_ext, fr_ext, it_ext = d
        dc = DocumentCategory(
            id=doc_id,
            name=key,
            account=account,
            de=de,
            en=en,
            fr=fr,
            it=it,
            de_external=de_ext,
            en_external=en_ext,
            fr_external=fr_ext,
            it_external=it_ext,
        )
        added = optionally_create_doccat(account, dc, dry_run, override)
        if added:
            num_added += 1

    logger.info(f"Added {num_added} document categories.")

    # Rename 368-EKD51 Bestätigung Alimente
    d_ali = DocumentCategory.objects.get(account=account, name="CONFIRMATION_ALIMONY")
    assert d_ali
    if d_ali.id == "368-EKD51":
        d_ali.id = "365-EKD51"
        logger.info(
            f"Change ID of CONFIRMATION_ALIMONY from 368-EKD51 to {d_ali.id} with dry_run={dry_run}"
        )
        if not dry_run:
            d_ali.save()

    # Rename 429-EKD54 zu Pensionskasse Diverses (alt Säule 2 Diverses)
    d_pension = DocumentCategory.objects.get(account=account, name="PILLAR_TWO_MISC")
    assert d_pension
    if d_pension.de == "Säule 2 Diverses":
        d_pension.de = "Pensionskasse Diverses"
        d_pension.en = "Pension fund Miscellaneous"
        d_pension.fr = "Caisse de pension divers"
        # Ignore EN, IT
        logger.info(
            f"Change title de, fr of PILLAR_TWO_MISC: {d_pension} with dry_run={dry_run}"
        )
        if not dry_run:
            d_pension.save()

    d_marriage = DocumentCategory.objects.get(account=account, name="MARRIAGE_CONTRACT")
    assert d_marriage
    if d_marriage.id != "275-EKD131" or override:
        d_marriage.id = "275-EKD131"
        d_marriage.de_external = "Personen Diverses"
        d_marriage.en_external = "Person Miscellaneous"
        d_marriage.fr_external = "Personnes divers"
        logger.info(
            f"Change id of MARRIAGE_CONTRACT: {d_marriage} with dry_run={dry_run}"
        )
        if not dry_run:
            d_marriage.save()

    d_application = DocumentCategory.objects.get(
        account=account, name="PENSION3_INSURANCE_APPLICATION"
    )
    assert d_application
    if d_application.id != "445-EKD132" or override:
        d_application.id = "445-EKD132"
        d_application.de_external = "Vorsorge Diverses"
        d_application.en_external = "Pension Miscellaneous"
        d_application.fr_external = "Prévoyance divers"
        logger.info(
            f"Change id of PENSION3_INSURANCE_APPLICATION: {d_application} with dry_run={dry_run}"
        )
        if not dry_run:
            d_application.save()

    d16 = DocumentCategory.objects.get(account=account, name="BEKB_EKD16")
    assert d16
    if d16.de_external != "Auftrag zu Verwaltungshypotheken Dritter" or override:
        d16.de_external = "Auftrag zu Verwaltungshypotheken Dritter"
        d16.en_external = "Auftrag zu Verwaltungshypotheken Dritter"
        d16.fr_external = "Mandat d'hypothèques administratives de tiers"
        logger.info(f"Change id of BEKB_EKD16: {d16} with dry_run={dry_run}")
        if not dry_run:
            d16.save()

    d782 = DocumentCategory.objects.get(
        account=account, name="REGISTRATION_LAND_REGISTER"
    )
    assert d782
    if d782.id == "782-EKD44":
        d782.id = "694-EKD44"
        if not dry_run:
            d782.save()

    d955 = DocumentCategory.objects.get(account=account, name="LAND_REGISTER_BILL")
    assert d955
    if d955.id == "955-EKD127":
        d955.id = "697-EKD127"
        if not dry_run:
            d955.save()

    d789 = DocumentCategory.objects.get(account=account, name="LAND_REGISTER_MISC")
    assert d789
    if d789.id == "789-EKD133":
        d789.id = "698-EKD133"
        if not dry_run:
            d789.save()

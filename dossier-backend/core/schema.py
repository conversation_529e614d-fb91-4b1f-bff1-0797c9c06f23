from typing import Generic, Optional, TypeVar
from typing_extensions import Self
from pydantic import BaseModel, RootModel, model_validator, ConfigDict


class PaginatedData(BaseModel):
    count: int
    # has_next: bool
    # has_prev: bool
    # current_page: int


class Message(BaseModel):
    detail: str


class Error(BaseModel):
    code: int
    message: str


DataT = TypeVar("DataT")


class Result(BaseModel, Generic[DataT]):
    # see also https://docs.pydantic.dev/usage/models/#generic-models
    data: Optional[DataT] = None
    error: Optional[Error] = None

    @model_validator(mode="after")
    def check_data_xor_error(self) -> Self:
        data = self.data
        error = self.error
        if error is not None and data is not None:
            raise ValueError("must not provide both data and error")
        if error is None and data is None:
            raise ValueError("must provide data or error")
        return self


class ModelNestedObjectsStatistics(BaseModel):
    model_config = ConfigDict(protected_namespaces=())
    model_type: str
    count: int


class ModelStats(RootModel):
    root: list[ModelNestedObjectsStatistics]

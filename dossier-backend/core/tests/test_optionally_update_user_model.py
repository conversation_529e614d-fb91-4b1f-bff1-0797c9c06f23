import pytest
from django.contrib.auth import get_user_model

from core.helpers import optionally_update_user_model


User = get_user_model()

pytestmark = pytest.mark.django_db


@pytest.fixture()
def get_defaults():
    return {
        "email": "<EMAIL>",
        "first_name": "service-swissfex-first",
        "last_name": "service-swissfex-last",
    }


def test_create_new_object(get_defaults):
    # Function should not create a new object
    with pytest.raises(ValueError):
        optionally_update_user_model(
            User(),
            defaults=get_defaults,
        )


def test_update_existing_object(get_defaults):
    initial_obj = User.objects.create(
        username="<EMAIL>"
    )

    obj, updated = optionally_update_user_model(
        initial_obj,
        defaults=get_defaults,
    )

    assert updated is True
    assert obj.pk == initial_obj.pk
    assert obj.email == "<EMAIL>"
    assert obj.first_name == "service-swissfex-first"
    assert obj.last_name == "service-swissfex-last"


def test_no_changes_to_existing_object(get_defaults):
    initial_obj = User.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="service-swissfex-first",
        last_name="service-swissfex-last",
    )

    obj, updated = optionally_update_user_model(
        initial_obj,
        defaults=get_defaults,
    )

    assert updated is False
    assert obj.pk == initial_obj.pk
    assert obj.email == "<EMAIL>"
    assert obj.first_name == "service-swissfex-first"
    assert obj.last_name == "service-swissfex-last"


def test_partial_update_existing_object(get_defaults):
    initial_obj = User.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="service-swissfex-first",
        last_name="old-last-name",
    )

    new_defaults = {
        "email": "<EMAIL>",
        "last_name": "service-swissfex-last",
    }

    obj, updated = optionally_update_user_model(
        initial_obj,
        defaults=new_defaults,
    )

    assert updated is True
    assert obj.pk == initial_obj.pk
    assert obj.email == "<EMAIL>"
    assert obj.first_name == "service-swissfex-first"  # Unchanged
    assert obj.last_name == "service-swissfex-last"


def test_empty_defaults():
    obj, created = optionally_update_user_model(
        User(),
    )

    assert created is False
    assert obj.first_name == ""
    assert obj.last_name == ""
    assert obj.email == ""


def test_update_with_same_values(get_defaults):
    initial_obj = User.objects.create(
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="service-swissfex-first",
        last_name="service-swissfex-last",
    )

    obj, updated = optionally_update_user_model(
        initial_obj,
        defaults=get_defaults,
    )

    assert updated is False
    assert obj.pk == initial_obj.pk
    assert obj.email == "<EMAIL>"
    assert obj.first_name == "service-swissfex-first"
    assert obj.last_name == "service-swissfex-last"

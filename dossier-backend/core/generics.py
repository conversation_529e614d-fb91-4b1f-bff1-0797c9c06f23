from typing import TypeVar, Annotated

from django.db.models import Model
from pydantic import TypeAdapter, BeforeValidator, AnyHttpUrl

T_Model = TypeVar("T_Model", bound=Model)

# Ran into an issue with 'TypeError: Object of type Url is not JSON serializable'
# In pydantic v1 urls inherited from str, so it can be easily passed to library codes,
# where they usually expects string representation of urls. Pydantic 2 broke things, and Url types don't get properly serialized
# https://github.com/pydantic/pydantic/discussions/8211
# a lot of code was broken since AnyUrl is not accepted as str any more
AnyHttpUrlAdapter = TypeAdapter(AnyHttpUrl)
AnyHttpUrlStr = Annotated[
    str,
    BeforeValidator(lambda value: AnyHttpUrlAdapter.validate_python(value) and value),
]

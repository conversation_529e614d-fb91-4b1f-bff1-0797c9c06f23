import json
import uuid
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import List, Optional

import pytest
import structlog
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from django.utils import timezone
from django.utils.http import urlencode
from freezegun import freeze_time
import jwt
from jwcrypto import jwk
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

import dossier.schemas as dossier_schemas
from bcge.conftest import api_bcge_create_dossier
from bcge.schemas.schemas import (
    AccountName,
    OriginalFileSchema,
    ExportOriginalFileSchema,
    OriginalFileSourceSchema,
)
from conftest import prepare_demo_dossier_for_account
from core.authentication import AuthenticatedClient
from dossier.fakes import (
    add_some_fake_semantic_documents,
)
from dossier.management.commands.dossier_event_consumer_v2 import (
    set_semantic_document_export_done,
)
from dossier.models import (
    Dossier,
    OriginalFile,
    DocumentCategory,
    DossierAccessGrant,
    OriginalFileSource,
    FileStatus,
)
from dossier.schemas import Language, Message
from dossier.schemas_external import DossierCloseResponse
from dossier.statemachine_types import DossierState
from dossier.tests.common_state import assert_dossier_access_mode
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from processed_file.schemas import PageObjectSchema
from semantic_document.models import (
    SemanticDocument,
)
from semantic_document import (
    schemas as semantic_document_schemas,
)
from semantic_document.services import reset_semantic_document_work_status
from statemgmt.configurations.semantic_document_state_machine import (
    STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS,
    SemanticDocumentState,
)
from statemgmt.models import StateMachine, Status
from workers.models import SemanticDocumentExport
from workers import schemas as worker_schemas
from workers.workers import process_semantic_dossier_pdf_request
from bcge.schemas import schemas
import bcge.schemas.schemas as bcge_schemas


User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_ping(bcge_authenticated_client, bcge_account, set_bcge_JWK):
    res = bcge_authenticated_client.get(reverse("bcge-api:ping"))
    assert res.status_code == 200
    assert Message.model_validate_json(res.content) == Message(detail="pong")


def mock_publish_side_effect_services_rabbit_mq_publish(*args, **kwargs):
    request = worker_schemas.SemanticDocumentPDFRequestV1.model_validate_json(
        kwargs["message"]
    )

    # Process the pdf generation
    # Returns json dump in format SemanticDocumentPDFResponseV1
    process_semantic_document_response = process_semantic_dossier_pdf_request(
        semantic_document_pdf_request=request
    )

    # which is then collected by dossier events consumer
    # and sets event as done
    set_semantic_document_export_done(process_semantic_document_response)


def test_create_dossier_api_success(
    bcge_authenticated_client, bcge_account, set_bcge_JWK
):
    # Base case to check whether we can create a dossier
    reverse("bcge-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    result = api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, expected_response_code=201
    )

    dossier = Dossier.objects.get(
        external_id=external_dossier_id, account__key=AccountName.bcged.value
    )
    parsed = schemas.Dossier.model_validate_json(result)
    assert parsed.external_dossier_id == external_dossier_id
    assert parsed.uuid == dossier.uuid

    # Test for conflict when dossier already exists
    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, expected_response_code=409
    )


def test_show_dossier(bcge_authenticated_client, bcge_account, set_bcge_JWK):
    external_dossier_id = str(uuid.uuid4())
    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, expected_response_code=201
    )

    assert (
        bcge_authenticated_client.get(
            path=reverse(
                "bcge-api:show-dossier",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        ).status_code
        == 302
    )

    assert (
        bcge_authenticated_client.get(
            path=reverse(
                "bcge-api:show-dossier",
                kwargs={"external_dossier_id": "doesnt-exist"},
            ),
        ).status_code
        == 404
    )


def test_update_dossier_api_success(
    bcge_authenticated_client, bcge_account, set_bcge_JWK
):
    """
    1. Create "Test Dossier" with lang de
    2. Update name to "Test Dossier 2" and change lang to fr
    3. Update name to "Test Dossier 3" and do not change lang
    @param bcge_authenticated_client:
    @param bcge_account:
    @param set_bcge_JWK:
    @return:
    """
    external_dossier_id = str(uuid.uuid4())

    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, expected_response_code=201
    )

    # Test changing two params
    result = bcge_authenticated_client.patch(
        path=reverse(
            "bcge-api:update-dossier",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 2",
            external_dossier_id=external_dossier_id,
            lang=Language.fr,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 2"
    assert dossier.lang == "fr"

    # Test changing one param
    result = bcge_authenticated_client.patch(
        path=reverse(
            "bcge-api:update-dossier",
            kwargs=dict(external_dossier_id=external_dossier_id),
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 3",
            external_dossier_id=external_dossier_id,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 3"
    # lang should not be changed
    assert dossier.lang == "fr"


def test_create_to_delete_dossier_api_success(
    bcge_authenticated_client, bcge_account, set_bcge_JWK
):
    """
    Test creating and deleting a dossier via API
    @param bcge_authenticated_client:
    @param bcge_account:
    @param set_bcge_JWK:
    @return:
    """
    external_dossier_id = str(uuid.uuid4())

    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, "Test Dossier"
    )

    d = Dossier.objects.get(
        external_id=external_dossier_id,
        account__key=bcge_account.key,
    )
    assert d
    assert d.name == "Test Dossier"
    assert d.external_id == external_dossier_id

    result = bcge_authenticated_client.delete(
        path=reverse(
            "bcge-api:dossier-delete",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert result.status_code == 202

    # Setting expiry date to past (in delete call) causes object to not be visible
    # as default dossier model manager has been overwritten
    assert (
        Dossier.objects.filter(
            external_id=external_dossier_id,
            account__key=bcge_account.key,
        ).count()
        == 0
    )


def test_delete_dossier_api_failure(bcge_authenticated_client, set_bcge_JWK):
    """
    Expect failure when trying to delete a dossier that does not exist
    @param bcge_authenticated_client:
    @param set_bcge_JWK:
    @return:
    """
    result = bcge_authenticated_client.delete(
        path=reverse(
            "bcge-api:dossier-delete",
            kwargs={"external_dossier_id": str(uuid.uuid4())},
        )
    )

    assert result.status_code == 404


def test_add_original_file(
    bcge_authenticated_client, bcge_account, mocker: MockerFixture, set_bcge_JWK
):
    """
    Test the behavior of the 'add-original-file' API endpoint for adding original files to a dossier.

    Specifically, it verifies:

    Successful file upload and its subsequent processing.
    Default prevention of duplicate file uploads.
    Option to allow and rename duplicate files.
    """
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    bcge_authenticated_client.post(
        path=reverse("bcge-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
        ).model_dump_json(),
        content_type="application/json",
    )

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = bcge_authenticated_client.post(
        reverse(
            "bcge-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert original_file.source == OriginalFileSource.API.value

    assert original_file.create_user == User.objects.get(
        username="<EMAIL>"
    )

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    # Check correct association with dossier
    assert original_file.dossier.external_id == external_dossier_id

    # Test checking if file exists by filename, these should be refactored into separate test
    of_exists: bool = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-original-filename",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "original_filename": original_file.file.name,
            },
        ),
    ).json()
    assert of_exists

    of2_exists: bool = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-original-filename",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "original_filename": "wrong_filename.pdf",
            },
        ),
    ).json()
    assert of2_exists is False

    # same file should not be allowed by default
    response = bcge_authenticated_client.post(
        reverse(
            "bcge-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )
    assert response.status_code == 409
    assert (
        "The file test_page1.jpg already exists in the dossier"
        in response.json()["detail"]
    )

    # Use parameter to allow duplicate files
    response = bcge_authenticated_client.post(
        reverse(
            "bcge-api:add-original-file",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
        data={"file": file, "allow_duplicate_and_rename": True},
        format="multipart",
    )

    assert response.status_code == 201
    duplicate_and_rename_original_file_response = (
        dossier_schemas.CreatedObjectReference(**response.json())
    )

    duplicate_and_rename_original_file = OriginalFile.objects.get(
        uuid=duplicate_and_rename_original_file_response.uuid
    )

    # Check that file model objects are different
    assert original_file.uuid != duplicate_and_rename_original_file.uuid

    # Check that new duplicate file has new name
    assert original_file.file.name != duplicate_and_rename_original_file.file.name

    # Check that _copy_1 has been added to the filename
    simplified_name: str = duplicate_and_rename_original_file.file.name.replace(
        "_copy_1", ""
    )
    assert original_file.file.name == simplified_name

    # Check that they belong to the same dossier
    assert original_file.dossier == duplicate_and_rename_original_file.dossier


# def test_get_file_status(
#     bcge_authenticated_client,
#     bcge_account,
#     document_categories,
#     mocker: MockerFixture,
#     set_bcge_JWK,
# ):
#     external_dossier_id = str(uuid.uuid4())
#
#     # Create a dossier
#     bcge_authenticated_client.post(
#         path=reverse("bcge-api:create-dossier"),
#         data=schemas.CreateDossier(
#             name="Test Dossier", external_dossier_id=external_dossier_id, lang='fr'
#         ).model_dump_json(),
#         content_type="application/json",
#     )
#
#     dossier = Dossier.objects.get(external_id=external_dossier_id)
#
#     file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"
#     file = SimpleUploadedFile(
#         file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
#     )
#
#     # Upload a file
#     mocker.patch("dossier.services_external.process_original_file")
#     response = bcge_authenticated_client.post(
#         reverse(
#             "bcge-api:add-original-file",
#             kwargs={"external_dossier_id": external_dossier_id},
#         ),
#         data={"file": file},
#         format="multipart",
#     )
#
#     assert response.headers["Content-Type"] == "application/json; charset=utf-8"
#
#     OriginalFile.objects.get(uuid=response.json()["uuid"])
#
#     random.seed(42)
#     add_some_fake_semantic_documents(
#         dossier=dossier, allow_empty_docs=False, num_docs=5
#     )
#
#     response = bcge_authenticated_client.get(
#         path=reverse(
#             "bcge-api:file-status",
#             kwargs={"external_dossier_id": external_dossier_id},
#         )
#     )
#
#     assert response.headers["Content-Type"] == "application/json; charset=utf-8"
#
#     parsed = schemas.DossierProcessingStatus.model_validate_json(response.content)
#     assert parsed.dossier_uuid == dossier.uuid
#     assert len(parsed.original_files) == 2
#
#     for original_file in parsed.original_files:
#         original_file_object = OriginalFile.objects.get(uuid=original_file.uuid)
#         extracted_files = original_file_object.extractedfile_set.all()
#
#         assert len(original_file.extracted_files) == len(extracted_files)
#
#         for extracted_file in extracted_files:
#             assert extracted_file.uuid in [
#                 extracted_file.uuid for extracted_file in original_file.extracted_files
#             ]
#
#     # Check that 50% of original files are processed
#     assert parsed.progress == 50


# def test_get_dossier_details_api(bcge_authenticated_client, bcge_account, set_bcge_JWK):
#     external_dossier_id = str(uuid.uuid4())
#
#     # Create a dossier
#     assert (
#         bcge_authenticated_client.post(
#             path=reverse("bcge-api:create-dossier"),
#             data=schemas.CreateDossier(
#                 name="Test Dossier",
#                 external_dossier_id=external_dossier_id,
#                 lang='fr',
#             ).model_dump_json(),
#             content_type="application/json",
#         ).status_code
#         == 201
#     )
#
#     response = bcge_authenticated_client.get(
#         path=reverse(
#             "bcge-api:dossier-details",
#             kwargs={"external_dossier_id": external_dossier_id},
#         )
#     )
#
#     parsed = schemas.Dossier.model_validate_json(response.content)
#     assert parsed.external_dossier_id == external_dossier_id
#
#     assert (
#         bcge_authenticated_client.get(
#             path=reverse(
#                 "bcge-api:dossier-details",
#                 kwargs={"external_dossier_id": str(uuid.uuid4())},
#             )
#         ).status_code
#         == 404
#     )
#
#     # Set the dossier to be expired
#     dossier = Dossier.objects.get(external_id=external_dossier_id)
#     dossier.expiry_date = timezone.now() - timedelta(days=1)
#     dossier.save()
#     response = bcge_authenticated_client.get(
#         path=reverse(
#             "bcge-api:dossier-details",
#             kwargs={"external_dossier_id": external_dossier_id},
#         )
#     )
#
#     assert response.status_code == 404


def test_get_semantic_documents_api_success(
    bcge_authenticated_client, bcge_account, document_categories, set_bcge_JWK
):
    """
    1. Create a dossier
    2. Add some random files
    3. Call the API to list the semantic documents and make sure they are the same
    @param bcge_authenticated_client:
    @param bcge_account:
    @param document_categories:
    @param set_bcge_JWK:
    @return:
    """
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    api_bcge_create_dossier(bcge_authenticated_client, external_dossier_id)

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    response, parsed = api_bcge_get_semantic_documents(
        bcge_authenticated_client, external_dossier_id, 200
    )
    assert ("null" in str(response.content)) is False

    assert len(semantic_documents) == len(parsed)

    semdoc_uuids = {doc.uuid for doc in parsed}
    for semantic_document in semantic_documents:
        assert semantic_document.uuid in semdoc_uuids

    response = bcge_authenticated_client.get(
        path=f"{reverse('bcge-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )
    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)
    semdoc_uuids = {doc.uuid for doc in parsed}
    page_uuids = {page.uuid for doc in parsed for page in doc.semantic_pages}

    for semantic_document in semantic_documents:
        assert semantic_document.uuid in semdoc_uuids
        assert semantic_document.access_mode.name == "READ_WRITE"
        for page in semantic_document.semantic_pages.all():
            assert page.uuid in page_uuids


# This test is periodically flaky and I'm not sure why. If it gets annoying disable it
def api_bcge_get_semantic_documents(
    bcge_authenticated_client,
    external_dossier_id: str,
    expected_response_code: Optional[int] = None,
    url_suffix: str = "",
):
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
        + url_suffix
    )

    if expected_response_code:
        if not response.status_code == expected_response_code:
            raise Exception(
                f"wrong response code. Expected {expected_response_code} but got {response.status_code}"
            )
    if expected_response_code is None or expected_response_code == 200:
        parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
            response.content
        )
    else:
        parsed = None
    return response, parsed


def test_get_semantic_documents_api_success_deep_last_update(
    bcge_authenticated_client, bcge_account, document_categories, set_bcge_JWK
):
    """Test that the last_update field is correctly set on the semantic documents and pages"""
    external_dossier_id = str(uuid.uuid4())

    current_time = timezone.now()

    # Step 1: 10 min ago we create a dossier with some documents

    current_minus_10 = current_time - timedelta(minutes=10)
    with freeze_time(current_minus_10):
        fixed_time = current_minus_10

        api_bcge_create_dossier(
            bcge_authenticated_client, external_dossier_id, "Test Dossier"
        )

        dossier = Dossier.objects.get(external_id=external_dossier_id)
        add_some_fake_semantic_documents(dossier=dossier, allow_empty_docs=False)

        response, parsed = api_bcge_get_semantic_documents(
            bcge_authenticated_client, external_dossier_id, 200
        )

        # difference in timestamps might be a few milliseconds negative due to rounding
        assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

        # Last change to the document was by creating it which sets the updated_at
        assert parsed[0].last_change == parsed[0].updated_at

    # Step 2: 5 min ago: soft delete a page which will change last_change but not updated_at

    current_minus_5 = current_time - timedelta(minutes=5)
    with freeze_time(current_minus_5):
        fixed_time = current_minus_5

        semantic_document = SemanticDocument.objects.get(uuid=parsed[0].uuid)

        # Soft delete a page
        first_page = semantic_document.semantic_pages.first()
        first_page.deleted_at = current_minus_5
        first_page.save()

        response, parsed = api_bcge_get_semantic_documents(
            bcge_authenticated_client, external_dossier_id, 200
        )

        # The updated_at did not change since creation of the document but the last_change
        # changed due to the soft delete of a page

        # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
        # assert (parsed[0].last_change - parsed[0].updated_at) > timedelta(minutes=4)

        # Soft delete happened in less than 2 seconds
        # difference in timestamps might be a few milliseconds negative due to rounding
        assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

        # Last change happened around 5 minutes before 'now'
        # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
        # assert (current_time - parsed[0].last_change) > timedelta(minutes=4)


def test_update_semantic_documents_api_success(
    bcge_authenticated_client, bcge_account, document_categories, set_bcge_JWK
):
    external_dossier_id = str(uuid.uuid4())

    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, "Test Dossier"
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    semantic_document = semantic_documents[0]

    assert semantic_document.access_mode.name == "READ_WRITE"

    assert semantic_document.external_semantic_document_id is None

    response = bcge_authenticated_client.patch(
        path=reverse(
            "bcge-api:update-semantic-document",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document.uuid,
            },
        ),
        data=schemas.SemanticDocumentUpdate(
            external_semantic_document_id="Test external document",
            access_mode="read_only",
        ).model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200

    parsed = schemas.SemanticDocument.model_validate_json(response.content)

    assert parsed.access_mode == "read_only"
    assert parsed.external_semantic_document_id == "Test external document"


def test_get_semantic_documents_api_failure(
    bcge_authenticated_client, bcge_account, document_categories, set_bcge_JWK
):
    # Test the case where the dossier does not exist
    non_existant_external_uuid = str(uuid.uuid4())
    response, parsed = api_bcge_get_semantic_documents(
        bcge_authenticated_client, non_existant_external_uuid, 404
    )
    assert "does not exist" in str(response.content)
    assert parsed is None

    external_dossier_id = str(uuid.uuid4())

    # Create a dossier, but don't add any semantic documents
    api_bcge_create_dossier(
        bcge_authenticated_client, external_dossier_id, "Test Dossier"
    )

    # Validate it has no documents
    response = bcge_authenticated_client.get(
        path=f"{reverse('bcge-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )
    assert response.status_code == 200
    assert response.json() == []


def test_get_document_categories_api_success(
    bcge_authenticated_client, bcge_account, document_categories, set_bcge_JWK
):
    """
    Fetch document categories via API and validate they are correct
    """
    response = bcge_authenticated_client.get(
        path=reverse("bcge-api:document-categories"),
    )

    assert response.status_code == 200

    # Check we can parse the response
    schemas.DocumentCategories.model_validate_json(response.content)

    for document_category in document_categories:
        assert document_category.account.key == AccountName.bcged.value
        assert document_category.name in response.json().keys()
        if document_category.name == "PASSPORT_CH":
            # used to be "PASSPORT_BCGE_CUSTOM" with test mapping
            assert document_category.id_external == "210"
        elif document_category.name == "RESIDENCE_PERMIT_G":
            assert document_category.id_external == "231G"


def test_get_document_categories_api_empty(
    bcge_authenticated_client, bcge_account, set_bcge_JWK
):
    """
    Test the case where there are no document categories
    @param bcge_authenticated_client:
    @param bcge_account:
    @param set_bcge_JWK:
    @return:
    """
    DocumentCategory.objects.all().delete()
    response = bcge_authenticated_client.get(
        path=reverse("bcge-api:document-categories"),
    )
    assert response.status_code == 200
    assert response.json() == {}


@pytest.mark.parametrize(
    "api_url_name",
    [
        "dossier-set-semantic-documents-state-ready-for-export",
        "semantic-document-set-state-ready-for-export",
    ],
)
def test_set_dossier_ready_for_export(
    prepare_data_export,
    bcge_authenticated_client,
    api_url_name,
):
    """
    Perform 2 tests:
    1. Set all semantic documents in dossier as ready for export
    2. Set a single semantic document in dossier as ready for export
    @param prepare_data_export:
    @param bcge_authenticated_client:
    @param api_url_name:
    @return:
    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    # Request to generate an export
    # Test two different APIs, one that generates just for the semantic document
    # and one that generates for the whole dossier
    if api_url_name == "dossier-set-semantic-documents-state-ready-for-export":
        response = bcge_authenticated_client.post(
            path=reverse(
                "bcge-api:dossier-set-semantic-documents-state-ready-for-export",
                kwargs={"external_dossier_id": external_dossier_id},
            ),
        )
    else:
        response = bcge_authenticated_client.post(
            path=reverse(
                "bcge-api:semantic-document-set-state-ready-for-export",
                kwargs={
                    "semantic_document_uuid": str(semantic_document.uuid),
                    "external_dossier_id": external_dossier_id,
                },
            ),
        )

    assert response.status_code == 200

    mock_dispatch_publish_request.assert_called()

    assert response.status_code == 200

    list(SemanticDocumentExport.objects.all())

    if api_url_name == "dossier-set-semantic-documents-state-ready-for-export":
        expected_semdoc_export_uuid = response.json()[0]
    else:
        expected_semdoc_export_uuid = response.json()

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=expected_semdoc_export_uuid
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Check we can poll for dossier status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:dossier-semantic-document-export-status",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)

    export = parse[0]

    assert export.semantic_document_export_request_uuid == semantic_document_export.uuid

    assert (
        export.semantic_document_uuid == semantic_document_export.semantic_document.uuid
    )
    assert export.semantic_document_url
    assert (
        export.document_category_id_external == "210"
    )  # used to be this in test mapping "PASSPORT_BCGE_CUSTOM"
    assert export.document_category_key == "PASSPORT_CH"

    # Test we can also get the same via all-semantic-document-export-status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    # Test resting the semantic document to IN_FRONT_OFFICE
    # e.g. if export fails and we need to trigger a reset
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Remove external_dossier_id and try export again -> should return nothing
    backup_external_id = semantic_document.dossier.external_id
    semantic_document.refresh_from_db()
    semantic_document.dossier.external_id = None
    semantic_document.dossier.save()
    semantic_document.refresh_from_db()
    assert semantic_document.dossier.external_id is None
    response_without_external_id = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:all-semantic-document-export-status",
        ),
    )
    parse_without_external_id: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response_without_external_id.content)
    assert len(parse_without_external_id) == 0
    # Now set the external ID again for the next test
    semantic_document.dossier.external_id = backup_external_id
    semantic_document.dossier.save()
    semantic_document.refresh_from_db()
    assert semantic_document.dossier.external_id is not None

    reset_semantic_document_work_status(semantic_document)
    assert (
        semantic_document.work_status.key
        == semantic_document.dossier.account.active_semantic_document_work_status_state_machine.start_status.key
    )
    response_after_reset = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:all-semantic-document-export-status",
        ),
    )
    parse_after_reset: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response_after_reset.content)
    assert len(parse_after_reset) == 0


def test_export_dossier_semantic_document_pdf_auth_failure(
    prepare_data_export,
    bcge_miss_signed_authenticated_client,
):
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )
    # Request to generate an export
    response = bcge_miss_signed_authenticated_client.post(
        path=reverse(
            "bcge-api:semantic-document-set-state-ready-for-export",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 401
    mock_dispatch_publish_request.assert_not_called()


def setup_test_dossier_with_document(
    bcge_authenticated_client, bcge_account, mocker: MockerFixture
) -> tuple[str, OriginalFile, Dossier, list[SemanticDocument]]:
    """
    Sets up a test dossier with a single document for testing.

    Args:
        bcge_authenticated_client: The authenticated test client
        bcge_account: The BCGE account instance
        mocker: PyTest mocker fixture

    Returns:
        Tuple containing:
        - external_dossier_id (str)
        - original_file (OriginalFile)
        - dossier (Dossier)
        - semantic_documents (list[SemanticDocument])
    """
    external_dossier_id = str(uuid.uuid4())

    state_machine = StateMachine.objects.get(
        name=STATE_MACHINE_SEMANTIC_DOCUMENT_WORK_STATUS
    )

    # Check that we have a semantic document state machine loaded
    assert (
        bcge_account.active_semantic_document_work_status_state_machine == state_machine
    )

    # Check that we have a dossier state machine loaded
    assert bcge_account.active_work_status_state_machine

    # Create a dossier
    api_bcge_create_dossier(bcge_authenticated_client, external_dossier_id)

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"
    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = bcge_authenticated_client.post(
        reverse(
            "bcge-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    assert original_file.source == OriginalFileSource.API.value

    assert original_file.create_user == User.objects.get(
        username="<EMAIL>"
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier, num_docs=1)

    assert len(semantic_documents) == 1
    # Pick a first one
    semantic_document = semantic_documents[0]

    # Work status should have already been set by create semantic document function
    assert semantic_document.work_status == Status.objects.get(
        key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        state_machine=dossier.account.active_semantic_document_work_status_state_machine,
    )

    dossier.refresh_from_db()

    return external_dossier_id, original_file, dossier, semantic_documents


def test_dossier_end_to_end(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    semantic_document = semantic_documents[0]

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=response.json()[0]
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    # Check we can poll for dossier status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:dossier-semantic-document-export-status",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    # Test we can also get the same via all-semantic-document-export-status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 1  # One export waiting (one doc)
    assert (
        parse[0].semantic_document_export_request_uuid == semantic_document_export.uuid
    )

    # Check we can set the export as done (success case)
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:dossier-semantic-document-export-set-done",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
    )

    affected_export_uuids = response.json()
    assert affected_export_uuids[0] == str(semantic_document_export.uuid)
    assert len(affected_export_uuids) == 1

    # Check we can poll again for dossier status, to check that it is removed from export status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:dossier-semantic-document-export-status",
            kwargs={"external_dossier_id": str(external_dossier_id)},
        ),
    )

    assert response.status_code == 200

    # Should not be in the list anymore
    assert response.json() == []

    # Test we can also get the same via all-semantic-document-export-status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:all-semantic-document-export-status",
        ),
    )

    assert response.status_code == 200

    parse: List[schemas.ExportStatusSemanticDocument] = TypeAdapter(
        List[schemas.ExportStatusSemanticDocument]
    ).validate_json(response.content)
    assert len(parse) == 0


def test_update_export_status_error_with_message(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    # Test the process of setting an error status with an error message
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    semantic_document = semantic_documents[0]

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=response.json()[0]
    )

    # Set error status with error message
    error_message = "Test error message Test error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error messageTest error message"
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:dossier-semantic-document-export-set-error",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "semantic_document_uuid": str(semantic_document.uuid),
            },
        ),
        data=json.dumps({"error_message": error_message}),
        content_type="application/json",
    )

    assert response.status_code == 200
    affected_export_uuids = response.json()
    assert affected_export_uuids[0] == str(semantic_document_export.uuid)
    assert len(affected_export_uuids) == 1

    # Verify the error message was set
    semantic_document_export.refresh_from_db()
    assert semantic_document_export.error_message == error_message

    # Verify the semantic document status is now ERROR
    semantic_document.refresh_from_db()
    assert semantic_document.work_status.key == SemanticDocumentState.EXPORT_ERROR.value


@pytest.mark.parametrize(
    ("token_overwrite", "expected"),
    [
        ({}, 201),  # Base case
        ({"user_roles": ""}, 401),  # No user roles
        ({"account_key": "wrong"}, 401),  # Wrong account key
        ({"exp": 1}, 401),  # Expired token
        ({"aud": ""}, 401),  # Aud not set
    ],
)
def test_create_dossier_api_authentication(
    token_overwrite,
    expected,
    bcge_account,
    mock_jwks_public_private,
    token_data,
    set_bcge_JWK,
):
    # Test various cases for JWT authentication
    url = reverse("bcge-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    dossier_data = schemas.CreateDossier(
        name="Test Dossier", external_dossier_id=external_dossier_id, lang="fr"
    ).model_dump_json()

    # Use the token_overwrite to overwrite certain fields in the token to ensure JWT authentication is working
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    authenticated_client = AuthenticatedClient(
        jwt.encode(
            {
                "aud": "account",
                "user_roles": [settings.API_ROLE],
                **token_data,
                **token_overwrite,
            },
            key=pem,
            algorithm="RS256",
        )
    )

    result = authenticated_client.post(
        path=url,
        data=dossier_data,
        content_type="application/json",
    )

    assert result.status_code == expected


def test_soft_delete_semantic_document(
    set_bcge_JWK,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
):
    # Test deleting and recovering a semantic document
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=bcge_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    # Get Semantic documents
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
        + "?show_pages=true"
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid
    assert parsed[0].work_status_key == "IN_FRONT_OFFICE"

    response = bcge_authenticated_client.delete(
        path=f"{reverse('bcge-api:semantic-document-soft-delete',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "deleted"
    assert response.status_code == 200

    # Note trick is here to get the object from the deleted objects manager (all objects)
    # otherwise we will not see soft deleted objects
    soft_deleted_semantic_document = SemanticDocument.all_objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert soft_deleted_semantic_document.deleted_at is not None

    # Check we can't fetch it
    response, parsed = api_bcge_get_semantic_documents(
        bcge_authenticated_client, external_dossier_id
    )
    assert len(parsed) == 0

    # Check we can recover it
    response = bcge_authenticated_client.put(
        path=f"{reverse('bcge-api:semantic-document-restore',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "restored"
    assert response.status_code == 200

    restored_semantic_document = SemanticDocument.objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert restored_semantic_document.deleted_at is None

    # Check we can fetch it
    _, parsed2 = api_bcge_get_semantic_documents(
        bcge_authenticated_client, external_dossier_id, url_suffix="?show_pages=true"
    )

    assert len(parsed2) == 1
    assert len(parsed2[0].semantic_pages) == 5
    assert parsed2[0].uuid == semantic_documents[0].uuid


def test_set_dossier_user_grant(set_bcge_JWK, bcge_authenticated_client, bcge_account):
    new_date = timezone.now() + timezone.timedelta(days=1)

    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        bcge_authenticated_client.post(
            path=reverse("bcge-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    data = schemas.DossierAccessGrant(
        name="Test Dossier",
        external_dossier_id=external_dossier_id,
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        expires_at=new_date,
        has_access=True,
    ).model_dump_json()

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data,
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access <NAME_EMAIL>" in response.json()["detail"]

    assert DossierAccessGrant.objects.get(
        user__username="<EMAIL>", dossier=dossier
    )


def test_set_dossier_user_grant_revoke(
    set_bcge_JWK, bcge_authenticated_client, bcge_account
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        bcge_authenticated_client.post(
            path=reverse("bcge-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    data = schemas.DossierAccessGrant(
        name="Test Dossier",
        external_dossier_id=external_dossier_id,
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
    )

    # Grant access
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access <NAME_EMAIL>" in response.json()["detail"]

    assert DossierAccessGrant.objects.get(
        user__username="<EMAIL>", dossier=dossier
    )

    # Expire access
    expired_date = timezone.now() - timezone.timedelta(days=1)
    data.expires_at = expired_date

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access expired" in response.json()["detail"]

    assert (
        DossierAccessGrant.objects.get(
            user__username="<EMAIL>", dossier=dossier
        ).expires_at
        == expired_date
    )

    # Revoke access
    data.has_access = False

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access revoked" in response.json()["detail"]

    assert (
        DossierAccessGrant.objects.get(
            user__username="<EMAIL>", dossier=dossier
        ).expires_at
        == expired_date
    )


def test_set_dossier_user_grant_revoke_case_insensitive(
    set_bcge_JWK, bcge_authenticated_client, bcge_account
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        bcge_authenticated_client.post(
            path=reverse("bcge-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    data = schemas.DossierAccessGrant(
        name="Test Dossier",
        external_dossier_id=external_dossier_id,
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
    )

    # Grant access
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access <NAME_EMAIL>" in response.json()["detail"]

    assert DossierAccessGrant.objects.get(
        user__username="<EMAIL>", dossier=dossier
    )

    # Expire access
    expired_date = timezone.now() - timezone.timedelta(days=1)
    data.expires_at = expired_date

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access expired" in response.json()["detail"]

    assert (
        DossierAccessGrant.objects.get(
            user__username="<EMAIL>", dossier=dossier
        ).expires_at
        == expired_date
    )

    # Revoke access
    data.has_access = False

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access revoked" in response.json()["detail"]

    assert (
        DossierAccessGrant.objects.get(
            user__username="<EMAIL>", dossier=dossier
        ).expires_at
        == expired_date
    )


def test_set_dossier_user_grant_dossier_not_found(
    set_bcge_JWK, bcge_authenticated_client, bcge_account
):

    new_date = timezone.now() + timezone.timedelta(days=1)

    external_dossier_id = str(uuid.uuid4())

    data = schemas.DossierAccessGrant(
        name="Test Dossier",
        external_dossier_id=external_dossier_id,
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        expires_at=new_date,
        has_access=True,
    ).model_dump_json()

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data,
        content_type="application/json",
    )

    assert response.status_code == 404


def test_set_dossier_user_grant_update_existing(
    set_bcge_JWK, bcge_authenticated_client, bcge_account
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        bcge_authenticated_client.post(
            path=reverse("bcge-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    data = schemas.DossierAccessGrant(
        name="Test Dossier",
        external_dossier_id=external_dossier_id,
        username="<EMAIL>",
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
        expires_at=timezone.now() + timezone.timedelta(days=1),
        has_access=True,
    )

    # Grant access
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access <NAME_EMAIL>" in response.json()["detail"]

    assert DossierAccessGrant.objects.get(
        user__username="<EMAIL>", dossier=dossier
    )

    # Update access
    new_date = timezone.now() + timezone.timedelta(days=2)
    data.expires_at = new_date

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:set-dossier-user-grant",
        ),
        data=data.model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200
    assert "Access <NAME_EMAIL>" in response.json()["detail"]

    assert (
        DossierAccessGrant.objects.get(
            user__username="<EMAIL>", dossier=dossier
        ).expires_at
        == new_date
    )


@pytest.fixture
def create_original_file(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        bcge_authenticated_client.post(
            path=reverse("bcge-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                lang="fr",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    mocker.patch("dossier.services_external.process_original_file")
    response = bcge_authenticated_client.post(
        reverse(
            "bcge-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    return original_file


def test_get_export_original_files(
    bcge_authenticated_client,
    create_original_file,
):
    # Test without filters
    response = bcge_authenticated_client.get(
        reverse(
            "bcge-api:get-original-files",
        ),
    )

    assert response.status_code == 200

    original_files: list[OriginalFileSchema] = TypeAdapter(
        List[OriginalFileSchema]
    ).validate_json(response.content)

    assert len(original_files) == 1

    dossier = create_original_file.dossier

    response = bcge_authenticated_client.get(
        reverse(
            "bcge-api:get-dossier-original-files",
            kwargs={"external_dossier_id": dossier.external_id},
        ),
    )

    assert response.status_code == 200

    original_files: list[OriginalFileSchema] = TypeAdapter(
        List[OriginalFileSchema]
    ).validate_json(response.content)

    assert len(original_files) == 1

    response = bcge_authenticated_client.get(
        reverse(
            "bcge-api:export-original-file",
            kwargs={"original_file_uuid": original_files[0].original_file_uuid},
        ),
    )

    original_file = ExportOriginalFileSchema.model_validate_json(response.content)

    assert original_file
    assert original_file.file_url


def test_get_original_files_after_timestamp(
    bcge_authenticated_client,
    create_original_file,
):
    # Test filtering by created_after_timestamp
    time_stamp = timezone.now() - timezone.timedelta(days=3)
    timestamp_iso = time_stamp.isoformat()

    base_url = reverse("bcge-api:get-original-files")

    # Construct query parameters
    query_params = urlencode({"created_after_timestamp": timestamp_iso})

    # Combine base URL and query parameters
    url = f"{base_url}?{query_params}"

    response = bcge_authenticated_client.get(path=url)

    original_files: list[OriginalFileSchema] = TypeAdapter(
        List[OriginalFileSchema]
    ).validate_json(response.content)

    assert len(original_files) == 1
    assert original_files[0].created_at > time_stamp


def test_get_original_files_with_source(
    bcge_authenticated_client,
    create_original_file,
):
    # Test filtering by source
    base_url = reverse("bcge-api:get-original-files")

    # Construct query parameters
    query_params = urlencode({"source": OriginalFileSourceSchema.API_SOURCE.value})

    # Combine base URL and query parameters
    url = f"{base_url}?{query_params}"

    response = bcge_authenticated_client.get(path=url)

    assert response.status_code == 200

    original_files: list[OriginalFileSchema] = TypeAdapter(
        List[OriginalFileSchema]
    ).validate_json(response.content)

    assert len(original_files) == 1


def test_get_original_files_with_no_matches(
    bcge_authenticated_client,
    create_original_file,
):
    time_stamp = timezone.now() + timezone.timedelta(days=1)
    timestamp_iso = time_stamp.isoformat()

    base_url = reverse("bcge-api:get-original-files")

    # Construct query parameters
    query_params = urlencode(
        {
            "created_after_timestamp": timestamp_iso,
            "source": OriginalFileSourceSchema.DMF_SOURCE.value,
        }
    )

    # Combine base URL and query parameters
    url = f"{base_url}?{query_params}"

    response = bcge_authenticated_client.get(path=url)

    original_files: list[OriginalFileSchema] = TypeAdapter(
        List[OriginalFileSchema]
    ).validate_json(response.content)

    assert len(original_files) == 0


def test_semantic_document_unique_page_objects_sample_dossier(
    bcge_authenticated_client, bcge_account, set_bcge_JWK
):
    """
    @param bcge_authenticated_client:
    @param bcge_account:
    @param set_bcge_JWK:
    @return:
    """

    dossier = prepare_demo_dossier_for_account(
        account=bcge_account, external_id="254e93ec-c0f2-4133-be04-24170c607777"
    )

    # Check a single document of type Pension Certificate
    semdoc_pension_cert = dossier.semantic_documents.get(
        document_category__name="PENSION_CERTIFICATE",
        title_suffix="Thiemann Manuel AXA Zusatz GL 2017-03-01",
    )

    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:semantic-document-unique-page-objects",
            kwargs={"semantic_document_uuid": str(semdoc_pension_cert.uuid)},
        )
    )
    assert response.status_code == 200

    parsed_for_one_semdoc = TypeAdapter(List[PageObjectSchema]).validate_json(
        response.content
    )
    logger.info(
        "page_objects for single semantic document",
        parsed_for_one_semdoc=parsed_for_one_semdoc,
    )

    compact_result = []
    for po in parsed_for_one_semdoc:
        compact_result.append(
            (po.key, po.titles.en, po.value, po.semantic_document_titles.fr)
        )

    expected_result = [
        (
            "company",
            "Company",
            "AXA",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "document_date",
            "Date",
            "01.03.2017",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "product",
            "Product",
            "Zusatz GL",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "pc_employer",
            "Employer",
            "HypoPlus AG",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "fullname",
            "Name",
            "Thiemann Manuel",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "date_of_birth",
            "Date of Birth",
            "04.09.1977",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "applicable_annual_salary_declared",
            "Applicable annual Salary (declared)",
            "CHF 189'300",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "ahv_new",
            "New AHV No",
            "756.4078.9585.31",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "projected_assets_retirement",
            "Assets on retirement",
            "CHF 901'974",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "projected_pension_retirement",
            "Benefits on retirement",
            "CHF 52'008",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "withdrawal_benefit",
            "Withdrawal benefit (vesting Art. 15)",
            "CHF 15'229",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
    ]

    logger.info("compact result", compact_result=compact_result)
    assert sorted(expected_result, key=lambda x: x[0]) == sorted(
        compact_result, key=lambda x: x[0]
    )


def test_dossier_close_workflow(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    # Set it to status processed
    original_file.status = FileStatus.PROCESSED
    original_file.save()

    # Expect initial state open
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.OPEN.value
    )

    # Dossier is Open, expect read-write access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_WRITE)

    assert (
        bcge_schemas.DossierStatusResponse.model_validate_json(
            bcge_authenticated_client.get(
                path=reverse(
                    "bcge-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.OPEN.value
    )

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Check whether a dossier can be closed, should return false as semantic documents have not been
    # exported yet
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: bcge_schemas.DossierCloseReadyResponse = (
        bcge_schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == bcge_schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=0,
        num_documents_export_not_started=1,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=False,
        msg_nok_de="Es gibt noch Dokumente (1), die nicht aus HypoDossier übertragen wurden. Gehen Sie zu HypoDossier und benennen Sie jedes Dokument im Abschnitt 'Zuweisung / Dokumententrennung offen'. Anschliessend Dokumente übertragen.",
        msg_nok_en="There are still documents (1) that are not transferred from HypoDossier. Go to HypoDossier and rename each of the documents in section 'Assignment / Document separation open'. Afterwards transfer documents.",
        msg_nok_fr="Il reste encore des documents (1) qui n'ont pas été transférés depuis HypoDossier. Accédez à HypoDossier et renommez chaque document dans la section 'Affectation / Séparation des documents ouverte'. Ensuite, transférez les documents.",
        msg_nok_it="Ci sono ancora documenti (1) che non sono stati trasferiti da HypoDossier. Accedi a HypoDossier e rinomina ciascun documento nella sezione 'Assegnazione / Separazione documenti aperta'. Successivamente trasferisci i documenti.",
    )

    # Try to close the dossier - should fail

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 400

    res_close_dossier = Message.model_validate_json(response.content)

    assert res_close_dossier == Message(
        detail="Transition not allowed from Default Dossier State Machine/OPEN to Default Dossier State Machine/CLOSED or conditions not fulfilled. Only the following next states are allowed: ['CLOSING']."
    )

    # Dossier is still Open,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.OPEN.value
    )
    # expect read-write access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_WRITE)

    # Set the dossier to closing and dispatch export of semantic documents
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = (
        bcge_schemas.PrepareCloseDossierResponse.model_validate_json(response.content)
    )

    assert res_prepare_close_dossier == bcge_schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=1,
        detail="Dossier moved to Closing state",
        close_result=None,
    )

    assert (
        bcge_schemas.DossierStatusResponse.model_validate_json(
            bcge_authenticated_client.get(
                path=reverse(
                    "bcge-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSING.value
    )

    # Dossier is Closing,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.CLOSING.value
    )
    # expect read only access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_ONLY)

    # Check whether a dossier can be closed, should return True
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: bcge_schemas.DossierCloseReadyResponse = (
        bcge_schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == bcge_schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=True,
        msg_nok_de=None,
        msg_nok_en=None,
        msg_nok_fr=None,
        msg_nok_it=None,
    )

    # Finally close the dossier
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_close_dossier = bcge_schemas.DossierCloseResponse.model_validate_json(
        response.content
    )

    assert res_close_dossier == bcge_schemas.DossierCloseResponse(
        success=True, msg_nok_de=None, msg_nok_en=None, msg_nok_fr=None, msg_nok_it=None
    )

    assert (
        bcge_schemas.DossierStatusResponse.model_validate_json(
            bcge_authenticated_client.get(
                path=reverse(
                    "bcge-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSED.value
    )

    # Dossier is Closed,
    assert (
        Dossier.objects.get(external_id=external_dossier_id).work_status.key
        == DossierState.CLOSED.value
    )
    # expect read only access
    assert_dossier_access_mode(dossier, Dossier.DossierAccessMode.READ_ONLY)


def test_dossier_cant_close_original_file_processing(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    # Test the case where we have an original file in processing state
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = (
        bcge_schemas.PrepareCloseDossierResponse.model_validate_json(response.content)
    )

    assert res_prepare_close_dossier == bcge_schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=1,
        detail="Dossier moved to Closing state",
        close_result=None,
    )

    # Check whether a dossier can be closed, should return false as semantic documents have not been
    # exported yet
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: bcge_schemas.DossierCloseReadyResponse = (
        bcge_schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == bcge_schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=1,
        ready_for_close=False,
        msg_nok_de="Es gibt noch 1 Originaldateien in Verarbeitung. Bitte warten Sie, bis die Verarbeitung abgeschlossen ist.",
        msg_nok_en="There are still 1 original files being processed. Please wait until processing is complete.",
        msg_nok_fr="Il y a encore 1 fichiers originaux en cours de traitement. Veuillez attendre que le traitement soit terminé.",
        msg_nok_it="Ci sono ancora 1 file originali in elaborazione. Attendere il completamento dell'elaborazione.",
    )

    # Try to close the dossier - should fail
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 400

    res_close_dossier = Message.model_validate_json(response.content)

    # Show anyway is disabled for all transitions. Thereofore no valid target state for the transition
    assert res_close_dossier == Message(
        detail="Transition not allowed from Default Dossier State Machine/CLOSING to Default Dossier State Machine/CLOSED or conditions not fulfilled. Only the following next states are allowed: []."
    )


def test_dossier_early_close_workflow(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    # Test the case where we can close at prepare-close-dossier stage, as all semantic documents are exported

    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    # Set it to status processed
    original_file.status = FileStatus.PROCESSED
    original_file.save()

    mock_dispatch_publish_request = mocker.patch(
        "semantic_document.services.rabbit_mq_publish",
        side_effect=mock_publish_side_effect_services_rabbit_mq_publish,
    )

    # Request to generate an export
    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:dossier-set-semantic-documents-state-ready-for-export",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    # Check whether a dossier can be closed, should return True
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:check-dossier-close-ready",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_check_dossier_close_ready: bcge_schemas.DossierCloseReadyResponse = (
        bcge_schemas.DossierCloseReadyResponse.model_validate_json(response.content)
    )

    assert res_check_dossier_close_ready == bcge_schemas.DossierCloseReadyResponse(
        num_documents_all=1,
        num_documents_exported=1,
        num_documents_export_not_started=0,
        num_documents_in_export=0,
        num_documents_unknown=0,
        num_original_files_in_processing=0,
        ready_for_close=True,
        msg_nok_de=None,
        msg_nok_en=None,
        msg_nok_fr=None,
        msg_nok_it=None,
    )

    response = bcge_authenticated_client.post(
        path=reverse(
            "bcge-api:prepare-close-dossier",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    res_prepare_close_dossier = (
        bcge_schemas.PrepareCloseDossierResponse.model_validate_json(response.content)
    )

    assert res_prepare_close_dossier == bcge_schemas.PrepareCloseDossierResponse(
        success=True,
        exports_triggered_count=0,
        detail="Dossier moved to Closed state",
        close_result=DossierCloseResponse(
            success=True,
            msg_nok_de=None,
            msg_nok_en=None,
            msg_nok_fr=None,
            msg_nok_it=None,
        ),
    )

    assert (
        bcge_schemas.DossierStatusResponse.model_validate_json(
            bcge_authenticated_client.get(
                path=reverse(
                    "bcge-api:dossier-status",
                    kwargs={
                        "external_dossier_id": external_dossier_id,
                    },
                ),
            ).content
        ).status_key
        == DossierState.CLOSED.value
    )


def test_get_dossier_status_success(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    """Test successful retrieval of dossier status"""
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    # Set dossier to CLOSING state
    closing_state = Status.objects.get(
        key=DossierState.CLOSING.value,
        state_machine=dossier.account.active_work_status_state_machine,
    )
    dossier.work_status = closing_state
    dossier.save()

    # Request dossier status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:dossier-status",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    status_response = bcge_schemas.DossierStatusResponse.model_validate_json(
        response.content
    )
    assert status_response.status_key == DossierState.CLOSING.value


def test_get_dossier_status_no_status(
    mocked_get_dossier,
    bcge_authenticated_client,
    bcge_account,
    document_categories,
    set_bcge_JWK,
    mocker: MockerFixture,
):
    """Test response when dossier has no status set"""
    # Setup test dossier without setting status
    external_dossier_id, original_file, dossier, semantic_documents = (
        setup_test_dossier_with_document(
            bcge_authenticated_client, bcge_account, mocker
        )
    )

    # Clear work status
    dossier.work_status = None
    dossier.save()

    # Request dossier status
    response = bcge_authenticated_client.get(
        path=reverse(
            "bcge-api:dossier-status",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 404
    error_response = Message.model_validate_json(response.content)
    assert error_response.detail == "No status set for this dossier"

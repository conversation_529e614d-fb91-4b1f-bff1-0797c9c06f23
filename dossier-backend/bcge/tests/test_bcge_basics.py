from faker import Faker

from bcge.factories import BCGEAccountFactoryFaker
from bcge.schemas.schemas import Account<PERSON>ame
from dossier.models import DocumentCategory


def test_bcge_account_setup(db):
    account_key = AccountName.bcged.value
    Faker.seed(234777)
    bfac = BCGEAccountFactoryFaker(
        account_key=account_key,
    )
    assert bfac.account.enable_semantic_document_export is True


def test_bcge_document_categories(db):
    account_key = AccountName.bcged.value
    Faker.seed(234777)
    bfac = BCGEAccountFactoryFaker(
        account_key=account_key,
    )
    doccats = bfac.load_initial_document_categories()
    bfac.account.save()

    # 262 + 3 custom titre de sejour + CORRESPONDENCE_NOTARY + LETTER_COMMITMENT_NOTARY
    assert len(doccats) == 267

    # d1 = DocumentCategory.objects.get(name="RESIDENCE_PERMIT", account=bfac.account)
    # assert d1.fr == "Titre séjour UE/AELE"

    d2 = DocumentCategory.objects.get(name="AUTHORIZATION_EMAIL", account=bfac.account)
    assert d2.fr == "DTTT"

    d3 = DocumentCategory.objects.get(name="PROOF_OF_INCOME", account=bfac.account)
    assert d3.exclude_for_recommendation is True

    d_passport = DocumentCategory.objects.get(name="PASSPORT_CH", account=bfac.account)
    assert (
        d_passport.id_external == "210"  # "798"
    )  # used to be test mapping: "PASSPORT_BCGE_CUSTOM"

    d_passport = DocumentCategory.objects.get(
        name="RESIDENCE_PERMIT_B", account=bfac.account
    )
    assert (
        d_passport.id_external == "231B"  # "801"
    )  # used to be this in test mapping "231Bexternal"

    # All doc cats need to be mapped
    for doccat in doccats:
        assert (
            doccat.id_external is not None
        ), f"No mapping for external_id for {doccat}"
        assert doccat.id_external is not None  # They are strings, do not need to be int

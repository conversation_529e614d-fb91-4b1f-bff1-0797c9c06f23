# Create your tests here.

import pytest
import structlog
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser

from bcge.services import create_export_status_object_list
from dossier.fakes import add_some_fake_semantic_documents
from dossier.helpers_v2 import get_semantic_pages
from semantic_document.models import SemanticDocument
from semantic_document.services import (
    set_semantic_documents_ready_for_export,
)
from statemgmt.configurations.semantic_document_state_machine import (
    SemanticDocumentState,
)
from workers.models import SemanticDocumentExport


User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_set_semantic_documents_ready_for_export_one_doc(
    prepare_data_export, bcge_authenticated_client, django_assert_num_queries
):
    """
    Triage performance issues with the export of semantic documents
    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = semantic_document.dossier

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    # For 1 semantic document have 75 queries
    # Now increased to 77 because sem doc empty page check
    with django_assert_num_queries(77):
        uuids = set_semantic_documents_ready_for_export(semantic_documents)
        assert len(uuids) == 1  # one sem doc in dossier

    mock_dispatch_publish_request.assert_called()

    semantic_document_export = SemanticDocumentExport.objects.get(uuid=str(uuids[0]))

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )


def test_set_semantic_documents_ready_for_export_ten_docs(
    prepare_data_export, bcge_authenticated_client, django_assert_num_queries
):
    """
    Triage performance issues with the export of semantic documents
    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = semantic_document.dossier
    num_semdocs = 10

    # document_categories_known = DocumentCategoryModel.objects.all().exclude(
    #     name__icontains="UNKNOWN"
    # )
    add_some_fake_semantic_documents(
        dossier=dossier,
        num_docs=num_semdocs - 1,  # because 1 semdoc already in dossier
        max_pages=10,
        min_num_pages=10,
        no_page_objects_per_page=10,
        valid_document_category_keys=["PASSPORT_CH"],
        allow_empty_docs=False,
    )

    semantic_documents = SemanticDocument.objects.filter(
        work_status__key=SemanticDocumentState.IN_FRONT_OFFICE.value,
        work_status__state_machine=dossier.account.active_semantic_document_work_status_state_machine,
        dossier=dossier,
    )

    # For 10 semantic documents have 329 queries
    # Increased to 340 because sem doc page count is tested for 0
    with django_assert_num_queries(340):
        uuids = set_semantic_documents_ready_for_export(semantic_documents)

    mock_dispatch_publish_request.assert_called()

    semantic_document_export = SemanticDocumentExport.objects.get(uuid=str(uuids[0]))

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    semantic_document.refresh_from_db()

    # Check that DEC has moved state from IN_FRONT_OFFICE to EXPORT_AVAILABLE
    assert (
        semantic_document.work_status.key
        == SemanticDocumentState.EXPORT_AVAILABLE.value
    )

    semdoc_exports = list(
        (
            SemanticDocumentExport.objects.filter(
                semantic_document=semantic_document, done__isnull=False
            )
            .filter(
                semantic_document__work_status__key=SemanticDocumentState.EXPORT_AVAILABLE.value
            )
            .order_by("done")
            .select_related(
                "semantic_document",
                "semantic_document__dossier",
                "semantic_document__document_category",
            )
        ).all()
    )

    exports_available = create_export_status_object_list(semdoc_exports)
    assert len(exports_available) == 1
    logger.info(f"exports_available: {exports_available}")


def test_get_semantic_pages(
    prepare_data_export, bcge_authenticated_client, django_assert_num_queries
):
    """
    Triage performance issues with the export of semantic documents
    """
    external_dossier_id, semantic_document, mock_dispatch_publish_request = (
        prepare_data_export
    )

    dossier = semantic_document.dossier

    # Good - only does one query
    with django_assert_num_queries(1):

        get_semantic_pages(dossier.uuid, show_soft_deleted=False)

import djclick as click
import structlog
from django.contrib.auth import get_user_model
from faker import Faker

from bcge.factories import BCGEAccountFactoryFaker, load_bcge_document_categories
from dossier.models import Account
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)

User = get_user_model()
logger = structlog.get_logger()


@click.group()
def grp():
    pass


@grp.command()
@click.argument("account_key")
@click.option(
    "--default_bucket_name",
    type=click.STRING,
    default=None,
    help="Bucket name if it should be different from the default bucket name for the dev account",
)
def load_account(account_key: str, default_bucket_name: str = None):
    """
    Create or update a bcge account with close to production config.
    This does not load document categories (use update_document_categories).

    Example:

    python manage.py reset-db
    python manage.py load_bcge_data load-account bcgeevo

    python manage.py load_bcge_data load-account bcgeevo --default_bucket_name dms-default-bucket

    python manage.py load_bcge_data load-account bcgetst
    python manage.py load_bcge_data load-account bcgeprd
    python manage.py load_bcge_data load-account bcgedasdfnotworking

    """
    Faker.seed(234777)
    bfac = BCGEAccountFactoryFaker(
        account_key=account_key, default_bucket_name=default_bucket_name
    )
    bfac.load_initial_document_categories()

    created_objects = create_semantic_document_state_machine()

    bfac.account.active_semantic_document_work_status_state_machine = created_objects[
        "machine"
    ]
    bfac.account.save()


@grp.command()
@click.argument("account_key")
def update_document_categories(account_key: str):
    """
    Load / update all document categories for BCGE. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_bcge_data update-document-categories bcgeevo

    @param account_key:
    @return:
    """
    load_bcge_document_categories(account_key)


@grp.command()
@click.argument("account_key", type=click.STRING, default="")
def update_semantic_document_state_machine(account_key: str = None):
    """
    Load / update all document categories for BCGE. Do not rely on the factory here
    as the factory changes the account itself on initialization.

    python manage.py load_bcge_data update-semantic-document-state-machine bcgeevo

    @param account_key:
    @return:
    """
    created_objects = create_semantic_document_state_machine()

    logger.info(
        "create state machine for semantic documents", num_objects=len(created_objects)
    )

    if account_key:
        account = Account.objects.get(key=account_key)

        account.active_semantic_document_work_status_state_machine = created_objects[
            "machine"
        ]
        account.save()

        logger.info(
            "activate semantic document state machine for account", account=account
        )
    else:
        logger.info("Skip linking to account as no account_key was provided")

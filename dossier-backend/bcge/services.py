from typing import List, Union


import structlog
from pydantic import BaseModel

from bcge.schemas import schemas as bcge_schemas
from workers.models import SemanticDocumentExport

logger = structlog.get_logger(__name__)


class ContractException(BaseModel):
    _class: str


class Msg(BaseModel):
    key: str
    message: str
    args: List[str] = []


class ServiceException(ContractException):
    msg: Msg


class PropertyFailure(BaseModel):
    path: str
    value: str
    proposal: str = None


class ValidationFailure(BaseModel):
    msg: Msg
    properties: List[PropertyFailure]


class ValidationException(ServiceException):
    failures: List[ValidationFailure]


async def external_service_check(
    partner_uuid: str, logon_id: str
) -> Union[bool, ServiceException, ValidationException]:
    # We call this service to check if a user is allowed access to a dossier

    return True


def create_export_status_object_list(semdoc_exports: List[SemanticDocumentExport]):
    response_list = []

    for export in semdoc_exports:
        semantic_document_url = export.file.get_fast_url()
        doc_cat = export.semantic_document.document_category
        response_list.append(
            bcge_schemas.ExportStatusSemanticDocument(
                semantic_document_export_request_uuid=export.uuid,
                semantic_document_uuid=export.semantic_document.uuid,
                external_dossier_id=export.semantic_document.dossier.external_id,
                semantic_document_url=semantic_document_url,
                document_category_key=doc_cat.name,
                document_category_id_external=(
                    doc_cat.id_external if doc_cat.id_external else doc_cat.name
                ),
                updated_at=export.done,
            )
        )

    return response_list

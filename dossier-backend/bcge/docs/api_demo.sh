#!/bin/bash

set -e  # Exit immediately if a command exits with a non-zero status

# Variables
client_id="bcgeevo-test-client"
client_secret="dl7hWj6VWkvdrtNGBayYCIfkMjr4WHyH"
environment="bcgeevo"
realm_id="dev-hypodossier"

hypodossier_api_endpoint="http://localhost:8000/partner/bcge/api/v1"
hypodossier_auth_endpoint="https://auth.hypo.duckdns.org/realms"

access_token_path="$(pwd)/HYPODOSSIER_API_ACCESS_TOKEN.private"
MAX_RETRIES=5

# Function to get the access token
get_access_token() {
    response=$(curl -s -X POST "$hypodossier_auth_endpoint/$realm_id/protocol/openid-connect/token" \
        -d "client_id=$client_id" \
        -d "client_secret=$client_secret" \
        -d "grant_type=client_credentials")

    if echo "$response" | jq . >/dev/null 2>&1; then
        access_token=$(echo "$response" | jq -r '.access_token')
        echo "$access_token"
    else
        echo "Failed to get access token" >&2
        exit 1
    fi
}


# Get the access token
access_token=$(get_access_token)

echo "Got access token $access_token"

# Headers for authenticated requests
headers=(-H "Authorization: Bearer $access_token")

# Ping the API
echo "Pinging the API..."
response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/ping" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Ping failed with status $http_code"
    exit 1
fi

echo "Ping response: $http_body"

# Create a new dossier
echo "Creating a new dossier..."
external_dossier_id=$(uuidgen)
dossier_data=$(jq -n \
    --arg external_dossier_id "$external_dossier_id" \
    --arg name "Demo Dossier" \
    --arg lang "fr" \
    '{external_dossier_id: $external_dossier_id, name: $name, lang: $lang}')

response=$(curl -s -w "\n%{http_code}" -X POST "${hypodossier_api_endpoint}/dossier" \
    -H "Content-Type: application/json" "${headers[@]}" \
    -d "$dossier_data")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" && "$http_code" != "201" ]]; then
    echo "Failed to create dossier with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Dossier created: $http_body"

# Upload a file to the dossier
echo "Uploading a file to the dossier..."
file_path="./lawssimplicity.pdf"

if [[ ! -f "$file_path" ]]; then
    echo "File not found: $file_path"
    exit 1
fi

response=$(curl -s -w "\n%{http_code}" -X POST "${hypodossier_api_endpoint}/dossier/${external_dossier_id}/original-files" \
    -F "file=@${file_path};type=application/pdf" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" && "$http_code" != "201" ]]; then
    echo "Failed to upload file with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "File uploaded: $http_body"

# Fetch semantic documents associated with the dossier
echo "Fetching semantic documents..."
retry_count=0
semantic_documents=""

while [[ $retry_count -lt $MAX_RETRIES ]]; do
    echo "Fetching semantic documents... (Attempt $((retry_count+1)))"
    response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/dossier/${external_dossier_id}/semantic-documents" "${headers[@]}")

    http_body=$(echo "$response" | head -n -1)
    http_code=$(echo "$response" | tail -n1)

    if [[ "$http_code" != "200" ]]; then
        echo "Failed to fetch semantic documents with status $http_code"
        echo "$http_body"
        exit 1
    fi

    if echo "$http_body" | jq . >/dev/null 2>&1; then
        semantic_documents=$(echo "$http_body")
        count=$(echo "$semantic_documents" | jq 'length')
        if [[ "$count" -gt 0 ]]; then
            echo "Semantic document found: $(echo "$semantic_documents" | jq '.[0]')"
            break
        fi
    fi

    retry_count=$((retry_count+1))
    sleep 5
done

if [[ -z "$semantic_documents" ]]; then
    echo "No semantic documents found after $MAX_RETRIES attempts"
    exit 1
fi

# Update a semantic document if available
semantic_document=$(echo "$semantic_documents" | jq '.[0]')
semantic_document_uuid=$(echo "$semantic_document" | jq -r '.uuid')

echo "Updating semantic document $semantic_document_uuid..."
update_data=$(jq -n \
    --arg external_semantic_document_id "Updated External ID" \
    --arg access_mode "read_only" \
    '{external_semantic_document_id: $external_semantic_document_id, access_mode: $access_mode}')

response=$(curl -s -w "\n%{http_code}" -X PATCH "${hypodossier_api_endpoint}/dossier/${external_dossier_id}/semantic-documents/${semantic_document_uuid}" \
    -H "Content-Type: application/json" "${headers[@]}" \
    -d "$update_data")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to update semantic document with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Semantic document updated: $http_body"

# Set Semantic Document ready for export and trigger processing
echo "Exporting semantic document to PDF..."
response=$(curl -s -w "\n%{http_code}" -X POST "${hypodossier_api_endpoint}/export/dossier/${external_dossier_id}/semantic-documents/${semantic_document_uuid}/set-state-ready-for-export" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" && "$http_code" != "201" ]]; then
    echo "Failed to export semantic document with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Export request submitted: $http_body"

# Check export status
echo "Checking export status..."

retry_count=0
while [[ $retry_count -lt $MAX_RETRIES ]]; do
    echo "Checking export status... (Attempt $((retry_count+1)))"
    response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/export/${external_dossier_id}/semantic-documents-available" "${headers[@]}")

    http_body=$(echo "$response" | head -n -1)
    http_code=$(echo "$response" | tail -n1)

   if [[ "$http_code" == "200" ]]; then
        export_status="$http_body"
        if [[ "$export_status" != "[]" ]]; then
            echo "Export status: $export_status"
            break
        fi
    elif [[ "$http_code" == "404" ]]; then
        :
        # Do nothing, continue retrying
    else
        echo "Failed to check export status with status $http_code"
        echo "$http_body"
        exit 1
    fi

    retry_count=$((retry_count+1))
    sleep 5
done

# We can also get a list of all semantic documents that are ready for export for a given account via
# response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/export/all-semantic-documents-available" "${headers[@]}")

# Set export status to complete
echo "Setting export status to complete..."
response=$(curl -s -w "\n%{http_code}" -X POST "${hypodossier_api_endpoint}/export/${external_dossier_id}/semantic-documents/${semantic_document_uuid}/set-done" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to set export status to complete with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Export status: $http_body"

# Check to make sure we have no more semantic documents available for download/export
echo "Checking for more semantic documents to export..."
response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/export/${external_dossier_id}/semantic-documents-available" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to check export status with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "New Export status: $http_body"

# Delete the semantic document
echo "Deleting semantic document $semantic_document_uuid..."
response=$(curl -s -w "\n%{http_code}" -X DELETE "${hypodossier_api_endpoint}/dossier/${external_dossier_id}/semantic-document/${semantic_document_uuid}" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to delete semantic document with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Semantic document deleted: $http_body"

# Restore the semantic document
echo "Restoring semantic document $semantic_document_uuid..."
response=$(curl -s -w "\n%{http_code}" -X PUT "${hypodossier_api_endpoint}/dossier/${external_dossier_id}/semantic-document/${semantic_document_uuid}/restore" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to restore semantic document with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Semantic document restored: $http_body"

# Fetch original files
echo "Fetching original files..."
response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/original-files/" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to fetch original files with status $http_code"
    echo "$http_body"
    exit 1
fi

original_files="$http_body"
echo "Original files: $original_files"

# Export the original file
original_file=$(echo "$original_files" | jq '.[0]')
original_file_uuid=$(echo "$original_file" | jq -r '.original_file_uuid')

echo "Exporting original file $original_file_uuid..."
response=$(curl -s -w "\n%{http_code}" -X GET "${hypodossier_api_endpoint}/export/original-file/${original_file_uuid}" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "200" ]]; then
    echo "Failed to export original file with status $http_code"
    echo "$http_body"
    exit 1
fi

exported_file="$http_body"
echo "Exported file info: $exported_file"

# Delete the dossier
echo "Deleting the dossier..."
response=$(curl -s -w "\n%{http_code}" -X DELETE "${hypodossier_api_endpoint}/dossier/${external_dossier_id}" "${headers[@]}")

http_body=$(echo "$response" | head -n -1)
http_code=$(echo "$response" | tail -n1)

if [[ "$http_code" != "202" ]]; then
    echo "Failed to delete dossier with status $http_code"
    echo "$http_body"
    exit 1
fi

echo "Dossier deleted: $http_body"

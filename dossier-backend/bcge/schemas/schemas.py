from enum import Enum
from typing import List, Dict, Optional, Annotated
from uuid import UUID

from dateutil import tz
from datetime import datetime
from pydantic import Field, AnyHttpUrl, BaseModel, StringConstraints, RootModel

from core.generics import AnyHttpUrlStr
from core.types import ExternalDossierID, DossierName, PrincipalID
from dossier import schemas_external
from dossier.schemas import Language, EntityTypes, AccessMode
from dossier.statemachine_types import DossierState
from semantic_document.schemas import Confidence


class AccountName(str, Enum):
    # These strings are used as account keys
    # evo == development
    bcged = "bcgeevo"  # evo
    bcget = "bcgetst"  # tst
    bcgei = "bcgeint"  # int
    bcgep = "bcgeprd"  # domain: no suffix, just bcge.hypodossier.ch


class CreateDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: DossierName
    lang: Optional[Language] = Language.fr


class ExportDossierExport(BaseModel):
    external_dossier_id: ExternalDossierID


class SemanticPage(BaseModel):
    uuid: UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: Optional[str] = None

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class SemanticDocument(BaseModel):
    uuid: UUID

    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None

    title: str

    document_category_confidence: Confidence

    document_category_key: str

    semantic_pages: List[SemanticPage]

    entity_type: Optional[EntityTypes] = None
    entity_key: Optional[str] = None

    access_mode: AccessMode = AccessMode.READ_WRITE

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())

    work_status_key: Optional[str] = None


class Dossier(BaseModel):
    uuid: UUID
    external_dossier_id: ExternalDossierID

    updated_at: datetime
    created_at: datetime


class ExportProcessingStatus(str, Enum):
    PROCESSING = "PROCESSING"
    ERROR = "ERROR"
    PROCESSED = "PROCESSED"


class ErrorMessageSchema(BaseModel):
    error_message: str | None = None


class ExportStatusSemanticDocument(BaseModel):
    semantic_document_export_request_uuid: UUID
    semantic_document_uuid: UUID
    # We use AnyHttpUrl instead of HttpUrl as CI uses ports as part of the URL
    semantic_document_url: AnyHttpUrlStr
    external_dossier_id: Optional[ExternalDossierID] = None

    # Internal key, e.g. PASSPORT_CH
    document_category_key: str

    # document_category_id    # 606
    #
    # document_category_title_translated  # Plan situation
    #
    # title_suffix # Maria Mustermann PostFinance changed to plan de situation / Test 3
    #
    # title # 606 Plan de situation Maria Mustermann PostFinance changed to plan de situation / Test 3

    # External unique identifier in M-Files that the document category should be mapped to, e.g. PASSPORT_IN_BCGE
    document_category_id_external: Optional[str] = None

    updated_at: datetime


class SemanticDocumentPDFExportRequest(BaseModel):
    uuid: UUID


class DocumentCategory(BaseModel):
    key: str
    id: str
    title_de: str
    description_de: Optional[str] = None


DocumentCategories = RootModel[Dict[str, DocumentCategory]]


class DossierAccessRequest(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class DossierAuthorization(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class FileStatus(str, Enum):
    PROCESSING = "processing"
    ERROR = "error"
    PROCESSED = "processed"


class ExtractedFile(BaseModel):
    uuid: UUID
    path_from_original: str
    file_name: str
    status: FileStatus
    file_url: AnyHttpUrl
    created_at: datetime
    updated_at: datetime


class OriginalFile(BaseModel):
    uuid: UUID
    name: str
    status: FileStatus
    extracted_files: List[ExtractedFile]
    file_url: AnyHttpUrl
    created_at: datetime
    updated_at: datetime


class DossierProcessingStatus(BaseModel):
    dossier_uuid: UUID
    external_id: str
    progress: int
    original_files: List[OriginalFile]


class ChangeDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    lang: Optional[Language] = None


class SemanticDocumentUpdate(BaseModel):
    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None
    access_mode: Optional[AccessMode] = None


# BBox, PageObjectTitles and PageObjectFullApiData are duplicates of
# dossier schemas. We can't change ZKB API schemas without a contractual agreement
# hence duplicate them here, to make sure a change in dossier/schemas does not accidentally
# break one of theirs


class BBox(BaseModel):
    ref_width: int
    ref_height: int
    top: int
    left: int
    right: int
    bottom: int


class MultilingualTitles(BaseModel):
    de: str
    en: str
    fr: str
    it: str


class DossierAccessGrant(BaseModel):
    # At bcge the only identifier we get is the username (which is the emailaddress)
    # via access grant schema.
    # Firstname, lastname, email and username is provided via sso.
    expires_at: datetime

    username: str  # "<EMAIL>"

    external_dossier_id: str

    has_access: bool


class OriginalFileSourceSchema(str, Enum):
    API_SOURCE = "API"
    DMF_SOURCE = "DMF"


class OriginalFileSchema(BaseModel):
    external_dossier_id: ExternalDossierID
    original_file_uuid: UUID
    created_at: datetime


class ExportOriginalFileSchema(BaseModel):
    external_dossier_id: ExternalDossierID
    original_file_uuid: UUID
    file_url: AnyHttpUrlStr
    created_at: datetime


class DossierCloseReadyResponse(schemas_external.DossierCloseReadyResponse):
    pass


class DossierCloseResponse(schemas_external.DossierCloseResponse):
    pass


class PrepareCloseDossierResponse(schemas_external.PrepareCloseDossierResponse):
    pass


class DossierStatusResponse(BaseModel):
    """Schema for dossier status response"""

    status_key: Optional[DossierState] = None

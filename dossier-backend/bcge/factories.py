from typing import Optional

import structlog

from assets import ASSETS_PATH
from bcge.schemas import schemas
from dossier.document_category_mapping_external_id import (
    add_external_id_to_document_categories,
)
from dossier.dossier_access_external import (
    get_access_check_provider_default_dossier_access_grant,
)
from dossier.dossier_work_status import (
    DOSSIER_STATE_MACHINE_NAME,
    PATH_DOSSIER_STATE_MACHINE_EXPORT,
)
from dossier.factories import (
    set_new_account_defaults,
    DefaultAccountFactoryFaker,
)
from dossier.fakes import load_initial_document_categories
from dossier.models import (
    Account,
    NavigationStrategy,
    DocumentCategory,
    DossierAccessCheckErrorComponent,
    DossierCloseStrategy,
)
from statemgmt.configurations.semantic_document_state_machine import (
    create_semantic_document_state_machine,
)
from statemgmt.export import update_state_machine
from statemgmt.models import StateMachine

logger = structlog.get_logger()

FRONTEND_THEME_BCGE: str = "BC<PERSON>"


def update_or_create_bcge_account(
    account_key: str,
    default_bucket_name: str = None,
    update_statemgmt: bool = False,
) -> Account:
    assert is_valid_account_key(
        account_key
    ), f"Invalid account key: '{account_key}'. Valid keys are {[a.value for a in schemas.AccountName]}"

    account, _ = Account.objects.update_or_create(key=account_key)

    state_machine_name = DOSSIER_STATE_MACHINE_NAME
    state_machine = StateMachine.objects.filter(name=state_machine_name).first()
    if update_statemgmt or state_machine is None:
        p = PATH_DOSSIER_STATE_MACHINE_EXPORT
        assert p.exists()
        state_machine = update_state_machine(p, state_machine_name)
        account.active_work_status_state_machine = state_machine
    assert state_machine is not None

    if account_key == schemas.AccountName.bcged.value:
        # Used for development HD internally and shared dev environment
        account.name = "BCGE Development"
        # account.default_bucket_name = "dms-default-bucket"
        account.default_bucket_name = "production-v2-test-bcgeevo"
        account.dmf_endpoint = "https://evo.bcge.hypodossier.ch"
    elif account_key == schemas.AccountName.bcget.value:
        account.name = "BCGE Test"
        account.default_bucket_name = "production-v2-test-bcgetst"
        account.dmf_endpoint = "https://tst.bcge.hypodossier.ch"
    elif account_key == schemas.AccountName.bcgei.value:
        account.name = "BCGE Integration"
        account.default_bucket_name = "production-v2-test-bcgeint"
        account.dmf_endpoint = "https://int.bcge.hypodossier.ch"
    elif account_key == schemas.AccountName.bcgep.value:
        account.name = "BCGE Production"
        account.default_bucket_name = "production-v2-bcge-dms"
        account.dmf_endpoint = "https://bcge.hypodossier.ch"

        account.navigation_strategy = NavigationStrategy.NO_DOSSIER_LIST_BRANDED
        account.enable_button_create = False
        account.enable_feedback_form = False
        account.enable_uploading_files = False
        account.enable_document_upload = False
        account.allow_dossier_listing = False

    else:
        raise ValueError(f"Invalid account key '{account_key}'")

    if default_bucket_name:
        account.default_bucket_name = default_bucket_name

    set_new_account_defaults(account)

    # Standard config for bcge
    account.max_dossier_expiry_duration_days = 500

    # To allow 20 days for final archiving process
    account.default_dossier_expiry_duration_days = 520
    account.valid_dossier_languages = ["Fr", "En"]
    account.valid_ui_languages = ["fr", "en"]
    account.instructions_menu_key = "bcge"
    account.show_document_category_external = False
    account.show_business_case_type = False
    account.enable_virus_scan = False
    account.enable_rendering_hurdles_tab = False
    account.enable_dossier_permission = False
    account.enable_dossier_assignment = False

    # Download will most likely happen via API. That will provide the document_category.
    # No need to put it into the attachment for now
    account.enable_download_metadata_json = False
    account.enable_semantic_document_export = True

    # Ensure that a state machine is created
    created_objects = create_semantic_document_state_machine()

    state_machine = created_objects["machine"]

    # Associate state machine with account
    account.active_semantic_document_work_status_state_machine = state_machine

    # This is needed to customize the "Übermittlung an Archiv" button
    account.frontend_theme = FRONTEND_THEME_BCGE
    account.enable_semantic_document_export_unknown_documents = True
    account.dossier_close_strategy = (
        DossierCloseStrategy.REQUIRE_SEMANTIC_DOCUMENT_EXPORT_DONE
    )
    account.dossier_close_expiry_days = 30

    account.dossier_access_check_provider = (
        get_access_check_provider_default_dossier_access_grant()
    )
    account.dossier_access_check_error_component = (
        DossierAccessCheckErrorComponent.BCGE_ACCESS_CHECK_INSTRUCTIONS
    )

    account.save()

    return account


# Function to validate if the string is part of the enum
def is_valid_account_key(account_key: str) -> bool:
    try:
        # Attempt to match the account_key with an enum member
        schemas.AccountName(account_key)
        return True
    except ValueError:
        # If account_key is not found in the enum, return False
        return False


def load_bcge_document_categories(account_key):

    # This is the latest doc cat definition that is verified for bcge
    document_categories_json_path = (
        ASSETS_PATH / "document_category/default/DocumentCategory-2024-11-13.json"
    )
    account = Account.objects.get(key=account_key)
    load_initial_document_categories(
        account, document_categories_json_path=document_categories_json_path
    )

    document_categories_json_path_custom = (
        ASSETS_PATH / "document_category/bcge/DocumentCategory-2024-09-25.json"
    )
    document_categories_custom, _, _, _ = load_initial_document_categories(
        account, document_categories_json_path_custom
    )

    d = DocumentCategory.objects.get(name="TAX_ATTACHMENTS", account__key=account_key)
    d.exclude_for_recommendation = True
    d.save()

    d = DocumentCategory.objects.get(name="ID_OTHER", account__key=account_key)
    d.fr = "Autres documents identification"
    d.save()

    # d = DocumentCategory.objects.get(name="RESIDENCE_PERMIT", account__key=account_key)
    # d.de = "Aufenthaltstitel EU / EFTA"
    # d.fr = "Titre séjour UE/AELE"
    # d.save()

    # 608 SITG
    d = DocumentCategory.objects.get(name="GIS_INFO", account__key=account_key)
    d.fr = "Extrait SITG"
    d.save()

    # 620
    d = DocumentCategory.objects.get(
        name="PROPERTY_VALUATION", account__key=account_key
    )
    d.fr = "Estimation ou expertise valeur immeuble"
    d.save()

    # 624
    d = DocumentCategory.objects.get(name="ENERGY_CERTFICATE", account__key=account_key)
    d.fr = "Certificat énergétique divers"
    d.save()

    d = DocumentCategory.objects.get(name="RENOVATIONS", account__key=account_key)
    d.de = "Renovationen Diverses"
    d.en = "Renovations Miscellaneous"
    d.fr = "Rénovations diverses"
    d.it = "Rinnovamenti vari"
    d.save()

    d = DocumentCategory.objects.get(
        name="AUTHORIZATION_EMAIL", account__key=account_key
    )
    d.de = "DTTT"
    d.en = "DTTT"
    d.fr = "DTTT"
    d.it = "DTTT"
    d.save()

    d = DocumentCategory.objects.get(name="MORTGAGE_CONTRACT", account__key=account_key)
    d.de = "Hypothekarvertrag Fremdbank"
    d.en = "Mortgage contract external bank"
    d.fr = "Contract hypothécaire autres banque"
    d.it = "Contratto ipotecario esterno"
    d.save()

    d = DocumentCategory.objects.get(name="POWER_OF_ATTORNEY", account__key=account_key)
    d.fr = "Procuration notaire ou autre"
    d.save()

    for key in ["FOREIGN_NATIONAL_ID", "PROOF_OF_INCOME", "MISC_CAT", "ZEK_INFO"]:
        d = DocumentCategory.objects.get(name=key, account__key=account_key)
        d.exclude_for_recommendation = True
        d.save()

    # Now add the external id based on a local file for all document categories
    # As of 240926 these are 264 document categories.
    # New mapping that is unified on all environmens was added 24.2.25
    doccat_mapping_csv_path = (
        ASSETS_PATH
        / "document_category/bcge/document_category_mapping/DocumentCategoryMapping-2025-02-24.csv"
    )
    success_mapping, updates, not_found = add_external_id_to_document_categories(
        account_key, doccat_mapping_csv_path
    )
    if success_mapping:
        logger.info(
            "All HD document categories were successfully mapped to BCGE external_id"
        )
    else:
        logger.info(
            "Could not find external id mapping for all document categories. This might be ok doc_cat_key is returned as external_id as fallback",
            num_not_found=len(not_found),
            num_updates=len(updates),
        )
        if not_found:
            logger.info("not_found_in_mapping", not_found=not_found)


class BCGEAccountFactoryFaker(DefaultAccountFactoryFaker):
    def __init__(
        self,
        account: Optional[Account] = None,
        account_key: Optional[str] = None,
        default_bucket_name: Optional[str] = None,
    ):
        self.document_categories = None

        if account is None:
            if account_key is None:
                account_key = schemas.AccountName.bcged.value
        else:
            account_key = account.key

        account = update_or_create_bcge_account(account_key, default_bucket_name)

        super().__init__(account, "fr_CH")

    def load_initial_document_categories(self):

        num_expected_categories = 267

        load_bcge_document_categories(self.account.key)

        self.document_categories = list(
            DocumentCategory.objects.filter(account=self.account).all()
        )

        assert (
            len(self.document_categories) == num_expected_categories
        ), f"Instead of {num_expected_categories}, {len(self.document_categories)} have been found "

        return self.document_categories

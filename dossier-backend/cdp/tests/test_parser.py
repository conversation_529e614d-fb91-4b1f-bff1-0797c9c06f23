from pathlib import Path
from tempfile import TemporaryDirectory

import pytest

from cdp.factories import FieldDefinitionFactory, AssignedFieldFactory
from cdp.models import ReturnType
from cdp.schemas import (
    StringValue,
    Area,
    Volume,
    IntegerValue,
    FloatValue,
    BooleanValue,
    Currency,
    Date,
    Street,
    ZipCode,
    City,
)
from cdp.scripts.get_address_blocks import process_csv, save_comparison_results
from cdp.value_parser import (
    get_parser,
    parse_value,
    StringParser,
)


@pytest.mark.django_db
def test_get_parser_for_null_return_type(return_type_keys):
    field_definition = FieldDefinitionFactory()
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    return_type = (
        assigned_field.field_definition.return_type.key
        if assigned_field.field_definition.return_type
        else "string"
    )
    parser = get_parser(return_type)
    assert isinstance(parser, StringParser)
    result = parse_value("some value", assigned_field, use_cached_parser=False)
    assert isinstance(result, StringValue)
    assert result.return_type == "string"
    assert result.value == "some value"


@pytest.mark.django_db
def test_get_parser_for_unknown_return_type(return_type_keys):
    return_type = ReturnType.objects.create(key="unknown")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    return_type = (
        assigned_field.field_definition.return_type.key
        if assigned_field.field_definition.return_type
        else None
    )
    parser = get_parser(return_type)
    assert isinstance(parser, StringParser)
    result = parse_value("some value", assigned_field, use_cached_parser=False)
    assert isinstance(result, StringValue)
    assert result.return_type == "string"
    assert result.value == "some value"


@pytest.mark.parametrize(
    "area_parser_input, expected_area_value, expected_area_unit",
    [
        ("100 m2", 100, "m2"),
        ("100m2", 100, "m2"),
        ("200 sqm", 200, "m2"),
        ("200sqm", 200, "m2"),
        ("300 square meters", 300, "m2"),
        ("300square meters", 300, "m2"),
        ("100 ft2", 100, "ft2"),
        ("100ft2", 100, "ft2"),
        ("200 sqft", 200, "ft2"),
        ("200sqft", 200, "ft2"),
        ("300 square feet", 300, "ft2"),
        ("300square feet", 300, "ft2"),
        ("500", 500, None),
        # Standalone 'm' unit
        ("80 m", 80.0, "m2"),
        ("80m", 80.0, "m2"),
        ("1234 m", 1234.0, "m2"),
        # Thousand separators (') with units
        ("2'399 m2", 2399.0, "m2"),
        ("2'399m2", 2399.0, "m2"),  # No space
        ("1'000'000 sqm", 1000000.0, "m2"),  # Multiple separators
        ("1'000 m", 1000.0, "m2"),  # Separator + 'm' unit
        # Thousand separators (') without units
        ("1'234", 1234.0, None),
        ("1'000'000", 1000000.0, None),
        # Decimals (with/without separators/units)
        ("123.45 m2", 123.45, "m2"),
        ("123.45m", 123.45, "m2"),  # Decimal + 'm' unit
        ("1'234.56 m", 1234.56, "m2"),  # Separator + Decimal + 'm' unit
        ("1'234.56", 1234.56, None),  # Separator + Decimal only
        ("987.65", 987.65, None),  # Decimal only
        # Case variations (already covered by re.IGNORECASE but good to have)
        ("75 SQM", 75.0, "m2"),
        ("60 M", 60.0, "m2"),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_area_return_type(
    return_type_keys, area_parser_input, expected_area_value, expected_area_unit
):
    return_type, _ = ReturnType.objects.get_or_create(key="area")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(area_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, Area)
    assert result.value == expected_area_value
    assert result.unit == expected_area_unit
    assert result.return_type == "area"


@pytest.mark.parametrize(
    "volume_parser_input, expected_volume_value, expected_volume_unit",
    [
        ("100 m3", 100, "m3"),
        ("100m3", 100, "m3"),
        ("200 cbm", 200, "m3"),
        ("200cbm", 200, "m3"),
        ("300 cubic meters", 300, "m3"),
        ("300cubic meters", 300, "m3"),
        ("100 ft3", 100, "ft3"),
        ("100ft3", 100, "ft3"),
        ("200 cft", 200, "ft3"),
        ("200cft", 200, "ft3"),
        ("300 cubic feet", 300, "ft3"),
        ("300cubic feet", 300, "ft3"),
        ("500", 500, None),
        # Thousand separators (') with units
        ("2'399 m3", 2399.0, "m3"),
        ("2'399cbm", 2399.0, "m3"),  # No space + cbm normalization
        ("1'000'000 ft3", 1000000.0, "ft3"),  # Multiple separators
        ("1'000 cubic meters", 1000.0, "m3"),  # Separator + long unit name
        # Thousand separators (') without units
        ("1'234", 1234.0, None),
        ("1'000'000", 1000000.0, None),
        # Decimals (with/without separators/units)
        ("123.45 m3", 123.45, "m3"),
        ("123.45cbm", 123.45, "m3"),  # Decimal + cbm normalization
        ("1'234.56 cft", 1234.56, "ft3"),  # Separator + Decimal + cft normalization
        ("1'234.56", 1234.56, None),  # Separator + Decimal only
        ("987.65", 987.65, None),  # Decimal only
        # Case variations (already covered by re.IGNORECASE but good to have)
        ("75 CBM", 75.0, "m3"),
        ("60 Cubic Feet", 60.0, "ft3"),
        ("99 M3", 99.0, "m3"),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_volume_return_type(
    return_type_keys, volume_parser_input, expected_volume_value, expected_volume_unit
):
    return_type, _ = ReturnType.objects.get_or_create(key="volume")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(volume_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, Volume)
    assert result.value == expected_volume_value
    assert result.unit == expected_volume_unit
    assert result.return_type == "volume"


@pytest.mark.parametrize(
    "currency_parser_input, expected_currency_value, expected_currency_unit",
    [
        ("100 EUR", 100, "EUR"),
        ("100.002EUR", 100.002, "EUR"),
        ("200,000.020 USD", 200000.020, "USD"),
        ("200USD", 200, "USD"),
        ("300'333.343 CHF", 300333.343, "CHF"),
        ("300'333CHF", 300333, "CHF"),
        ("500", 500, None),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_currency_return_type(
    return_type_keys,
    currency_parser_input,
    expected_currency_value,
    expected_currency_unit,
):
    return_type, _ = ReturnType.objects.get_or_create(key="currency")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(currency_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, Currency)
    assert result.value == expected_currency_value
    assert result.currency == expected_currency_unit


@pytest.mark.parametrize(
    "integer_parser_input, expected_integer_value",
    [
        ("100", 100),
        ("200 # some string", 200),
        ("300%specialCharacterAfter", 300),
        ("SpecialCharacterBefore#400", 400),
        ("some string before # 500", 500),
        # Thousand separators
        ("1'000", 1000),
        ("1,000", 1000),
        ("1'000'000", 1000000),
        ("1,000,000", 1000000),
        ("Number: 1'234", 1234),
        ("Number: 1,234", 1234),
        ("   1'234   ", 1234),
        ("   1,234   ", 1234),
        ("2023", 2023),
        ("300403", 300403),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_integer_return_type(
    return_type_keys, integer_parser_input, expected_integer_value
):
    return_type, _ = ReturnType.objects.get_or_create(key="integer")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(integer_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, IntegerValue)
    assert result.value == expected_integer_value
    assert result.return_type == "integer"


@pytest.mark.parametrize(
    "float_parser_input, expected_float_value",
    [
        ("100.0", 100.0),
        ("200.0 # some string", 200.0),
        ("300.0%specialCharacterAfter", 300.0),
        ("SpecialCharacterBefore#400.0", 400.0),
        ("some string before # 500.0", 500.0),
        ("100", 100.0),
        ("200 # some string", 200.0),
        ("300%specialCharacterAfter", 300.0),
        ("SpecialCharacterBefore#400", 400.0),
        ("some string before # 500", 500.0),
        # Thousand separators
        ("1'000.50", 1000.50),
        ("1,000.50", 1000.50),
        ("1'000'000.99", 1000000.99),
        ("1,000,000.99", 1000000.99),
        ("Number: 1'234.56", 1234.56),
        ("Number: 1,234.56", 1234.56),
        ("   1'234.56   ", 1234.56),
        ("   1,234.56   ", 1234.56),
        ("1'000", 1000.0),
        ("1,000", 1000.0),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_float_return_type(
    return_type_keys, float_parser_input, expected_float_value
):
    return_type, _ = ReturnType.objects.get_or_create(key="float")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(float_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, FloatValue)
    assert result.return_type == "float"
    assert result.value == expected_float_value


@pytest.mark.parametrize(
    "string_parser_input, expected_string_value",
    [
        ("some string", "some string"),
        (
            "some string with special characters #",
            "some string with special characters #",
        ),
        (
            "some string",
            "some string",
        ),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_string_return_type(
    return_type_keys, string_parser_input, expected_string_value
):
    return_type, _ = ReturnType.objects.get_or_create(key="string")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(string_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, StringValue)
    assert result.return_type == "string"
    assert result.value == expected_string_value


@pytest.mark.parametrize(
    "boolean_parser_input, expected_boolean_value",
    [
        ("True", True),
        ("true", True),
        ("False", False),
        ("false", False),
        ("1", True),
        ("0", False),
        ("some string", True),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_boolean_return_type(
    return_type_keys, boolean_parser_input, expected_boolean_value
):
    return_type, _ = ReturnType.objects.get_or_create(key="boolean")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(boolean_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, BooleanValue)
    assert result.value == expected_boolean_value
    assert result.return_type == "boolean"


@pytest.mark.parametrize(
    "date_parser_input, expected_date_value",
    [
        ("2021-12-30", "2021-12-30"),
        ("2021/12/30", "2021-12-30"),
        ("2021.12.30", "2021-12-30"),
        ("30.12.2021", "2021-12-30"),
        ("30-12-2021", "2021-12-30"),
        ("30/12/2021", "2021-12-30"),
        ("12-30-2021", "2021-12-30"),
        ("12.30.2021", "2021-12-30"),
        ("12/30/2021", "2021-12-30"),
        ("02.01.2021", "2021-01-02"),
        ("03-05-2021", "2021-05-03"),
        ("2020.03.02", "2020-03-02"),
    ],
)
@pytest.mark.django_db
def test_parse_value_with_date_return_type(
    return_type_keys, date_parser_input, expected_date_value
):
    return_type, _ = ReturnType.objects.get_or_create(key="date")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(date_parser_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, Date)
    assert result.return_type == "date"
    assert result.value == expected_date_value


@pytest.mark.parametrize(
    "street_input, expected_street_name, expected_street_number, expected_suffix",
    [
        ("Musterstrasse 12", "Musterstrasse", 12, None),
        ("Musterstrasse 12a", "Musterstrasse", 12, "a"),
        ("Musterstrasse 12 a", "Musterstrasse", 12, "a"),
    ],
)
@pytest.mark.django_db
def test_parse_street_return_type(
    return_type_keys,
    street_input,
    expected_street_name,
    expected_street_number,
    expected_suffix,
):
    return_type, _ = ReturnType.objects.get_or_create(key="street")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(
        street_input, assigned_field, page_object_type="street", use_cached_parser=False
    )
    assert isinstance(result, Street)
    assert result.return_type == "street"
    assert result.value.street_name == expected_street_name
    assert result.value.street_number == expected_street_number
    assert result.value.suffix == expected_suffix
    print(result)


@pytest.mark.parametrize(
    "address_block_input, expected_street_name, expected_street_number, expected_suffix",
    [
        (
            "Herr Markus Mustermann\nMusterstrasse 12\n8045 Zurich\n Switzerland",
            "Musterstrasse",
            12,
            None,
        ),
        (
            "Herr Markus Mustermann\nMusterstrasse 12a,\n8045 Zurich\n Switzerland",
            "Musterstrasse",
            12,
            "a",
        ),
        (
            "Herr Markus Mustermann\nMusterstrasse 12a, 8045 Zurich\n Switzerland",
            "Musterstrasse",
            12,
            "a",
        ),
        ("Musterstrasse 12a\n 8045 Zurich\n Switzerland", "Musterstrasse", 12, "a"),
    ],
)
@pytest.mark.django_db
def test_parse_street_return_type_for_address_blocks(
    return_type_keys,
    address_block_input,
    expected_street_name,
    expected_street_number,
    expected_suffix,
):
    return_type, _ = ReturnType.objects.get_or_create(key="street")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(address_block_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, Street)
    assert result.return_type == "street"
    assert result.value.street_name == expected_street_name
    assert result.value.street_number == expected_street_number
    assert result.value.suffix == expected_suffix


@pytest.mark.parametrize(
    "address_block_abbreviated_input, expected_street_name, expected_street_number, expected_suffix",
    [
        ("Av. des Alpes 23, 1003 Lausanne", "Avenue des Alpes", 23, None),
        ("Rte. de la Gare 22, 1618 Châtel-St-Denis", "Route de la Gare", 22, None),
        ("R de Lyon 10, 1203 Genève", "Rue de Lyon", 10, None),
        ("Ch. des Geais 2, 1967 Bramois", "Chemin des Geais", 2, None),
    ],
)
@pytest.mark.django_db
def test_parse_street_return_type_for_abbreviated_address_blocks(
    return_type_keys,
    address_block_abbreviated_input,
    expected_street_name,
    expected_street_number,
    expected_suffix,
):
    return_type, _ = ReturnType.objects.get_or_create(key="street")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(
        address_block_abbreviated_input, assigned_field, use_cached_parser=False
    )
    assert isinstance(result, Street)
    assert result.return_type == "street"
    assert result.value.street_name == expected_street_name
    assert result.value.street_number == expected_street_number
    assert result.value.suffix == expected_suffix


@pytest.mark.parametrize(
    "zip_code_input, expected_zip_code",
    [
        # Valid cases
        ("8000", 8000),
        ("8000 Zurich", 8000),
        ("8000 Zurich, Switzerland", 8000),
        (
            "Herr Markus Mustermann\nMusterstrasse 12a\n 8045 Zurich\n Switzerland",
            8045,
        ),
        ("CH-8000 Zurich", 8000),  # Handle potential prefix
        ("Postfach 123\n8045 Zürich", 8045),  # Handle multi-line
        ("12345", 12345),  # Valid 5-digit
        # Invalid cases (should result in None)
        ("123", None),  # Too short
        ("123456", None),  # Too long
        ("ABCD", None),  # Not digits
        ("Not a zip code", None),
        ("CH-123 Zurich", None),  # Too short with prefix
        ("", None),  # Empty string
    ],
)
@pytest.mark.django_db
def test_parse_zip_code_return_type(
    return_type_keys, zip_code_input, expected_zip_code
):
    """Test ZipCodeParser for valid and invalid inputs via parse_value."""
    return_type, _ = ReturnType.objects.get_or_create(key="zip_code")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(zip_code_input, assigned_field, use_cached_parser=False)

    if expected_zip_code is None:
        assert result is None
    else:
        assert isinstance(result, ZipCode)
        assert result.return_type == "zip_code"
        assert result.value == expected_zip_code


@pytest.mark.parametrize(
    "city_input, expected_city_name",
    [
        ("8000 Zurich", "Zurich"),
        (
            "Herr Markus Mustermann\nMusterstrasse 12a\n 8045 Zurich\n Switzerland",
            "Zurich",
        ),
        (
            "Herr Markus Mustermann\nMusterstrasse 12a, 8045 Zurich\n Switzerland",
            "Zurich",
        ),
        ("8000 Zurich, Switzerland", "Zurich"),
    ],
)
@pytest.mark.django_db
def test_parse_city_return_type(return_type_keys, city_input, expected_city_name):
    return_type, _ = ReturnType.objects.get_or_create(key="city")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(city_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, City)
    assert result.return_type == "city"
    assert result.value == expected_city_name


@pytest.mark.parametrize(
    "ratio_input, expected_value",
    [
        ("83/1000", 83),
        ("123/1000", 123),
        ("0/1000", 0),
        ("999/1000", 999),
        ("1000/1000", 1000),
        ("9/1000)", 9),  # OCR artifact: trailing parenthesis
        ("9/1000.", 9),  # OCR artifact: trailing period
    ],
)
@pytest.mark.django_db
def test_parse_value_ratio_return_type(return_type_keys, ratio_input, expected_value):
    return_type, _ = ReturnType.objects.get_or_create(key="value_ratio")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(ratio_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, IntegerValue)
    assert result.return_type == "integer"
    assert result.value == expected_value


@pytest.mark.parametrize(
    "invalid_ratio_input",
    [
        "83/100",  # Wrong denominator
        "83/1001",  # Wrong denominator
        "83/999",  # Wrong denominator
        "abc/1000",  # Non-numeric numerator
        "83/abc",  # Non-numeric denominator
        "83",  # Missing denominator
        "/1000",  # Missing numerator
        "83/",  # Missing denominator
        "83/1000/1000",  # Too many slashes
    ],
)
@pytest.mark.django_db
def test_parse_value_ratio_return_type_invalid_input(
    return_type_keys, invalid_ratio_input
):
    return_type, _ = ReturnType.objects.get_or_create(key="value_ratio")
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(invalid_ratio_input, assigned_field, use_cached_parser=False)
    assert isinstance(result, StringValue)
    assert result.return_type == "string"
    assert result.value == invalid_ratio_input


@pytest.mark.parametrize(
    "parser_type, invalid_input",
    [
        ("area", "invalid area string"),
        ("volume", "invalid volume string"),
        ("zip_code", "not a zip code"),
        ("zip_code", "123"),  # Invalid length
        ("zip_code", "123456"),  # Invalid length
        ("city", "Address line without City"),
    ],
)
@pytest.mark.django_db
def test_parse_value_returns_none_on_failure(
    return_type_keys, parser_type, invalid_input
):
    """Test that specific parsers (Area, Volume) return None via parse_value on failure."""
    return_type, _ = ReturnType.objects.get_or_create(key=parser_type)
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(invalid_input, assigned_field, use_cached_parser=False)

    assert result is None


@pytest.mark.parametrize(
    "parser_type, invalid_input",
    [
        ("integer", "not an integer"),
        ("float", "not a float"),
        ("currency", "noEuros EUR"),  # Missing value
        ("date", "not a date"),
        ("value_ratio", "abc/1000"),  # Invalid numerator
    ],
)
@pytest.mark.django_db
def test_parse_value_returns_stringvalue_on_failure(
    return_type_keys, parser_type, invalid_input
):
    """Test that other parsers return StringValue via parse_value on failure."""
    return_type, _ = ReturnType.objects.get_or_create(key=parser_type)
    field_definition = FieldDefinitionFactory(return_type=return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)

    result = parse_value(invalid_input, assigned_field, use_cached_parser=False)
    print(result)
    assert isinstance(result, StringValue)
    assert result.value == invalid_input
    assert result.return_type == "string"


@pytest.mark.django_db
def test_parsing_with_real_data():
    data_dir = Path(__file__).parent.parent / "scripts" / "data"
    address_block_file = data_dir / "anonymized_addresses_1000.csv"

    with TemporaryDirectory() as temp_dir:
        results_file = Path(temp_dir) / "address_parsing_results_1000.csv"

        results_df = process_csv(address_block_file)
        save_comparison_results(results_df, results_file)
        street_similarity_mean = results_df["street_similarity"].mean()
        zip_code_similarity_mean = results_df["zip_similarity"].mean()
        city_similarity_mean = results_df["city_similarity"].mean()
        print(f"Street similarity mean: {street_similarity_mean}")
        print(f"Zip code similarity mean: {zip_code_similarity_mean}")
        print(f"City similarity mean: {city_similarity_mean}")
        assert street_similarity_mean > 0.9
        assert zip_code_similarity_mean > 0.9
        assert city_similarity_mean > 0.9

import pytest


from cdp.services import get_document_date
from dossier.factories import (
    DossierFactory,
    SemanticDocumentFactory,
    SemanticPageFactory,
    PageObjectFactory,
    AccountFactory,
)
from processed_file.models import PageObjectTitle
from semantic_document.models import SemanticPagePageObject


@pytest.fixture
def setup_data():
    account = AccountFactory(key="default")
    dossier = DossierFactory(account=account, name="Dossier for Cdp Tests")
    # original_file = OriginalFileFactory(dossier=dossier)
    # extracted_file = ExtractedFileFactory(original_file=original_file, dossier=dossier)
    # processed_file = ProcessedFileFactory(
    #     extracted_file=extracted_file, dossier=dossier
    # )
    semantic_document = SemanticDocumentFactory(dossier=dossier)
    semantic_page = SemanticPageFactory(
        page_number=0, semantic_document=semantic_document, dossier=dossier
    )
    return dossier, semantic_document, semantic_page


@pytest.mark.django_db
def test_returns_document_date_present_on_same_page_as_sppo(setup_data):
    _, semantic_document, semantic_page = setup_data

    page_object = PageObjectFactory(
        key=PageObjectTitle.objects.create(key="key_1"),
        processed_page=semantic_page.processed_page,
        value="value_1",
    )

    sppo = SemanticPagePageObject.objects.create(
        page_object=page_object, semantic_page=semantic_page
    )

    semantic_doc_date = "2024-01-01"
    page_object_name = "document_date"
    po_key, _ = PageObjectTitle.objects.get_or_create(key=page_object_name)
    date_page_object = PageObjectFactory(
        key=po_key,
        processed_page=semantic_page.processed_page,
        value="2024-01-01",
    )
    SemanticPagePageObject.objects.create(
        page_object=date_page_object, semantic_page=semantic_page
    )
    result = get_document_date(sppo)
    assert result == semantic_doc_date


@pytest.mark.django_db
def test_returns_document_date_present_on_same_document_as_sppo_but_different_page(
    setup_data,
):
    _, semantic_document, semantic_page = setup_data

    page_object = PageObjectFactory(
        key=PageObjectTitle.objects.create(key="key_1"),
        processed_page=semantic_page.processed_page,
        value="value_1",
    )

    sppo = SemanticPagePageObject.objects.create(
        page_object=page_object, semantic_page=semantic_page
    )

    semantic_doc_date = "2024-01-01"
    page_object_name = "document_date"
    po_key, _ = PageObjectTitle.objects.get_or_create(key=page_object_name)
    date_page_object = PageObjectFactory(
        key=po_key,
        processed_page=SemanticPageFactory(
            page_number=2, semantic_document=semantic_document
        ).processed_page,
        value="2024-01-01",
    )
    SemanticPagePageObject.objects.create(
        page_object=date_page_object, semantic_page=semantic_page
    )
    result = get_document_date(sppo)
    assert result == semantic_doc_date


@pytest.mark.django_db
def test_returns_document_validity_start_date_if_document_date_not_present(setup_data):
    _, semantic_document, semantic_page = setup_data
    page_object = PageObjectFactory(
        key=PageObjectTitle.objects.create(key="key_1"),
        processed_page=semantic_page.processed_page,
        value="value_1",
    )

    sppo = SemanticPagePageObject.objects.create(
        page_object=page_object, semantic_page=semantic_page
    )

    semantic_doc_date = "2024-01-01"
    page_object_name = "document_validity_start_date"
    po_key, _ = PageObjectTitle.objects.get_or_create(key=page_object_name)
    date_page_object = PageObjectFactory(
        key=po_key,
        processed_page=semantic_page.processed_page,
        value="2024-01-01",
    )
    SemanticPagePageObject.objects.create(
        page_object=date_page_object, semantic_page=semantic_page
    )
    result = get_document_date(sppo)
    assert result == semantic_doc_date


@pytest.mark.django_db
def test_returns_created_at_date_if_no_dates_present(setup_data):
    dossier, semantic_document, semantic_page = setup_data
    page_object = PageObjectFactory(
        key=PageObjectTitle.objects.create(key="key_1"),
        processed_page=semantic_page.processed_page,
        value="value_1",
    )
    sppo = SemanticPagePageObject.objects.create(
        page_object=page_object, semantic_page=semantic_page
    )
    result = get_document_date(sppo)
    assert (
        result
        == page_object.processed_page.processed_file.extracted_file.original_file.created_at.date().isoformat()
    )

from datetime import datetime

import pytest
from django.core.cache import caches

from cdp.services import (
    prepare_semantic_page_details,
    fetch_semantic_pages_for_document,
)


@pytest.fixture(autouse=True)
def clear_caches():
    """Clear both caches before each test"""
    caches["default"].clear()
    caches["cdp-cache"].clear()


@pytest.mark.django_db
def test_fetch_semantic_pages_for_document_uses_cdp_cache(sample_dossier_cdp):
    """Test that fetch_semantic_pages_for_document uses the cdp-cache backend"""
    semantic_document = sample_dossier_cdp.semantic_documents.first()

    result1 = fetch_semantic_pages_for_document(semantic_document)

    # Clear default cache but not cdp-cache
    caches["default"].clear()

    # Second call should get from cdp-cache
    result2 = fetch_semantic_pages_for_document(semantic_document)

    # Verify both results are the same
    assert list(result1) == list(result2)

    # Verify data exists in cdp-cache but not in default cache
    cache_key = f"semantic_pages_{semantic_document.uuid}_{semantic_document.last_page_change_date}"
    assert caches["cdp-cache"].get(cache_key) is not None
    assert caches["default"].get(cache_key) is None


@pytest.mark.django_db
def test_prepare_semantic_page_details_uses_cdp_cache(sample_dossier_cdp):
    """Test that prepare_semantic_page_details uses the cdp-cache backend"""
    semantic_page = sample_dossier_cdp.semantic_pages.first()
    semantic_page_page_object = semantic_page.semantic_page_page_objects.first()

    # First call should populate cache
    result1 = prepare_semantic_page_details(semantic_page_page_object)

    # Clear default cache but not cdp-cache
    caches["default"].clear()

    # Second call should get from cdp-cache
    result2 = prepare_semantic_page_details(semantic_page_page_object)

    # Verify both results are the same
    assert result1 == result2

    # Verify data exists in cdp-cache but not in default cache
    cache_key = f"semantic_page_details_{semantic_page_page_object.uuid}_{semantic_page_page_object.semantic_page.semantic_document.last_page_change_date}"
    assert caches["cdp-cache"].get(cache_key) is not None
    assert caches["default"].get(cache_key) is None


@pytest.mark.django_db
def test_cache_invalidation_on_semantic_document_update(sample_dossier_cdp):
    """Test that cache is invalidated when semantic document is updated"""
    semantic_document = sample_dossier_cdp.semantic_documents.first()
    semantic_page = semantic_document.semantic_pages.first()
    semantic_page_page_object = semantic_page.semantic_page_page_objects.first()

    # First call to populate cache
    fetch_semantic_pages_for_document(semantic_document)
    prepare_semantic_page_details(semantic_page_page_object)

    # Store initial cache keys
    pages_cache_key = f"semantic_pages_{semantic_document.uuid}_{semantic_document.last_page_change_date}"
    details_cache_key = f"semantic_page_details_{semantic_page_page_object.uuid}_{semantic_document.last_page_change_date}"

    # Verify cache is populated
    assert caches["cdp-cache"].get(pages_cache_key) is not None
    assert caches["cdp-cache"].get(details_cache_key) is not None

    # Update the semantic document (simulating a page move/delete)
    semantic_document.last_page_change_date = datetime.now()
    semantic_document.save()

    # Refresh the semantic page page object to get the updated document
    semantic_page_page_object.semantic_page.semantic_document.refresh_from_db()

    # New cache keys with updated last_page_change_date
    new_pages_cache_key = f"semantic_pages_{semantic_document.uuid}_{semantic_document.last_page_change_date}"
    new_details_cache_key = f"semantic_page_details_{semantic_page_page_object.uuid}_{semantic_document.last_page_change_date}"

    # Verify old cache entries are not used
    assert caches["cdp-cache"].get(new_pages_cache_key) is None
    assert caches["cdp-cache"].get(new_details_cache_key) is None

    # Fetch new results
    fetch_semantic_pages_for_document(semantic_document)
    prepare_semantic_page_details(semantic_page_page_object)

    # Verify new cache entries are created
    assert (
        caches["cdp-cache"].get(new_pages_cache_key) is not None
    ), f"Cache key {new_pages_cache_key} not found in cache"
    assert (
        caches["cdp-cache"].get(new_details_cache_key) is not None
    ), f"Cache key {new_details_cache_key} not found in cache"

    # Verify old cache entries are still present but not used
    assert caches["cdp-cache"].get(pages_cache_key) is not None
    assert caches["cdp-cache"].get(details_cache_key) is not None

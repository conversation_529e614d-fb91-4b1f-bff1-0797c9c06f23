from operator import indexOf

import pytest

from cdp.factories import (
    FieldDefinitionFactory,
    AssignedFieldFactory,
    DocumentCategoryFactory,
    RelevantPageObjectFactory,
    PageObjectTypeFactory,
)
from cdp.models import ReturnType
from cdp.schemas import (
    StringValue,
    Area,
    Volume,
    Currency,
    FloatValue,
    IntegerValue,
    BooleanValue,
    Date,
)
from cdp.services import group_sppos, get_recommended_sppos, sort_grouped_sppos
from dossier.factories import SemanticPagePageObjectFactory

pytestmark = pytest.mark.django_db


@pytest.fixture
def group_recommendation_data_area(return_type_keys):
    area_return_type, _ = ReturnType.objects.get_or_create(key="area")
    field_definition = FieldDefinitionFactory(return_type=area_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="123m2",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123sqm",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456ft2",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_area(
    group_recommendation_data_area,
):
    assigned_field, sppos = group_recommendation_data_area
    assert assigned_field.field_definition.return_type.key == "area"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 3
    sppos_with_area123 = grouped_recommendations.get(
        Area(value=123, return_type="area", unit="m2")
    )
    assert len(sppos_with_area123) == 2
    sppos_with_area_none = grouped_recommendations.get(
        Area(value=123, return_type="area", unit=None)
    )
    assert len(sppos_with_area_none) == 1
    sppos_with_area456 = grouped_recommendations.get(
        Area(value=456, return_type="area", unit="ft2")
    )
    assert len(sppos_with_area456) == 1


@pytest.fixture
def group_recommendation_data_volume(return_type_keys):
    volume_return_type, _ = ReturnType.objects.get_or_create(key="volume")
    field_definition = FieldDefinitionFactory(return_type=volume_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="123m3",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123 cubic meters",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456cubic feet",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_volume(
    group_recommendation_data_volume,
):
    assigned_field, sppos = group_recommendation_data_volume
    assert assigned_field.field_definition.return_type.key == "volume"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 3
    sppos_with_volume123 = grouped_recommendations.get(
        Volume(value=123, return_type="volume", unit="m3")
    )
    assert len(sppos_with_volume123) == 2
    sppos_with_volume_none = grouped_recommendations.get(
        Volume(value=123, return_type="volume", unit=None)
    )
    assert len(sppos_with_volume_none) == 1
    sppos_with_volume456 = grouped_recommendations.get(
        Volume(value=456, return_type="volume", unit="ft3")
    )
    assert len(sppos_with_volume456) == 1


@pytest.fixture
def group_recommendations_data_currency(return_type_keys):
    currency_return_type, _ = ReturnType.objects.get_or_create(key="currency")
    field_definition = FieldDefinitionFactory(return_type=currency_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="123 USD",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123 USD",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456 CHF",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456 CHF",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_currency(
    group_recommendations_data_currency,
):
    assigned_field, sppos = group_recommendations_data_currency
    assert assigned_field.field_definition.return_type.key == "currency"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 2
    sppos_with_currency123 = grouped_recommendations.get(
        Currency(value=123, return_type="currency", currency="USD")
    )
    assert len(sppos_with_currency123) == 2
    sppos_with_currency456 = grouped_recommendations.get(
        Currency(value=456, return_type="currency", currency="CHF")
    )
    assert len(sppos_with_currency456) == 2


@pytest.fixture
def group_recommendations_data_float(return_type_keys):
    float_return_type, _ = ReturnType.objects.get_or_create(key="float")
    field_definition = FieldDefinitionFactory(return_type=float_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="123.4",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123.4",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456.7",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456.7",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_float(
    group_recommendations_data_float,
):
    assigned_field, sppos = group_recommendations_data_float
    assert assigned_field.field_definition.return_type.key == "float"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 2
    sppos_with_float1234 = grouped_recommendations.get(
        FloatValue(value=123.4, return_type="float")
    )
    assert len(sppos_with_float1234) == 2
    sppos_with_float4567 = grouped_recommendations.get(
        FloatValue(value=456.7, return_type="float")
    )
    assert len(sppos_with_float4567) == 2


@pytest.fixture
def group_recommendations_data_integer(return_type_keys):
    integer_return_type, _ = ReturnType.objects.get_or_create(key="integer")
    field_definition = FieldDefinitionFactory(return_type=integer_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="456",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_integer(
    group_recommendations_data_integer,
):
    assigned_field, sppos = group_recommendations_data_integer
    assert assigned_field.field_definition.return_type.key == "integer"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 2
    sppos_with_integer123 = grouped_recommendations.get(
        IntegerValue(value=123, return_type="integer")
    )
    assert len(sppos_with_integer123) == 2
    sppos_with_integer456 = grouped_recommendations.get(
        IntegerValue(value=456, return_type="integer")
    )
    assert len(sppos_with_integer456) == 2


@pytest.fixture
def group_recommendations_data_boolean(return_type_keys):
    boolean_return_type, _ = ReturnType.objects.get_or_create(key="boolean")
    field_definition = FieldDefinitionFactory(return_type=boolean_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="true",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="yes",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="false",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="no",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_boolean(
    group_recommendations_data_boolean,
):
    assigned_field, sppos = group_recommendations_data_boolean
    assert assigned_field.field_definition.return_type.key == "boolean"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 2
    sppos_with_boolean_true = grouped_recommendations.get(
        BooleanValue(value=True, return_type="boolean")
    )
    assert len(sppos_with_boolean_true) == 2
    sppos_with_boolean_false = grouped_recommendations.get(
        BooleanValue(value=False, return_type="boolean")
    )
    assert len(sppos_with_boolean_false) == 2


@pytest.fixture
def group_recommendations_data_date(return_type_keys):
    date_return_type, _ = ReturnType.objects.get_or_create(key="date")
    field_definition = FieldDefinitionFactory(return_type=date_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="2020-01-01",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="01.01.2020",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="2020-02-01",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="01.02.2020",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_date(
    group_recommendations_data_date,
):
    assigned_field, sppos = group_recommendations_data_date
    assert assigned_field.field_definition.return_type.key == "date"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    assert len(grouped_recommendations) == 2
    sppos_with_date_20200101 = grouped_recommendations.get(
        Date(value="2020-01-01", return_type="date")
    )
    assert len(sppos_with_date_20200101) == 2
    sppos_with_date_20200102 = grouped_recommendations.get(
        Date(value="2020-02-01", return_type="date")
    )
    assert len(sppos_with_date_20200102) == 2


@pytest.fixture
def group_recommendation_data_string(return_type_keys):
    string_return_type, _ = ReturnType.objects.get_or_create(key="string")
    field_definition = FieldDefinitionFactory(return_type=string_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="value123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="value123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="value123",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="value456",
        ),
    ]
    return assigned_field, sppos


@pytest.mark.django_db
def test_grouping_recommendations_of_return_type_string(
    group_recommendation_data_string,
):
    assigned_field, sppos = group_recommendation_data_string
    assert assigned_field.field_definition.return_type.key == "string"
    assert len(sppos) == 4
    grouped_recommendations = group_sppos(sppos, assigned_field)
    print(grouped_recommendations.keys)
    assert len(grouped_recommendations) == 2
    sppos_with_value123 = grouped_recommendations.get(
        StringValue(value="value123", return_type="string")
    )
    assert len(sppos_with_value123) == 3
    sppos_with_value456 = grouped_recommendations.get(
        StringValue(value="value456", return_type="string")
    )
    assert len(sppos_with_value456) == 1


@pytest.fixture
def sorted_group_recommendation_data(return_type_keys):
    string_return_type, _ = ReturnType.objects.get_or_create(key="string")
    field_definition = FieldDefinitionFactory(return_type=string_return_type)
    assigned_field = AssignedFieldFactory(field_definition=field_definition)
    sppos = [
        SemanticPagePageObjectFactory(
            page_object__value="1",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="1",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="2",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="2",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="3",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="3",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="3",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="4",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="4",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="4",
        ),
        SemanticPagePageObjectFactory(
            page_object__value="4",
        ),
    ]
    for s in sppos:
        page_object = s.page_object
        page_object_type_cdp = PageObjectTypeFactory(key=page_object.key)
        rpo = RelevantPageObjectFactory(
            field_definition=field_definition,
            page_object=page_object_type_cdp,
        )
        doc_cat = DocumentCategoryFactory(key=s.semantic_page.document_category.name)
        rpo.generic_priority_mapping.create(
            document_category=doc_cat,
            priority=1,
        )

    sorted_sppos = get_recommended_sppos(sppos, assigned_field)
    unique_sppos_map = group_sppos(sppos, assigned_field)
    return sorted_sppos, unique_sppos_map


def test_grouped_sppos_after_sorting_are_sorted_within_the_group(
    sorted_group_recommendation_data,
):
    sorted_sppos, unique_sppos_map = sorted_group_recommendation_data
    for key, value in unique_sppos_map.items():
        for i in range(len(value) - 1):
            assert sorted_sppos.index(value[i]) < sorted_sppos.index(value[i + 1])


def test_grouped_sppos_after_sorting(sorted_group_recommendation_data):
    sorted_sppos, unique_sppos_map = sorted_group_recommendation_data
    sorted_grouped_values = sort_grouped_sppos(unique_sppos_map, sorted_sppos)
    assert len(sorted_grouped_values) == 4
    for i in range(len(sorted_grouped_values) - 1):
        assert indexOf(
            sorted_sppos, unique_sppos_map[sorted_grouped_values[i]][0]
        ) < indexOf(sorted_sppos, unique_sppos_map[sorted_grouped_values[i + 1]][0])

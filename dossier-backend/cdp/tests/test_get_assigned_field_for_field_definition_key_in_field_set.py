import pytest
from cdp.models import FieldSet, AssignedField, FieldDefinition
from cdp.services import get_assigned_field_for_field_definition_key_in_field_set


@pytest.mark.django_db
def test_assigned_field_for_existing_field_definition_key_in_field_set():
    field_set = FieldSet.objects.create(key="test_field_set")
    field_definition = FieldDefinition.objects.create(
        key="test_field_definition", flavour="hd"
    )
    AssignedField.objects.create(field_set=field_set, field_definition=field_definition)

    result = get_assigned_field_for_field_definition_key_in_field_set(
        "test_field_definition", "test_field_set"
    )
    assert result.field_definition.key == "test_field_definition"
    assert result.field_set.key == "test_field_set"


@pytest.mark.django_db
def test_assigned_field_for_non_existing_field_definition_key_in_field_set():
    FieldSet.objects.create(key="test_field_set")
    FieldDefinition.objects.create(key="test_field_definition")

    result = get_assigned_field_for_field_definition_key_in_field_set(
        "non_existing_key", "test_field_set"
    )
    assert result is False


@pytest.mark.django_db
def test_assigned_field_for_non_existing_field_set():
    FieldDefinition.objects.create(key="test_field_definition")

    result = get_assigned_field_for_field_definition_key_in_field_set(
        "test_field_definition", "non_existing_field_set"
    )
    assert result is False

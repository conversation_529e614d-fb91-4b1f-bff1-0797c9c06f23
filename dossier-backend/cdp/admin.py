from nested_admin.nested import (
    NestedTabularInline,
    NestedGenericTabularInline,
    NestedModelAdmin,
)
from django.contrib import admin
from django.contrib.admin import SimpleListFilter
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q
from django.shortcuts import redirect
from django.urls import reverse, path
from import_export.admin import ImportExportModelAdmin
from rangefilter.filters import DateRangeFilter

from cdp.models import (
    FieldSet,
    FieldDefinition,
    AssignedField,
    PageObjectType,
    RelevantPageObject,
    DocumentCategory,
    ReturnType,
    RelevantSemanticPage,
    RelevantSemanticDocument,
    GenericPriorityMapping,
    RelevantFieldContext,
)


class FieldDefinitionFilter(SimpleListFilter):
    title = "Field Definition"
    parameter_name = "field_definition"

    def lookups(self, request, model_admin):
        field_definitions = FieldDefinition.objects.all()
        return [(fd.uuid, str(fd)) for fd in field_definitions]

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        rel_pageobject_ct = ContentType.objects.get_for_model(RelevantPageObject)
        rel_pageobject_uuids = RelevantPageObject.objects.filter(
            field_definition__uuid=self.value()
        ).values_list("uuid", flat=True)

        rel_semanticpage_ct = ContentType.objects.get_for_model(RelevantSemanticPage)
        rel_semanticpage_uuids = RelevantSemanticPage.objects.filter(
            field_definition__uuid=self.value()
        ).values_list("uuid", flat=True)

        rel_semanticdoc_ct = ContentType.objects.get_for_model(RelevantSemanticDocument)
        rel_semanticdoc_uuids = RelevantSemanticDocument.objects.filter(
            field_definition__uuid=self.value()
        ).values_list("uuid", flat=True)

        return queryset.filter(
            Q(content_type=rel_pageobject_ct, object_id__in=rel_pageobject_uuids)
            | Q(content_type=rel_semanticpage_ct, object_id__in=rel_semanticpage_uuids)
            | Q(content_type=rel_semanticdoc_ct, object_id__in=rel_semanticdoc_uuids)
        )


@admin.register(GenericPriorityMapping)
class GenericPriorityMappingAdmin(admin.ModelAdmin):
    list_display = (
        "uuid",
        "relevant_object",
        "field_definition",
        "document_category",
        "priority",
        "created_at",
        "updated_at",
    )
    list_filter = [
        FieldDefinitionFilter,
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    readonly_fields = ["created_at", "updated_at"]
    search_fields = [
        "document_category__key",
    ]
    ordering = ["priority"]

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("content_type", "document_category")
        )

    def field_definition(self, obj):
        """Display the FieldDefinition associated with the relevant object."""
        return obj.field_definition

    field_definition.short_description = "Field Definition"


@admin.register(DocumentCategory)
class DocumentCategoryAdmin(ImportExportModelAdmin):
    # resource_classes = [DocumentCategory]
    search_fields = [
        "key",
    ]
    list_display = ("uuid", "key", "created_at", "updated_at")
    list_filter = [
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    readonly_fields = ["created_at", "updated_at"]


@admin.register(AssignedField)
class AssignedFieldAdmin(ImportExportModelAdmin):
    search_fields = ["field_definition__key"]
    raw_id_fields = ["field_set", "field_definition"]
    list_display = ("uuid", "field_set", "field_definition", "created_at", "updated_at")
    list_filter = [
        "field_set",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    readonly_fields = ["created_at", "updated_at"]


class GenericPriorityMappingInline(NestedGenericTabularInline):
    model = GenericPriorityMapping
    raw_id_fields = ["document_category"]
    extra = 1  # Allows the admin to add new priority mappings dynamically


@admin.register(RelevantPageObject)
class RelevantPageObjectAdmin(ImportExportModelAdmin):
    inlines = [GenericPriorityMappingInline]  # Add the inline here
    list_display = (
        "uuid",
        "field_definition",
        "page_object",
        "created_at",
        "updated_at",
    )
    raw_id_fields = ["field_definition", "page_object"]
    search_fields = [
        "field_definition__key",
        "field_definition__flavour",
        "page_object__key",
    ]
    list_filter = [
        "field_definition",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]
    ordering = ["field_definition", "page_object"]
    readonly_fields = ["created_at", "updated_at"]


@admin.register(RelevantSemanticPage)
class RelevantSemanticPageAdmin(admin.ModelAdmin):
    inlines = [GenericPriorityMappingInline]


@admin.register(RelevantSemanticDocument)
class RelevantSemanticDocumentAdmin(admin.ModelAdmin):
    inlines = [GenericPriorityMappingInline]


@admin.register(PageObjectType)
class PageObjectTypeAdmin(ImportExportModelAdmin):
    search_fields = [
        "key",
    ]
    ordering = ["key"]


class RelevantPageObjectInline(NestedTabularInline):
    model = RelevantPageObject
    raw_id_fields = ["page_object"]
    inlines = [GenericPriorityMappingInline]
    extra = 0


class RelevantSemanticPageInline(NestedTabularInline):
    model = RelevantSemanticPage
    extra = 0
    inlines = [GenericPriorityMappingInline]


class RelevantSemanticDocumentInline(NestedTabularInline):
    model = RelevantSemanticDocument
    extra = 0
    inlines = [GenericPriorityMappingInline]


class RelevantContextInline(NestedTabularInline):
    model = RelevantFieldContext
    fk_name = "field_definition"
    autocomplete_fields = ["relevant_field_definition"]
    extra = 1


@admin.register(FieldDefinition)
class FieldDefinitionAdmin(NestedModelAdmin):
    inlines = [
        RelevantPageObjectInline,
        RelevantSemanticPageInline,
        RelevantSemanticDocumentInline,
        RelevantContextInline,
    ]
    raw_id_fields = ["return_type"]
    list_display = ("uuid", "key", "flavour", "return_type", "created_at", "updated_at")
    search_fields = ["key", "flavour"]
    ordering = ["key"]
    list_filter = ["return_type"]
    actions = ["manage_priority_mappings"]
    readonly_fields = ["created_at", "updated_at"]
    list_filter = [
        "return_type",
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]

    def manage_priority_mappings(self, request, queryset):
        if queryset.count() == 1:
            field_def = queryset.first()
            return redirect(
                reverse("admin:cdp_fielddefinition_priority", args=[field_def.uuid])
            )
        else:
            self.message_user(
                request,
                "Please select only one Field Definition to manage priority mappings",
                level="ERROR",
            )

    manage_priority_mappings.short_description = "Manage Priority Mappings"

    def get_urls(self):
        """
        Add a custom URL for managing priority mappings for a FieldDefinition.
        /admin/cdp/fielddefinition/<uuid:field_definition_uuid>/priority/
        """

        urls = super().get_urls()
        custom = [
            path(
                "<uuid:field_definition_uuid>/priority/",
                self.admin_site.admin_view(self.priority_mappings_view),
                name="cdp_fielddefinition_priority",
            ),
        ]
        return custom + urls

    def priority_mappings_view(self, request, field_definition_uuid):
        field_definition = FieldDefinition.objects.get(uuid=field_definition_uuid)
        return redirect(
            reverse("admin:cdp_genericprioritymapping_changelist")
            + f"?field_definition={field_definition.uuid}"
        )


class AssignedFieldInline(admin.TabularInline):
    model = AssignedField
    raw_id_fields = ["field_definition"]
    extra = 0


@admin.register(FieldSet)
class FieldSetAdmin(ImportExportModelAdmin):
    inlines = [AssignedFieldInline]
    list_display = ("uuid", "key", "created_at", "updated_at")
    search_fields = ["key"]
    readonly_fields = ["created_at", "updated_at"]
    list_filter = [
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]


@admin.register(ReturnType)
class ReturnTypeAdmin(ImportExportModelAdmin):
    search_fields = ["key"]
    list_display = ("uuid", "key", "created_at", "updated_at")
    readonly_fields = ["created_at", "updated_at"]
    list_filter = [
        "created_at",
        "updated_at",
        ("created_at", DateRangeFilter),
        ("updated_at", DateRangeFilter),
    ]

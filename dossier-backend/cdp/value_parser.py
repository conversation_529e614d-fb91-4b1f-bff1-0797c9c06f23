import re
from abc import ABC, abstractmethod
from typing import Any, Literal

import structlog
from django.core.cache import cache

from cdp.models import As<PERSON><PERSON><PERSON>, ReturnType
from dateutil.parser import parse as dateutil_parse

from cdp.schemas import (
    Area,
    Volume,
    Currency,
    IntegerValue,
    FloatValue,
    StringValue,
    BooleanValue,
    Date,
    FieldValue,
    Street,
    ZipCode,
    City,
    StreetDetails,
)

logger = structlog.get_logger(__name__)
CACHE_KEY = "parser_strategy_map"
CACHE_TIMEOUT = 300  # Cache timeout in seconds (5 minutes)


class Parser(ABC):
    @abstractmethod
    def parse(self, value: str, *args: Any, **kwargs: Any) -> Any:
        pass


class AreaParser(Parser):
    # Unit mapping including 'm' -> 'm2'
    _UNIT_MAPPING = {
        "sqm": "m2",
        "square meters": "m2",
        "m2": "m2",
        "m": "m2",  # Added 'm' to map to 'm2'
        "sqft": "ft2",
        "square feet": "ft2",
        "ft2": "ft2",
    }

    def __init__(self, return_type: Literal["area"]):
        self.return_type = return_type

        # Build regex patterns for area values
        value_pattern_part = r"(?P<value>\d{1,3}(?:[']\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)"
        unit_pattern_part = "|".join(
            re.escape(key) for key in self._UNIT_MAPPING.keys()
        )
        unit_group_part = f"(?P<unit>{unit_pattern_part})"
        space_pattern_part = r"\s?"

        # Compile regex patterns
        self._area_with_unit_re = re.compile(
            rf"^{value_pattern_part}{space_pattern_part}{unit_group_part}$",
            re.IGNORECASE,
        )
        self._number_only_re = re.compile(rf"^{value_pattern_part}$")

    def parse(self, value: str, **kwargs) -> Area:
        if not isinstance(value, str):
            raise TypeError(
                f"Input must be a string, got {type(value)}"
            )  # Raise error for non-string input

        value = value.strip()

        try:
            # Try matching value with units (e.g., "2'399 m2", "80 m")
            match = self._area_with_unit_re.fullmatch(value)
            if match:
                raw_value = match.group("value")
                cleaned_value_str = raw_value.replace("'", "")
                matched_unit_lower = match.group("unit").lower()
                normalized_unit = self._UNIT_MAPPING.get(
                    matched_unit_lower, matched_unit_lower
                )
                return Area(
                    value=float(cleaned_value_str),
                    unit=normalized_unit,
                    return_type=self.return_type,
                )

            # Try matching just a number (e.g., "149", "1'234")
            match = self._number_only_re.fullmatch(value)
            if match:
                raw_value = match.group("value")
                cleaned_value_str = raw_value.replace("'", "")
                return Area(
                    value=float(cleaned_value_str),
                    unit=None,
                    return_type=self.return_type,
                )

            # If neither pattern matched, raise error
            raise ValueError(
                "Input does not match expected area format (e.g., '100 m2', '1'200 m2' or '149')"
            )

        except ValueError as ve:
            # Catch float conversion errors or the explicit raise above
            raise ValueError(f"Could not parse '{value}' as area: {ve}")
        except Exception as e:
            # Catch any other unexpected errors during parsing steps
            raise ValueError(
                f"Could not parse '{value}' as area due to an unexpected error: {type(e).__name__}"
            )

    @staticmethod
    def normalize_area_unit(unit: str) -> str:
        """
        Normalize different variations of area units to a common format.
        (Uses the class attribute for consistency)
        """
        unit_lower = unit.lower()
        # Return original unit (lowercased) if not found in mapping
        return AreaParser._UNIT_MAPPING.get(unit_lower, unit_lower)


class VolumeParser(Parser):
    # Volume unit mapping
    _UNIT_MAPPING = {
        "cbm": "m3",
        "cubic meters": "m3",
        "m3": "m3",
        "cft": "ft3",
        "cubic feet": "ft3",
        "ft3": "ft3",
    }

    def __init__(self, return_type: Literal["volume"]):
        self.return_type = return_type

        # Build regex patterns for volume values
        value_pattern_part = r"(?P<value>\d{1,3}(?:[']\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)"
        unit_pattern_part = "|".join(
            re.escape(key) for key in self._UNIT_MAPPING.keys()
        )
        unit_group_part = f"(?P<unit>{unit_pattern_part})"
        space_pattern_part = r"\s?"

        # Compile regex patterns
        self._volume_with_unit_re = re.compile(
            rf"^{value_pattern_part}{space_pattern_part}{unit_group_part}$",
            re.IGNORECASE,
        )
        self._number_only_re = re.compile(rf"^{value_pattern_part}$")

    def parse(self, value: str, **kwargs) -> Volume:
        if not isinstance(value, str):
            raise TypeError(
                f"Input must be a string, got {type(value)}"
            )  # Raise error for non-string input

        value = value.strip()

        try:
            # Try matching value with units (e.g., "50 cbm", "1'200 m3")
            match = self._volume_with_unit_re.fullmatch(value)
            if match:
                raw_value = match.group("value")
                cleaned_value_str = raw_value.replace("'", "")
                matched_unit_lower = match.group("unit").lower()
                normalized_unit = self._UNIT_MAPPING.get(
                    matched_unit_lower, matched_unit_lower
                )
                return Volume(
                    value=float(cleaned_value_str),
                    unit=normalized_unit,
                    return_type=self.return_type,
                )

            # Try matching just a number (e.g., "150", "1'234")
            match = self._number_only_re.fullmatch(value)
            if match:
                raw_value = match.group("value")
                cleaned_value_str = raw_value.replace("'", "")
                return Volume(
                    value=float(cleaned_value_str),
                    unit=None,
                    return_type=self.return_type,
                )

            # If neither pattern matched, raise error
            raise ValueError(
                "Input does not match expected volume format (e.g., '100 m3', '1'200 cbm' or '150')"
            )

        except ValueError as ve:
            # Catch float conversion errors or the explicit raise above
            raise ValueError(f"Could not parse '{value}' as volume: {ve}")
        except Exception as e:
            # Catch any other unexpected errors during parsing steps
            raise ValueError(
                f"Could not parse '{value}' as volume due to an unexpected error: {type(e).__name__}"
            )

    @staticmethod
    def normalize_volume_unit(unit: str) -> str:
        """
        Normalize different variations of volume units to a common format.
        (Uses the class attribute for consistency)
        """
        unit_lower = unit.lower()
        # Return original unit (lowercased) if not found in mapping
        return VolumeParser._UNIT_MAPPING.get(unit_lower, unit_lower)


class CurrencyParser(Parser):
    def __init__(self, return_type: Literal["currency"]):
        self.currency_patterns = [
            r"(?P<currency>EUR|USD|CHF)\s?(?P<value>[0-9'.,]+)",  # Currency before value
            r"(?P<value>[0-9'.,]+)\s?(?P<currency>EUR|USD|CHF)",  # Value before currency
        ]
        self.undefined_currency_pattern = r"(?P<value>[0-9'.,]+)"
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> Currency:
        try:
            value = value.strip()
            for pattern in self.currency_patterns:
                match = re.search(pattern, value)
                if match:
                    currency = match.group("currency")
                    numeric_value = CurrencyParser.parse_numeric_value(
                        match.group("value")
                    )
                    return Currency(
                        value=numeric_value,
                        currency=currency,
                        return_type=self.return_type,
                    )
            match = re.search(self.undefined_currency_pattern, value)
            if match:
                numeric_value = CurrencyParser.parse_numeric_value(match.group("value"))
                # Check if the *entire* string matched the number-only pattern
                # to avoid partially matching things like "100 EUR"
                if match.group(0) == value:
                    return Currency(
                        value=numeric_value, currency=None, return_type=self.return_type
                    )

            # If no pattern resulted in a return, raise an error
            raise ValueError(
                f"Input '{value}' does not match any expected currency format."
            )

        except ValueError as e:
            # Catch explicit ValueErrors from parse_numeric_value or the raise above
            raise ValueError(f"Could not parse '{value}' as currency: {e}")
        except Exception as e:
            # Catch any other unexpected errors
            raise ValueError(
                f"Could not parse '{value}' as currency due to an unexpected error: {type(e).__name__}"
            )

    @staticmethod
    def parse_numeric_value(value: str) -> float:
        """Parse numeric string with optional thousands separators."""
        cleaned_value = value.replace("'", "").replace(",", "")
        try:
            return float(cleaned_value)
        except ValueError as e:
            raise ValueError(f"Could not parse {value} as float: {e}")


class IntegerParser(Parser):
    def __init__(self, return_type: Literal["integer"]):
        # Regex to capture an integer, ignoring surrounding non-digits and allowing ',' or ''' as separators
        self.integer_pattern = r"(?:[^\d,']*)(\d{1,3}(?:[,']\d{3})+|\d+)(?:[^\d]*)"
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> IntegerValue:
        try:
            value = value.strip()
            match = re.search(self.integer_pattern, value)
            if match:
                # Extract the matched number string
                raw_value = match.group(1)
                # Remove separators before converting to int
                cleaned_value_str = raw_value.replace("'", "").replace(",", "")
                return IntegerValue(
                    value=int(cleaned_value_str), return_type=self.return_type
                )
            # If no match, try a simpler extraction (handles cases like '123xyz')
            simple_match = re.search(r"\d+", value)
            if simple_match:
                return IntegerValue(
                    value=int(simple_match.group(0)), return_type=self.return_type
                )

        except Exception as e:
            raise ValueError(f"Could not parse {value} as integer: {e}")
        # Raise error if no integer pattern was found
        raise ValueError(f"Could not find an integer pattern in {value}")


class FloatParser(Parser):
    def __init__(self, return_type: Literal["float"]):
        # Regex to capture a float, allowing ',' or '\'' separators and optional decimal part
        self.float_pattern = (
            r"(?:[^\d,'.]*)(\d{1,3}(?:[,']\d{3})*(?:\.\d+)?|\d+(?:\.\d+)?)(?:[^\d.]*)"
        )
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> FloatValue:
        try:
            value = value.strip()
            match = re.search(self.float_pattern, value)
            if match:
                # Extract the matched number string
                raw_value = match.group(1)
                # Remove separators before converting to float
                cleaned_value_str = raw_value.replace("'", "").replace(",", "")
                return FloatValue(
                    value=float(cleaned_value_str), return_type=self.return_type
                )
            # If no match, try a simpler extraction (handles cases like '123.45xyz')
            simple_match = re.search(r"\d+(?:\.\d+)?", value)
            if simple_match:
                return FloatValue(
                    value=float(simple_match.group(0)), return_type=self.return_type
                )

        except Exception as e:
            raise ValueError(f"Could not parse {value} as float: {e}")
        # Raise error if no float pattern was found
        raise ValueError(f"Could not find a float pattern in {value}")


class BooleanParser(Parser):
    def __init__(self, return_type: Literal["boolean"]):
        self.return_type = return_type

    def parse(self, value, **kwargs) -> BooleanValue:
        try:
            if isinstance(value, str):
                value_lower = value.strip().lower()
                if value_lower in ["true", "yes", "1"]:
                    return BooleanValue(value=True, return_type=self.return_type)
                elif value_lower in ["false", "no", "0"]:
                    return BooleanValue(value=False, return_type=self.return_type)
            return BooleanValue(value=bool(value), return_type=self.return_type)
        except Exception as e:
            raise ValueError(f"Could not parse {value} as boolean: {e}")


class DateParser(Parser):
    def __init__(self, return_type: Literal["date"]):
        self.return_type = return_type
        self.date_patterns_dayfirst = {
            re.compile(r"^\d{4}[\./-]\d{2}[\./-]\d{2}$"): False,  # YYYY-MM-DD
            re.compile(r"^\d{2}[\./-]\d{2}[\./-]\d{4}$"): True,  # DD-MM-YYYY
        }

    def parse(self, value, **kwargs) -> Date:
        if not isinstance(value, str):
            raise TypeError(f"Input must be a string, got {type(value)}")
        value = value.strip()

        for pattern_re, day_first in self.date_patterns_dayfirst.items():
            try:
                if pattern_re.match(value):
                    parsed_date = dateutil_parse(value, dayfirst=day_first)
                    return Date(
                        value=parsed_date.date().isoformat(),
                        return_type=self.return_type,
                    )
            except Exception:
                logger.debug(
                    f"dateutil failed for pattern {pattern_re.pattern}", exc_info=True
                )

        raise ValueError(f"Could not parse '{value}' as date with known formats")


class StringParser(Parser):
    def __init__(self, return_type: Literal["string"]):
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> StringValue:
        return StringValue(value=value, return_type=self.return_type)


class StreetParser(Parser):
    """Parser for street addresses with optional street numbers and suffixes."""

    def __init__(self, return_type: Literal["street"]):
        self.abbr_map = {
            # German abbreviations
            "Str.": "Strasse",
            "str.": "strasse",
            "Hptstr.": "Hauptstrasse",
            "H.": "Hauptstrasse",
            "G.": "Gasse",
            "Pl.": "Platz",
            "Br.": "Brücke",
            "weg": "weg",
            # French abbreviations
            "Ch.": "Chemin",
            "Av.": "Avenue",
            "Rte.": "Route",
            "Rte": "Route",
            "All.": "Allée",
            "Pass.": "Passage",
            "Bd.": "Boulevard",
            "R": "Rue",
            # Italian abbreviations
            "V.": "Via",
            "V.le": "Viale",
            "P.zza": "Piazza",
            "C.so": "Corso",
            # English abbreviations
            "St.": "Street",
            "Pk.": "Park",
            "Dr.": "Drive",
            "Ct.": "Court",
            "Ln.": "Lane",
            "Rd.": "Road",
        }

        self.common_street_words = (
            r"(" + "|".join(map(re.escape, self.abbr_map.values())) + r")"
        )
        self.page_object_value = None
        self.street_pattern = r"(?P<street_name>[\w\säöüÄÖÜß\-'\.]+?)\s+(?P<street_number>\d{1,3})\s*(?P<suffix>[a-zA-Z]?)\b"
        self.street_word_pattern = rf"\b\w*{self.common_street_words}\w*\b"
        self.return_type = return_type
        self.sorted_abbrs = sorted(self.abbr_map.keys(), key=len, reverse=True)

    def preprocess_address(self, address: str) -> str:
        """Expand street name abbreviations in the address."""
        for abbr in self.sorted_abbrs:
            escaped_abbr = re.escape(abbr)
            if abbr == "R":
                address = re.sub(
                    rf"\b{escaped_abbr}(?=\s|\.|,)", self.abbr_map[abbr], address
                )
            else:
                pattern = rf"{escaped_abbr}(?=\s|,|$)"
                replacement = self.abbr_map[abbr]
                address = re.sub(pattern, replacement, address)
        return address

    def parse(self, value: str, **kwargs) -> Street:
        self.page_object_value = value
        value = self.preprocess_address(value)
        return self.parse_address_block(value)

    def parse_street_with_number(self, value: str) -> Street:
        try:
            value = value.strip()
            match = re.search(self.street_pattern, value, re.IGNORECASE)
            if match:
                street_name = match.group("street_name")
                street_number = match.group("street_number")
                suffix = match.group("suffix") if match.group("suffix") else None
                return Street(
                    value=StreetDetails(
                        street_name=street_name,
                        street_number=int(street_number),
                        suffix=suffix,
                        page_object_value=self.page_object_value,
                    ),
                    return_type=self.return_type,
                )
            else:
                return Street(
                    value=StreetDetails(
                        street_name=value,
                        street_number=None,
                        suffix=None,
                        page_object_value=self.page_object_value,
                    ),
                    return_type=self.return_type,
                )

        except Exception as e:
            raise ValueError(f"Could not parse {value} as street: {e}")

    def parse_address_block(self, value: str) -> Street:
        lines = re.split(r"\r\n|\r|\n|,", value)
        street_line = None
        for line in lines:
            line = self.preprocess_address(line)
            if re.search(self.street_word_pattern, line, re.IGNORECASE):
                street_line = line
                break
            if re.search(self.street_pattern, line, re.IGNORECASE):
                street_line = line
                break
        if street_line:
            return self.parse_street_with_number(street_line)
        else:
            raise ValueError(f"Could not parse {value} as street")


class ZipCodeParser(Parser):
    """Parser for zip codes in address blocks."""

    def __init__(self, return_type: Literal["zip_code"]):
        self.zip_pattern = r"\b(?P<zip_code>\d{4,5})\b"
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> ZipCode | None:
        lines = re.split(r"\r\n|\r|\n|,", value)
        zip_line = None
        for line in lines:
            if re.search(self.zip_pattern, line):
                zip_line = line
                break
        if zip_line:
            return self.parse_zip_code(zip_line)
        return None

    def parse_zip_code(self, value: str) -> ZipCode | None:
        try:
            match = re.search(self.zip_pattern, value)
            if match:
                if len(match.group("zip_code")) not in [4, 5]:
                    raise ValueError(f"Zip code should be 4 or 5 digits: {value}")
                return ZipCode(
                    value=int(match.group("zip_code")), return_type=self.return_type
                )
        except Exception:
            return None


class CityParser(Parser):
    """Parser for city names in address blocks."""

    def __init__(self, return_type: Literal["city"]):
        self.return_type = return_type
        self.city_pattern = r"(?P<zip_code_before>\b\d{4,5})\s*[,\s]?\s*(?P<city_name_after>[A-Za-zÀ-ÖØ-öø-ÿ\s\-/\.]+)$"

    def parse(self, value: str, **kwargs) -> City | None:
        lines = re.split(r"\r\n|\r|\n|,", value)
        city_line = None

        for line in lines:
            if re.search(self.city_pattern, line):
                city_line = line.strip()
                break

        if city_line:
            return self.parse_city_name(city_line)
        return None

    def parse_city_name(self, value: str) -> City | None:
        value = value.strip()
        try:
            match = re.search(self.city_pattern, value)
            if match:
                city_name = match.group("city_name_after").strip()
                return City(value=city_name, return_type=self.return_type)
        except Exception:
            logger.warning(f"Could not parse {value} as city")
            raise ValueError(f"Could not parse {value} as city")


class ValueRatioParser(Parser):
    """Parser for ratio values where denominator is 1000."""

    def __init__(self, return_type: Literal["value_ratio"]):
        self.ratio_pattern = r"^(?P<numerator>\d+)/1000[^0-9]*$"
        self.return_type = return_type

    def parse(self, value: str, **kwargs) -> IntegerValue:
        try:
            value = value.strip()
            match = re.match(self.ratio_pattern, value)
            if match:
                numerator = int(match.group("numerator"))
                return IntegerValue(value=numerator, return_type="integer")
            raise ValueError(
                f"Value {value} does not match expected format of 'number/1000'"
            )
        except Exception as e:
            raise ValueError(f"Could not parse {value} as value_ratio: {e}")


def get_parser_strategy_map(
    cache_key: str = "parser_strategy_map",
    cache_timeout: int = 300,
    use_cache: bool = True,
    refresh_cache: bool = False,
) -> dict:
    if use_cache:
        parser_strategy_map = cache.get(cache_key)
        if refresh_cache:
            parser_strategy_map = None
        if parser_strategy_map is None:
            # If not in cache, build the parser strategy map
            parser_strategy_map = build_parser_strategy_map()

            # Store the parser strategy map in the cache
            cache.set(cache_key, parser_strategy_map, cache_timeout)
        return parser_strategy_map
    else:
        return build_parser_strategy_map()


def snake_to_pascal_case(input_str: str) -> str:
    return "".join(word.capitalize() for word in input_str.split("_"))


def build_parser_strategy_map():
    return_type_keys = ReturnType.objects.values_list("key", flat=True)
    parser_strategy_map = {}
    for key in return_type_keys:
        parser_class_name = (
            f"{snake_to_pascal_case(key)}Parser"  # e.g. "area" -> "AreaParser"
        )
        parser_class = globals().get(parser_class_name)

        if parser_class:
            parser_strategy_map[key] = parser_class
        else:
            logger.warning(f"No parser class found for ReturnType: {key}")
    return parser_strategy_map


def get_parser(
    return_type: str, use_cache: bool = True, refresh_cache: bool = False
) -> Parser:
    parser_strategy_map = get_parser_strategy_map(
        cache_key=CACHE_KEY,
        cache_timeout=CACHE_TIMEOUT,
        use_cache=use_cache,
        refresh_cache=refresh_cache,
    )
    parser_return_type = parser_strategy_map.get(return_type)
    if parser_return_type:
        return parser_return_type(return_type=return_type)

    logger.warning(
        f"No parser found for return_type: {return_type} - returning StringParser"
    )
    return StringParser(return_type="string")


def parse_value(
    value: str,
    assigned_field: AssignedField,
    use_cached_parser: bool = True,
    refresh_cache: bool = False,
    **kwargs,
) -> FieldValue | None:
    """Parse a value based on the assigned field's return type.

    Uses the appropriate parser strategy based on the return_type key.

    Args:
        value: The raw string value to parse.
        assigned_field: The AssignedField instance containing field definition and return type.
        use_cached_parser: Whether to use the cached parser strategy map.
        refresh_cache: Force refresh of the parser strategy map cache.
        **kwargs: Additional arguments passed to the specific parser's parse method.

    Returns:
        A FieldValue schema object (e.g., Area, Volume, StringValue) if parsing is successful.
        Returns None if parsing fails for Area, Volume, ZipCode, or City types,
        as returning the original malformed string is often undesirable.
        Returns StringValue containing the original input if other parsers raise an exception.
    """
    return_type = (
        assigned_field.field_definition.return_type.key
        if assigned_field.field_definition.return_type
        else "string"
    )
    parser = get_parser(return_type, use_cached_parser, refresh_cache)

    try:
        # Attempt to parse the value using the determined parser
        return parser.parse(value, **kwargs)

    except Exception as e:
        # Check if the parser is one that should return None on failure
        if isinstance(parser, (AreaParser, VolumeParser, ZipCodeParser, CityParser)):
            # Log debug message as returning None is expected for these types on failure
            logger.debug(
                f"Parsing failed for '{value}' with {type(parser).__name__}, returning None. Error: {e}",
                exc_info=True,  # Include traceback in debug log
            )
            return None
        else:
            # For other parsers, log a warning and return the original value as StringValue
            logger.warning(
                f"Returning value without parsing, error parsing value={value} for return_type={return_type} : {e}"
            )
            return StringValue(value=value, return_type="string")

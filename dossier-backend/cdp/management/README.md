### Behaviour of Field Configuration

- We receive responses only where `GenericPriorityMapping` is defined for a given `(RelevantObject , DocumentCategory)` pair.
- `RelevantObject` can be of types 
  - `RelevantPageObject`: Page object key of interest 
  - `RelevantSemanticDocument` : Semantic Document of the specified document category (hint)
  - `RelevantSemanticPage` : Semantic Page of the specified document category (hint)

NOTE: Currently, we scroll to the first page of the RelevantSemanticDocument as it used as a hint. 
We will work on scrolling to the page of interest in the document, based on which `field_definition` we are fetching hints for.
### Fields config for field context 

#### Personal Data Fields : 

```
Nachname
hdf_person_lastname

Vorname
hdf_person_firstname
```
For `hdf_person_firstname`: We use `hdf_person_lastname` ; 
For `hdf_person_lastname`: We use `hdf_person_firstname` ;

---

These fields use both `hdf_person_firstname` and `hdf_person_lastname` fields.
**Relevant Field Context :** 

```
hdf_person_firstname
hdf_person_lastname
```

**Used in:**
```

Strasse
hdf_person_street_with_number

PLZ
hdf_person_zip

Ort
hdf_person_city

Geburtsdatum
hdf_person_date_of_birth

AHV
hdf_person_ahv_n13

Betreibungen
hdf_person_debt_enforcement_record_status

Nettoeinkommen
hdf_person_income_net

Bonus
hdf_person_income_bonus	

Weitere Regelmässige Einkommen
hdf_person_income_additional_regular

Alimenten einkunfte
hdf_person_income_alimony_total

Alimenten ausgaben
hdf_person_expense_alimony_total

Dividenden
hdf_person_income_dividends

Leasing
hdf_person_expense_leasing

Aktuelles Vorsorgeguthaben PK
hdf_person_pension2_assets_current

Altersguthaben bei Pensionierung
hdf_person_pension2_assets_projected_retirement
```


### Changes to value parser 
The default behaniour of the value parser is such that :

- Send a parsed Currency / Area/ Volume/ Street/ (any other valid return_type) field where we can parse the input based on the regex rules 
- If the input cannot be parsed / any error is raised, we repond back with the String return with the original input value 

For `return_type : ['city', 'zip']` : have adapted the value parser to return None if the input `page_object` value cannot be parsed. 
This has been done to reduce the responses where we would respond back with the default String response for malformed/ blacked out address block fileds in some domo documents. 
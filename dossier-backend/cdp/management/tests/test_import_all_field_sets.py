import json
from pathlib import Path
from django.utils import timezone

import pytest
from django.core.management import call_command

from cdp.models import (
    FieldSet,
    FieldDefinition,
    AssignedField,
    PageObjectType,
    RelevantPageObject,
    DocumentCategory,
    GenericPriorityMapping,
    ReturnType,
)


@pytest.fixture
def json_file_path(tmp_path):
    """
    Creates a temporary JSON file with minimal data for one FieldSet,
    one FieldDefinition, one PageObjectType, one DocumentCategory, and
    a single GenericPriorityMapping with priority=1.
    """
    json_data = {
        "key": "test_field_set",
        "assigned_fields": [
            {
                "field_definition_key": "test_field_def",
                "field_definition_flavour": "test_flavour",
                "field_definition_return_type": "test_return_type",
                "relevant_page_objects": [
                    {
                        "page_object_key": "test_page_object",
                        "priority_mappings": [
                            {
                                "document_category_key": "test_doc_category",
                                "priority": 1,
                            }
                        ],
                    }
                ],
            }
        ],
    }
    file_path = tmp_path / "test_field_set_data.json"
    with open(file_path, "w") as f:
        json.dump([json_data], f)
    return file_path


@pytest.mark.django_db
def test_imports_field_set_data_correctly(json_file_path):
    """
    Ensure the command 'import_all_field_sets' imports minimal data
    and creates a GenericPriorityMapping with priority=1.
    """
    call_command("import_all_field_sets", "--file", str(json_file_path))

    assert FieldSet.objects.filter(key="test_field_set").exists()
    assert FieldDefinition.objects.filter(
        key="test_field_def", flavour="test_flavour"
    ).exists()
    assert ReturnType.objects.filter(key="test_return_type").exists()
    assert PageObjectType.objects.filter(key="test_page_object").exists()
    assert DocumentCategory.objects.filter(key="test_doc_category").exists()

    # Check that at least one GenericPriorityMapping with priority=1 was created
    assert GenericPriorityMapping.objects.filter(priority=1).exists()


@pytest.mark.django_db
def test_warns_dangling_field_definitions(json_file_path):
    """
    Verify that existing FieldDefinition records not in the new JSON
    remain in the DB when the 'delete-dangling-field-definitions' flag is not used.
    """
    FieldDefinition.objects.create(key="dangling_field_def", flavour="dangling_flavour")
    call_command("import_all_field_sets", "--file", str(json_file_path))
    # The dangling definition should still exist
    assert FieldDefinition.objects.filter(
        key="dangling_field_def", flavour="dangling_flavour"
    ).exists()


@pytest.mark.django_db
def test_deletes_dangling_field_definitions_with_flag(json_file_path):
    """
    If the '--delete-dangling-field-definitions' flag is used,
    definitions not in the JSON should be removed.
    """
    FieldDefinition.objects.create(key="dangling_field_def", flavour="dangling_flavour")
    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
        "--delete-dangling-field-definitions",
    )
    # The dangling definition should now be removed
    assert not FieldDefinition.objects.filter(
        key="dangling_field_def", flavour="dangling_flavour"
    ).exists()


@pytest.mark.django_db
def test_import_field_set_data(mocker, tmp_path):
    """
    Test the Django management command to ensure that FieldSet data
    is correctly imported from a JSON file with multiple definitions.
    """
    # Create test JSON data
    test_data = [
        {
            "key": "test_field_set",
            "assigned_fields": [
                # distinct field_definition 1
                {
                    "field_definition_key": "test_field_definition1",
                    "field_definition_flavour": "test_flavour1",
                    "field_definition_return_type": "return_type_key1",
                    "relevant_page_objects": [
                        {
                            "page_object_key": "test_page_object_key1",
                            "priority_mappings": [
                                {
                                    "document_category_key": "test_document_category1",
                                    "priority": 1,
                                }
                            ],
                        }
                    ],
                },
                # distinct field_definition 2
                {
                    "field_definition_key": "test_field_definition2",
                    "field_definition_flavour": "test_flavour1",
                    "field_definition_return_type": "return_type_key2",
                    "relevant_page_objects": [
                        {
                            "page_object_key": "test_page_object_key2",
                            "priority_mappings": [
                                {
                                    "document_category_key": "test_document_category2",
                                    "priority": 1,
                                }
                            ],
                        }
                    ],
                },
                # field_definition 3 : same return_type/page_object_key as #1
                {
                    "field_definition_key": "test_field_definition3",
                    "field_definition_flavour": "test_flavour1",
                    "field_definition_return_type": "return_type_key1",
                    "relevant_page_objects": [
                        {
                            "page_object_key": "test_page_object_key1",
                            "priority_mappings": [
                                {
                                    "document_category_key": "test_document_category3",
                                    "priority": 1,
                                }
                            ],
                        }
                    ],
                },
                # field_definition 4 : same key as #1 but different flavour => new definition
                {
                    "field_definition_key": "test_field_definition1",
                    "field_definition_flavour": "test_flavour2",
                    "field_definition_return_type": None,
                    "relevant_page_objects": [
                        {
                            "page_object_key": "test_page_object_key1",
                            "priority_mappings": [
                                {
                                    "document_category_key": "test_document_category2",
                                    "priority": 1,
                                }
                            ],
                        }
                    ],
                },
            ],
        }
    ]

    temp_dir = tmp_path.parent.parent / "assets" / "field_set_data"
    temp_dir.mkdir(parents=True, exist_ok=True)
    timestamp = timezone.now().strftime("%Y%m%d")
    temp_file = temp_dir / f"field_set_export_{timestamp}.json"

    with open(temp_file, "w") as f:
        json.dump(test_data, f)

    # Patch the Path used by the command so it picks up our temp_dir
    mocker.patch(
        "cdp.management.commands.import_all_field_sets.Path",
        return_value=Path(temp_dir),
    )

    call_command("import_all_field_sets")

    field_set = FieldSet.objects.get(key="test_field_set")
    assert field_set is not None

    # Confirm creation of assigned fields, field definitions, etc.
    assert AssignedField.objects.filter(field_set=field_set).count() == 4
    assert FieldDefinition.objects.count() == 4
    assert PageObjectType.objects.count() == 2
    assert RelevantPageObject.objects.count() == 4
    assert ReturnType.objects.count() == 2
    assert DocumentCategory.objects.count() == 3

    # Now checking the number of GenericPriorityMapping entries
    # instead of PriorityMapping.
    assert GenericPriorityMapping.objects.count() == 4


@pytest.mark.django_db
def test_import_when_priority_mapping_in_db_but_not_in_json(json_file_path):
    """
    Test that the import command deletes GenericPriorityMapping
    entries that are in the DB but not in the JSON file.
    """
    # Import the JSON file to create the initial GenericPriorityMapping
    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
    )

    # create an additional GenericPriorityMapping entry that is not present in the JSON file
    rpo = RelevantPageObject.objects.first()
    doc_cat, _ = DocumentCategory.objects.get_or_create(key="test_doc_category2")
    print(DocumentCategory.objects.all())
    rpo.generic_priority_mapping.create(priority=2, document_category=doc_cat)

    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
        "--delete-dangling-priority-mappings",
    )
    # # The GenericPriorityMapping entry should not exist
    assert GenericPriorityMapping.objects.filter(priority=1).exists()
    assert GenericPriorityMapping.objects.count() == 1
    assert not GenericPriorityMapping.objects.filter(priority=2).exists()


@pytest.mark.django_db
def test_import_when_priority_mapping_in_db_but_not_in_json_flag_disabled(
    json_file_path,
):
    """
    Test that the import command does not delete GenericPriorityMapping
    entries that are in the DB but not in the JSON file.
    """
    # Import the JSON file to create the initial GenericPriorityMapping
    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
    )

    # create an additional GenericPriorityMapping entry that is not present in the JSON file
    rpo = RelevantPageObject.objects.first()
    doc_cat, _ = DocumentCategory.objects.get_or_create(key="test_doc_category2")
    print(DocumentCategory.objects.all())
    rpo.generic_priority_mapping.create(priority=2, document_category=doc_cat)

    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
        "--no-delete-dangling-priority-mappings",
    )
    #
    # # The GenericPriorityMapping entry should still exist
    assert GenericPriorityMapping.objects.filter(priority=1).exists()
    assert GenericPriorityMapping.objects.filter(priority=2).exists()
    assert GenericPriorityMapping.objects.count() == 2


@pytest.mark.django_db
def test_truncate_and_import_when_priority_mapping_in_db_but_not_in_json(
    json_file_path,
):
    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
    )

    rpo = RelevantPageObject.objects.first()
    doc_cat, _ = DocumentCategory.objects.get_or_create(key="test_doc_category2")
    print(DocumentCategory.objects.all())
    rpo.generic_priority_mapping.create(priority=2, document_category=doc_cat)
    call_command("truncate_cdp")
    call_command(
        "import_all_field_sets",
        "--file",
        str(json_file_path),
    )
    # The GenericPriorityMapping entry should not exist
    assert GenericPriorityMapping.objects.filter(priority=1).exists()
    assert GenericPriorityMapping.objects.count() == 1
    assert not GenericPriorityMapping.objects.filter(priority=2).exists()

import json
import pytest
from pathlib import Path
from django.core.management import call_command
from cdp.models import DocumentCategory


@pytest.mark.django_db
def test_import_document_categories(mocker, tmp_path):
    """
    Test the Django management command to ensure that DocumentCategory objects
    are correctly created from a provided JSON file.
    """
    # Create test JSON data
    test_data = [
        {"name": "category_1"},
        {"name": "category_2"},
        {"name": "category_3"},
    ]

    # Create a temporary directory and file to simulate the JSON file
    temp_dir = tmp_path / "document_categories"
    temp_dir.mkdir(parents=True, exist_ok=True)
    temp_file = temp_dir / "test_document_categories.json"

    # Write the test data to the JSON file
    with open(temp_file, "w") as f:
        json.dump(test_data, f)

    call_command("import_cdp_document_categories", "--file", str(temp_file))

    # Verify that the DocumentCategory objects have been created
    assert DocumentCategory.objects.filter(key="category_1").exists()
    assert DocumentCategory.objects.filter(key="category_2").exists()
    assert DocumentCategory.objects.filter(key="category_3").exists()

    # Verify that the correct number of objects have been created
    assert DocumentCategory.objects.count() == 3


@pytest.mark.django_db
def test_import_document_categories_default_file(mocker, tmp_path):
    """
    Test the command when no file is provided, ensuring that it defaults to the latest JSON file.
    """
    temp_dir = tmp_path.parent / "assets" / "document_category" / "default"
    temp_dir.mkdir(parents=True, exist_ok=True)

    old_file = temp_dir / "DocumentCategory-2023-01-01.json"
    new_file = temp_dir / "DocumentCategory-2024-01-01.json"

    old_data = [{"name": "old_key"}]
    new_data = [{"name": "new_key"}]

    with open(old_file, "w") as f:
        json.dump(old_data, f)

    with open(new_file, "w") as f:
        json.dump(new_data, f)

    mocker.patch(
        "cdp.management.commands.import_cdp_document_categories.get_doc_cat_config_file_path",
        return_value=Path(temp_dir),
    )
    call_command("import_cdp_document_categories")
    print(DocumentCategory.objects.all())
    # assert DocumentCategory.objects.filter(key="new_key").exists()
    # assert DocumentCategory.objects.count() == 1


@pytest.mark.django_db
def test_import_document_categories_invalid_json(mocker, tmp_path):
    temp_dir = tmp_path / "document_categories"
    temp_dir.mkdir(parents=True, exist_ok=True)
    temp_file = temp_dir / "invalid_document_categories.json"

    with open(temp_file, "w") as f:
        f.write("{Invalid JSON Content}")

    # Expecting the management command to handle the error and not raise CommandError
    call_command("import_cdp_document_categories", "--file", str(temp_file))

    # Ensure that no objects were created in the database
    assert DocumentCategory.objects.count() == 0


@pytest.mark.django_db
def test_import_document_categories_no_file_found(mocker, tmp_path):
    """
    Test the command when no JSON files are found in the directory.
    """
    temp_dir = tmp_path.parent / "assets" / "document_category" / "empty_default_dir"
    temp_dir.mkdir(parents=True, exist_ok=True)

    mocker.patch(
        "cdp.management.commands.import_cdp_document_categories.get_doc_cat_config_file_path",
        return_value=Path(temp_dir),
    )

    call_command("import_cdp_document_categories")

    assert DocumentCategory.objects.count() == 0

import json
import re
from datetime import datetime, date
from pathlib import Path
from typing import List

import djclick as click

from assets import ASSETS_PATH
from cdp.models import DocumentCategory


def extract_date_from_filename(path: Path) -> date:
    date_pattern = re.compile(r"(\d{4}-\d{2}-\d{2})")
    match = date_pattern.search(path.name)
    if match:
        return datetime.strptime(match.group(1), "%Y-%m-%d").date()
    return datetime.min.date()


# Sort files by timestamp in the name (newest first)
def get_latest_json(path_list: List[Path]) -> Path:
    path_list.sort(key=extract_date_from_filename, reverse=True)
    return path_list[0]


def get_doc_cat_config_file_path():
    return ASSETS_PATH / "document_category" / "default"


## The JSON files in dossier-backend/assets/document_category are obtained from the classifier project which serves as the source of truth for document categories.
## Assumption / Restriction: working with the default document categories (no specific doc cats / mapping for an account are considered)


@click.command()
@click.option(
    "--file",
    type=click.Path(exists=True, readable=True),
    help="The path to the JSON file to import. If not provided, the latest file in the dossier-backend/assets/document_category/default folder will be used.",
)
def command(file):
    """
    Import Document Categories from a JSON file, defaulting to the latest file in the dossier-backend/assets/document_category/default folder.
    """
    doc_cat_list = []
    document_categories_path = get_doc_cat_config_file_path()
    file_path = file
    click.secho(
        f"Importing document categories from {document_categories_path}", fg="green"
    )

    if not file_path:
        # Get the latest JSON file from the directory
        json_files = list(document_categories_path.glob("DocumentCategory-*.json"))
        if not json_files:
            click.secho(
                "No JSON files found in the document_categories directory.", fg="red"
            )
            return
        file_path = get_latest_json(json_files)

    try:
        with open(file_path) as json_file:
            data = json.load(json_file)
    except FileNotFoundError:
        click.secho(f"File {file_path} not found", fg="red")
        return
    except json.JSONDecodeError:
        click.secho("Invalid JSON format", fg="red")
        return

    for category_data in data:
        document_category_key = category_data.get(
            "name"
        )  # name is the key used for the DocumentCategory in the JSON file
        # Get or create the DocumentCategory
        document_category, created = DocumentCategory.objects.get_or_create(
            key=document_category_key
        )
        doc_cat_list.append(document_category_key)
        if not created:
            click.secho(
                f"DocumentCategory already exists: {document_category_key}, skipping creation",
                fg="yellow",
            )

    click.secho(
        f"{len(doc_cat_list)} Document categories imported successfully", fg="green"
    )
    click.echo(f"{doc_cat_list}")
    click.secho(f"Imported document categories from {file_path}", fg="green")

import djclick as click
from cdp.models import (
    GenericPriorityMapping,
    RelevantSemanticDocument,
    RelevantSemanticPage,
    RelevantPageObject,
    AssignedField,
    FieldDefinition,
    FieldSet,
    PageObjectType,
    DocumentCategory,
    ReturnType,
)


@click.command()
def command():
    """
    Truncate all tables in the CDP app.
    """
    truncate_cdp_tables()


def truncate_cdp_tables():
    # delete highest dependency first
    GenericPriorityMapping.objects.all().delete()
    RelevantSemanticDocument.objects.all().delete()
    RelevantSemanticPage.objects.all().delete()
    RelevantPageObject.objects.all().delete()
    AssignedField.objects.all().delete()

    FieldDefinition.objects.all().delete()
    FieldSet.objects.all().delete()

    PageObjectType.objects.all().delete()
    DocumentCategory.objects.all().delete()
    ReturnType.objects.all().delete()

    click.secho("Truncated CDP tables.", fg="green")

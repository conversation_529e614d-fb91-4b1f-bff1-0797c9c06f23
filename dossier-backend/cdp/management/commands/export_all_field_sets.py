import json
from pathlib import Path
from django.utils import timezone

import djclick as click
from django.contrib.contenttypes.models import ContentType
from cdp.models import (
    FieldSet,
    AssignedField,
    RelevantPageObject,
    RelevantSemanticPage,
    RelevantSemanticDocument,
    GenericPriorityMapping,
    RelevantFieldContext,
)


def get_priority_mappings_for_object(obj):
    """
    Given any relevant object (page, semantic page/document),
    return a list of dicts describing its GenericPriorityMapping rows.
    """
    ct = ContentType.objects.get_for_model(type(obj))
    gpms = GenericPriorityMapping.objects.filter(content_type=ct, object_id=obj.uuid)

    data = []
    for gpm in gpms:
        data.append(
            {
                "document_category_key": gpm.document_category.key,
                "priority": gpm.priority,
            }
        )
    return data


@click.command()
def command():
    """
    Export all FieldSets to a nested JSON file, including
     - field_definition_context_strategy
     - relevant_field_contexts
    the new GenericPriorityMapping data for:
      - RelevantPageObject
      - RelevantSemanticPage
      - RelevantSemanticDocument
    """
    field_sets = FieldSet.objects.all()
    data = []

    for field_set in field_sets:
        field_set_data = {"key": field_set.key, "assigned_fields": []}

        assigned_fields = AssignedField.objects.filter(field_set=field_set)
        for assigned_field in assigned_fields:
            field_definition = assigned_field.field_definition
            assigned_field_data = {
                "field_definition_key": field_definition.key,
                "field_definition_flavour": field_definition.flavour,
                "field_definition_return_type": (
                    field_definition.return_type.key
                    if field_definition.return_type
                    else None
                ),
                "field_definition_context_strategy": field_definition.field_context_strategy,
                "relevant_field_contexts": [],
                "relevant_page_objects": [],
                "relevant_semantic_pages": [],
                "relevant_semantic_documents": [],
            }

            # 0) RelevantFieldContext
            contexts = RelevantFieldContext.objects.filter(
                field_definition=field_definition
            )
            for context in contexts:
                other_fd = context.relevant_field_definition
                assigned_field_data["relevant_field_contexts"].append(
                    {
                        "relevant_field_definition_key": other_fd.key,
                        "relevant_field_definition_flavour": other_fd.flavour,
                        "weight": context.weight,
                    }
                )

            # 1) RelevantPageObject
            rpos = RelevantPageObject.objects.filter(field_definition=field_definition)
            for rpo in rpos:
                rpo_data = {
                    "page_object_key": rpo.page_object.key,
                    "priority_mappings": get_priority_mappings_for_object(rpo),
                }
                assigned_field_data["relevant_page_objects"].append(rpo_data)

            # 2) RelevantSemanticPage
            rspages = RelevantSemanticPage.objects.filter(
                field_definition=field_definition
            )
            for rspage in rspages:
                rspage_data = {
                    "relevant_object_type": rspage.relevant_object_type,  # e.g. "SEMANTIC_PAGE"
                    "priority_mappings": get_priority_mappings_for_object(rspage),
                }
                assigned_field_data["relevant_semantic_pages"].append(rspage_data)

            # 3) RelevantSemanticDocument
            rsdocs = RelevantSemanticDocument.objects.filter(
                field_definition=field_definition
            )
            for rsdoc in rsdocs:
                rsdoc_data = {
                    "relevant_object_type": rsdoc.relevant_object_type,  # e.g. "SEMANTIC_DOCUMENT"
                    "priority_mappings": get_priority_mappings_for_object(rsdoc),
                }
                assigned_field_data["relevant_semantic_documents"].append(rsdoc_data)

            field_set_data["assigned_fields"].append(assigned_field_data)

        data.append(field_set_data)

    # Write JSON to file
    field_set_data_path = (
        Path(__file__).resolve().parent.parent / "assets" / "field_set_data"
    )
    field_set_data_path.mkdir(parents=True, exist_ok=True)
    timestamp = timezone.now().strftime("%Y%m%d")
    file_path = field_set_data_path / f"field_set_export_{timestamp}.json"

    with open(file_path, "w") as json_file:
        json.dump(data, json_file, indent=2)

    click.secho(f"Data has been exported to {file_path}", fg="green")

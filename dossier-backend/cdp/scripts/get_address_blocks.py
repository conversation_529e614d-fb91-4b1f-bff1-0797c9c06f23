import csv
import os
import sys
from pathlib import Path
from typing import Dict

import pandas as pd
from django.core.wsgi import get_wsgi_application
from rapidfuzz import fuzz


sys.path.append(str(Path(__file__).resolve().parent.parent.parent))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "projectconfig.settings")
django_wgsi_app = get_wsgi_application()
from processed_file.models import PageObject
from dossier.models import Dossier
from cdp.value_parser import StreetParser, ZipCodeParser, CityParser


def save_to_csv(street_po, address_block_po, zip_code_po, city_po, filename: Path):
    file_exists = filename.exists()
    with open(filename, mode="a", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)

        # Write headers if file doesn't exist
        if not file_exists:
            writer.writerow(["Address Block", "Street", "Zip Code", "City"])

        # Write extracted values
        writer.writerow(
            [
                address_block_po.value if address_block_po else "",
                street_po.value if street_po else "",
                zip_code_po.value if zip_code_po else "",
                city_po.value if city_po else "",
            ]
        )


def load_csv(filename: Path) -> pd.DataFrame:
    """
    Load the saved addresses from a CSV file.

    :param filename: Path to the CSV file
    :return: Pandas DataFrame containing the address data
    """
    if not filename.exists():
        raise FileNotFoundError(f"CSV file not found: {filename}")

    return pd.read_csv(filename, encoding="utf-8")


def compare_results(row: Dict) -> Dict:
    """
    Compare parsed values against the saved values using fuzzy matching.

    :param row: A dictionary containing address fields from the CSV
    :return: Dictionary with similarity scores
    """
    # Get original values from CSV
    original_street = row["Street"]
    original_zip = str(row["Zip Code"])
    original_city = row["City"]

    sp = StreetParser(return_type="street")
    zp = ZipCodeParser(return_type="zip_code")
    cp = CityParser(return_type="city")
    # Parse extracted values from address block
    try:
        extracted_street = sp.parse(row["Address Block"])
    except Exception:
        extracted_street = ""
    try:
        extracted_zip = zp.parse(row["Address Block"])
    except Exception:
        extracted_zip = ""

    try:
        extracted_city = cp.parse(row["Address Block"])
    except Exception:
        extracted_city = ""

    # Compute fuzzy similarity scores
    street_similarity = fuzz.ratio(original_street, str(extracted_street))
    zip_similarity = fuzz.ratio(original_zip, str(extracted_zip))
    city_similarity = fuzz.ratio(original_city, str(extracted_city))

    return {
        "address_block": row["Address Block"],
        "original_street": original_street,
        "extracted_street": extracted_street,
        "street_similarity": street_similarity,
        "original_zip": original_zip,
        "extracted_zip": extracted_zip,
        "zip_similarity": zip_similarity,
        "original_city": original_city,
        "extracted_city": extracted_city,
        "city_similarity": city_similarity,
    }


def process_csv(filename: Path) -> pd.DataFrame:
    """
    Load the CSV file, parse addresses, and compute fuzzy similarity scores.
    """
    df = load_csv(filename)

    results = df.apply(compare_results, axis=1)

    results_df = pd.DataFrame(results.tolist())

    return results_df


def save_comparison_results(results_df: pd.DataFrame, output_file: Path):
    """
    Save the fuzzy comparison results to a CSV file.

    :param results_df: DataFrame containing the comparison results
    :param output_file: Path to save the results
    """
    results_df.to_csv(output_file, index=False, encoding="utf-8")
    print(f"Comparison results saved to {output_file}")


def main():
    N_ADDR_BLOCKS = 1000
    dest_dir = Path(__file__).parent / "data"
    dest_dir.mkdir(exist_ok=True, parents=True)
    filename = dest_dir / f"address_blocks_{N_ADDR_BLOCKS}.csv"

    dossier_list = Dossier.objects.all()
    dossier_count = 0
    address_block_count = 0
    while address_block_count < N_ADDR_BLOCKS:
        d = dossier_list[dossier_count]
        dossier_count += 1
        print(f"Dossier: {d.name}")
        street_po = PageObject.objects.filter(
            processed_page__dossier=d, key__key="street"
        ).first()
        if street_po:
            address_block_po = (
                PageObject.objects.filter(
                    processed_page=street_po.processed_page, key__key="address_block"
                )
                .filter(confidence_value__gte=0.8)
                .order_by("confidence_value")
                .first()
            )
            zip_code_po = (
                PageObject.objects.filter(
                    processed_page=street_po.processed_page, key__key="zip"
                )
                .filter(confidence_value__gte=0.8)
                .order_by("confidence_value")
                .first()
            )
            city_po = (
                PageObject.objects.filter(
                    processed_page=street_po.processed_page, key__key="city"
                )
                .filter(confidence_value__gte=0.8)
                .order_by("confidence_value")
                .first()
            )
            if (
                address_block_po
                and zip_code_po
                and city_po
                and len(address_block_po.value) > 30
            ):
                print(f"Street: {street_po.value}")
                print(f"Address block: {address_block_po.value}")
                print(f"Zip code: {zip_code_po.value}")
                print(f"City: {city_po.value}")
                save_to_csv(street_po, address_block_po, zip_code_po, city_po, filename)
                address_block_count += 1

    print(f"Processed {dossier_count} dossiers")
    print(f"Extracted {address_block_count} address blocks")


if __name__ == "__main__":
    main()

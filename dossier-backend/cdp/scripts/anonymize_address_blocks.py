import re
from pathlib import Path

import pandas as pd
import random
import string

from rapidfuzz import fuzz


def generate_random_text(length=10):
    """Generate a random string of letters."""
    abbr_map = {
        # German abbreviations
        "Str.": "Strasse",
        "str.": "strasse",
        "Hptstr.": "Hauptstrasse",
        "H.": "Hauptstrasse",
        "G.": "Gasse",
        "Pl.": "Platz",
        "Br.": "Brücke",
        "weg": "weg",
        # French abbreviations
        "Ch.": "Chemin",
        "Av.": "Avenue",
        "Rte.": "Route",
        "Rte": "Route",
        "All.": "Allée",
        "Pass.": "Passage",
        "Bd.": "Boulevard",
        "R": "Rue",  # Special case: Needs space or dot handling
        # Italian abbreviations
        "V.": "Via",
        "V.le": "Viale",
        "P.zza": "Piazza",
        "C.so": "Corso",
        # English abbreviations
        "St.": "Street",
        "Pk.": "Park",
        "Dr.": "Drive",
        "Ct.": "Court",
        "Ln.": "Lane",
        "Rd.": "Road",
    }
    common_street_words = r"(" + "|".join(map(re.escape, abbr_map.values())) + r")"
    street_word_pattern = rf"\b\w*{common_street_words}\w*\b"
    while True:
        random_text = "".join(random.choices(string.ascii_letters, k=length))
        if not re.search(street_word_pattern, random_text):
            return random_text


def is_fuzzy_match(word, comparison_values, threshold=80):
    """Check if a word is a fuzzy match to any of the comparison values."""
    return any(
        fuzz.ratio(word.strip().lower(), comparison_value.strip().lower()) >= threshold
        for comparison_value in comparison_values
    )


def replace_non_matching_words(row):
    """Replace words in text that are not in the corresponding row's comparison values with random text."""
    comparison_values = (
        set(row["Street"].split())
        .union(str(row["Zip Code"]).split())
        .union(row["City"].split())
    )
    lines = re.split(r"\r\n|\r|\n|,", row["Address Block"])
    anonymized_lines = []
    for line in lines:
        words = line.split()
        anonymized_line = " ".join(
            [
                (
                    word
                    if is_fuzzy_match(word, comparison_values)
                    else generate_random_text(len(word.strip()))
                )
                for word in words
            ]
        )
        anonymized_lines.append(anonymized_line)
    return "\n".join(anonymized_lines)


def main():
    N_ADDR_BLOCKS = 1000
    dest_dir = Path(__file__).parent / "data"
    dest_dir.mkdir(exist_ok=True, parents=True)
    filename = dest_dir / f"address_blocks_{N_ADDR_BLOCKS}.csv"
    df = pd.read_csv(filename)

    # Apply function to each row in Column2
    df["Address Block"] = df.apply(replace_non_matching_words, axis=1)

    # Save or display the anonymized DataFrame
    df.to_csv(dest_dir / f"anonymized_addresses_{N_ADDR_BLOCKS}.csv", index=False)
    print("Anonymization completed. Data saved to anonymized_addresses.csv")


if __name__ == "__main__":
    main()

# Generated by Django 4.2.15 on 2024-09-17 13:12

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("cdp", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="ReturnType",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("key", models.<PERSON>r<PERSON>ield(unique=True)),
                (
                    "description",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AlterField(
            model_name="fielddefinition",
            name="flavour",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="fielddefinition",
            name="key",
            field=models.<PERSON>r<PERSON>ield(max_length=255),
        ),
        migrations.AlterUniqueTogether(
            name="fielddefinition",
            unique_together={("key", "flavour")},
        ),
        migrations.AddField(
            model_name="fielddefinition",
            name="return_type",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to="cdp.returntype",
            ),
        ),
    ]

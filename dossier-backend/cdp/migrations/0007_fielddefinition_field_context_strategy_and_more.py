# Generated by Django 4.2.18 on 2025-02-13 09:53

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("cdp", "0006_delete_prioritymapping"),
    ]

    operations = [
        migrations.AddField(
            model_name="fielddefinition",
            name="field_context_strategy",
            field=models.CharField(
                blank=True,
                choices=[("TXT_FILTER", "txt_filter")],
                max_length=255,
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="RelevantFieldContext",
            fields=[
                (
                    "uuid",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("weight", models.FloatField(default=1.0)),
                (
                    "field_definition",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="field_definition",
                        to="cdp.fielddefinition",
                    ),
                ),
                (
                    "relevant_field_definition",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relevant_ctx_field_definition",
                        to="cdp.fielddefinition",
                    ),
                ),
            ],
            options={
                "unique_together": {("field_definition", "relevant_field_definition")},
            },
        ),
        migrations.AddField(
            model_name="fielddefinition",
            name="relevant_field_context",
            field=models.ManyToManyField(
                related_name="relevant_ctx",
                through="cdp.RelevantFieldContext",
                to="cdp.fielddefinition",
            ),
        ),
    ]

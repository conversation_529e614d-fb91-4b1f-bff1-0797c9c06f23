import json
from typing import List

import structlog
from django.conf import settings
from django.http import HttpRequest
from jwcrypto.jwk import JW<PERSON>
from jwcrypto.jwt import JWT, JWTExpired
from ninja import <PERSON><PERSON><PERSON>
from ninja.pagination import paginate
from ninja.security import HttpBearer
from ninja.errors import HttpError

from cdp.schemas import (
    RecommendationRequest,
    RecommendationResponse,
    FieldDefinitionResponse,
    NumRecommendationsResponse,
    NumRecommendationRequest,
    AnalyticsRequest,
    UnifiedRecommendationResponse,
)
from cdp.services import (
    get_page_object_types,
    get_assigned_field_for_field_definition_key_in_field_set,
    get_semantic_page_page_objects_with_key,
    get_recommended_sppos,
    prepare_recommendation_response,
    get_field_definitions_for_field_set,
    parse_dossier_access_token,
    get_num_recommendations,
    get_grouped_recommendations,
)

logger = structlog.get_logger()


class AuthBearer(HttpBearer):
    def __init__(self) -> None:
        super().__init__()

        self.private_key = JWK.from_json(settings.INSTANCE_SIGNING_KEY)

    def authenticate(self, request: HttpRequest, token: str):
        jwt = JWT()
        logger.info("dossier-access-token", token=token)
        try:
            jwt.deserialize(token, self.private_key)
            return jwt
        except JWTExpired as e:
            logger.warning("JWT token expired", error=str(e))
            return None
        except Exception as e:
            logger.error("Failed to authenticate", error=str(e))
            return None


api = NinjaAPI(
    title="Hypodossier - CDP API",
    csrf=False,
    auth=AuthBearer(),
    urls_namespace="cdp-api",
    version="1.0.0",
    servers=[],
)


@api.post(
    "/recommendations/",
    response={200: List[RecommendationResponse]},
    operation_id="recommendation-post",
    url_name="recommendation-post",
)
@paginate
def recommendations_post(
    request: HttpRequest,
    recommendation_request: RecommendationRequest,
):
    # The customer field_names are configured so that they match the field_definition key
    # For the FieldSet assigned for a customer, we can have a field_definition with the key as the field_definition_key
    # and the field_definition_flavour can be a customer specific value, thus allowing for customer specific field_definitions

    dossier_uuid, field_set = parse_dossier_access_token(request)
    field_definition_key = recommendation_request.field_definition
    assigned_field = get_assigned_field_for_field_definition_key_in_field_set(
        field_definition_key, field_set
    )
    # TODO : Refactor this snippet since these lines are repeated in multiple functions

    if not assigned_field:
        """
        If the field is not found in the field set, return an error message 404 : Not Found
        """
        logger.error(
            f"Field {field_definition_key} not found in field set {field_set}",
        )
        raise HttpError(
            404,
            f"Field {field_definition_key} not found in field set {field_set}",
        )

    page_object_types = get_page_object_types(
        assigned_field=assigned_field,
    )

    field_poks = [po.key for po in page_object_types]
    sppos = get_semantic_page_page_objects_with_key(
        dossier_uuid, field_poks, field_definition_key
    )

    # get recommended sppo
    sppo_recommendations = get_recommended_sppos(
        sppos,
        assigned_field=assigned_field,
    )

    return prepare_recommendation_response(sppo_recommendations, assigned_field)


@api.post(
    "/grouped_recommendations/",
    response={200: List[UnifiedRecommendationResponse]},
    operation_id="grouped-recommendation-post",
    url_name="grouped-recommendation-post",
)
@paginate
def grouped_recommendations_post(
    request: HttpRequest,
    recommendation_request: RecommendationRequest,
):
    dossier_uuid, field_set = parse_dossier_access_token(request)
    field_definition_key = recommendation_request.field_definition
    field_context = recommendation_request.field_context
    grouped_recommendation_response = get_grouped_recommendations(
        dossier_uuid,
        field_set=field_set,
        field_definition_key=field_definition_key,
        field_context=field_context,
    )
    return grouped_recommendation_response


@api.get(
    "/field_definitions/",
    response={200: List[FieldDefinitionResponse]},
    operation_id="field_definitions-get",
    url_name="field_definitions-get",
)
def get_field_definitions(request: HttpRequest):
    """
    Get the field definitions for the field_set as provided in the JWT claims of the dossier-access-token (CDP) token
    """
    jwt: JWT = request.auth
    field_set = json.loads(jwt.claims).get("field_set")
    if field_set is None:
        raise HttpError(401, "Field set not found in the dossier-access-token")

    try:

        field_definitions = get_field_definitions_for_field_set(field_set)
        if len(field_definitions) == 0:
            logger.info(
                f"No field definitions found for field set {field_set}",
            )
        return field_definitions

    except Exception as e:
        logger.error(
            f"Error getting field definitions for field set {field_set}", error=str(e)
        )
    raise HttpError(404, f"Error getting field definitions for field set: {field_set}")


@api.post(
    "/num_recommendations/",
    response={200: NumRecommendationsResponse},
    operation_id="num-recommendations-post",
    url_name="num-recommendations-post",
)
def num_recommendations_post(
    request: HttpRequest,
    num_recommendation_request: NumRecommendationRequest,
):
    dossier_uuid, field_set = parse_dossier_access_token(request)
    num_recommendations_response = get_num_recommendations(
        dossier_uuid, field_set, num_recommendation_request
    )

    return num_recommendations_response


@api.post(
    "/analytics/",
    response={200: None},
    operation_id="analytics-post",
    url_name="analytics-post",
)
def analytics_post(request: HttpRequest, analytics_request: AnalyticsRequest):
    dossier_uuid = parse_dossier_access_token(request)
    analytics_payload = {"dossier_uuid": dossier_uuid[0], **analytics_request.payload}
    # TODO: move logs prefix to constant/logging class
    logger.info("[CDP] [Analytics] Num recommendations", **analytics_payload)

    return None

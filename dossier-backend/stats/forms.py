from django import forms
from django.utils import timezone
from datetime import timed<PERSON>ta
from dateutil.relativedelta import relativedelta


class TimeRangeForm(forms.Form):
    """
    Form for selecting time range for statistics
    """

    TIME_RANGE_CHOICES = [
        ("30d", "Last 30 Days"),
        ("90d", "Last Quarter"),
        ("1y", "Last Year"),
        ("all", "All Time"),
        ("custom", "Custom Range"),
    ]

    time_range = forms.ChoiceField(
        choices=TIME_RANGE_CHOICES,
        initial="30d",
        widget=forms.Select(
            attrs={
                "class": "rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            }
        ),
    )

    start_date = forms.DateField(
        required=False,
        widget=forms.DateInput(
            attrs={
                "type": "date",
                "class": "rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",
            }
        ),
    )

    end_date = forms.DateField(
        required=False,
        widget=forms.DateInput(
            attrs={
                "type": "date",
                "class": "rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50",
            }
        ),
    )

    compare = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(
            attrs={
                "class": "rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
            }
        ),
    )

    def clean(self):
        cleaned_data = super().clean()
        time_range = cleaned_data.get("time_range")
        start_date = cleaned_data.get("start_date")
        end_date = cleaned_data.get("end_date")

        if time_range == "custom" and (not start_date or not end_date):
            raise forms.ValidationError(
                "Start date and end date are required for custom time range"
            )

        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError("Start date must be before end date")

        return cleaned_data

    def get_date_range(self):
        """
        Returns a tuple of (start_date, end_date) based on the selected time range
        """
        time_range = self.cleaned_data.get("time_range")
        today = timezone.now().date()

        if time_range == "30d":
            start_date = today - timedelta(days=30)
            end_date = today
        elif time_range == "90d":
            start_date = today - timedelta(days=90)
            end_date = today
        elif time_range == "1y":
            start_date = today - relativedelta(years=1)
            end_date = today
        elif time_range == "all":
            start_date = None
            end_date = None
        elif time_range == "custom":
            start_date = self.cleaned_data.get("start_date")
            end_date = self.cleaned_data.get("end_date")
        else:
            start_date = today - timedelta(days=30)
            end_date = today

        return start_date, end_date

    def get_previous_date_range(self):
        """
        Returns a tuple of (start_date, end_date) for the previous time period
        """
        start_date, end_date = self.get_date_range()

        if start_date is None or end_date is None:
            return None, None

        # Calculate the duration of the current range
        duration = end_date - start_date

        # Calculate the previous range
        prev_end_date = start_date - timedelta(days=1)
        prev_start_date = prev_end_date - duration

        return prev_start_date, prev_end_date

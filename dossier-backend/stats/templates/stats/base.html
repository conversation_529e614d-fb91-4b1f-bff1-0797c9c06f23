{% load static tailwind_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Statistics Dashboard{% endblock %}</title>
    {% tailwind_css %}
    <!-- Add any additional CSS or JS here -->
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="bg-indigo-600 shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <h1 class="text-white text-xl font-bold">Dossier Statistics</h1>
                    </div>
                    <nav class="ml-6 flex space-x-4">
                        <a href="{% url 'stats:dashboard' %}" class="{% if request.path == '/stats/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                        <a href="{% url 'stats:dossier_stats' %}" class="{% if request.path == '/stats/dossiers/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Dossiers</a>
                        <a href="{% url 'stats:document_stats' %}" class="{% if request.path == '/stats/documents/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Documents</a>
                        <a href="{% url 'stats:user_stats' %}" class="{% if request.path == '/stats/users/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Users</a>
                        <a href="{% url 'stats:file_stats' %}" class="{% if request.path == '/stats/files/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Files</a>
                        <a href="{% url 'stats:exception_stats' %}" class="{% if request.path == '/stats/exceptions/' %}bg-indigo-700 text-white{% else %}text-white hover:bg-indigo-500{% endif %} px-3 py-2 rounded-md text-sm font-medium">Exceptions</a>
                    </nav>
                </div>
                <div class="flex items-center">
                    <a href="/admin/" class="text-white hover:bg-indigo-500 px-3 py-2 rounded-md text-sm font-medium">Admin</a>
                    <a href="/admin/logout/" class="text-white hover:bg-indigo-500 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <p class="text-center text-gray-500 text-sm">
                &copy; {% now "Y" %} Dossier Statistics Dashboard
            </p>
        </div>
    </footer>
</body>
</html>

{% load static %}

<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <form method="get" class="space-y-4" id="time-range-form">
            <div class="flex flex-wrap gap-4 items-end">
                <div class="flex-1 min-w-[200px]">
                    <label for="{{ form.time_range.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
                    {{ form.time_range }}
                </div>
                
                <div class="flex-1 min-w-[200px] custom-date-range {% if form.time_range.value != 'custom' %}hidden{% endif %}">
                    <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    {{ form.start_date }}
                </div>
                
                <div class="flex-1 min-w-[200px] custom-date-range {% if form.time_range.value != 'custom' %}hidden{% endif %}">
                    <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    {{ form.end_date }}
                </div>
                
                <div class="flex items-center">
                    <div class="flex items-center h-5">
                        {{ form.compare }}
                    </div>
                    <div class="ml-2 text-sm">
                        <label for="{{ form.compare.id_for_label }}" class="font-medium text-gray-700">Compare with previous period</label>
                    </div>
                </div>
                
                <div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Apply Filter
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const timeRangeSelect = document.getElementById('{{ form.time_range.id_for_label }}');
        const customDateRangeFields = document.querySelectorAll('.custom-date-range');
        
        timeRangeSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateRangeFields.forEach(field => field.classList.remove('hidden'));
            } else {
                customDateRangeFields.forEach(field => field.classList.add('hidden'));
            }
        });
    });
</script>

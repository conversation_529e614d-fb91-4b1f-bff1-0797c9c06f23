{% extends 'stats/base.html' %}

{% block title %}Dossier Statistics{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            Dossier Statistics
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Detailed statistics about dossiers in the system.
        </p>
    </div>

    <!-- Average Documents per Dossier -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Average Documents per Dossier
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-blue-600">
                    {{ avg_documents.avg|floatformat:1 }}
                </dd>
                {% if avg_documents_change is not None and compare %}
                <dd class="mt-2 text-sm {% if avg_documents_change > 0 %}text-green-600{% elif avg_documents_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                    {% if avg_documents_change > 0 %}+{% endif %}{{ avg_documents_change }}% from previous period ({{ avg_documents_previous|floatformat:1 }})
                </dd>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Dossiers by Status -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Dossiers by Status
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for status in dossier_by_status %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col">
                                <p class="text-sm font-medium text-indigo-600 truncate">
                                    {{ status.work_status__name_en|default:"No Status" }}
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                    <div class="bg-indigo-600 h-2.5 rounded-full" style="width: {{ status.percentage }}%"></div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {{ status.count }} dossiers
                                </p>
                                <span class="ml-2 text-xs text-gray-500">{{ status.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No dossier status data found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Dossiers by Account -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Dossiers by Account
        </h3>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for account in dossier_by_account %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col">
                                <p class="text-sm font-medium text-indigo-600 truncate">
                                    {{ account.name }}
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ account.percentage }}%"></div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ account.dossier_count }} dossiers
                                </p>
                                <span class="ml-2 text-xs text-gray-500">{{ account.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No accounts found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}

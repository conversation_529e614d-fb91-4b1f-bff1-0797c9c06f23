{% extends 'stats/base.html' %}

{% block title %}Document Statistics{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            Document Statistics
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Detailed statistics about documents in the system.
        </p>
    </div>

    <!-- Page Statistics -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            Page Statistics
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Average Pages per Document -->
            <div class="bg-blue-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Average Pages per Document
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-blue-600">
                        {{ avg_pages.avg|floatformat:1 }}
                    </dd>
                    {% if avg_pages_change is not None and compare %}
                    <dd class="mt-2 text-sm {% if avg_pages_change > 0 %}text-green-600{% elif avg_pages_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                        {% if avg_pages_change > 0 %}+{% endif %}{{ avg_pages_change }}% from previous period ({{ avg_pages_previous|floatformat:1 }})
                    </dd>
                    {% endif %}
                </div>
            </div>

            <!-- Maximum Pages per Document -->
            <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        Maximum Pages per Document
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-purple-600">
                        {{ max_pages }}
                    </dd>
                    {% if max_pages_change is not None and compare %}
                    <dd class="mt-2 text-sm {% if max_pages_change > 0 %}text-green-600{% elif max_pages_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                        {% if max_pages_change > 0 %}+{% endif %}{{ max_pages_change }}% from previous period ({{ max_pages_previous }})
                    </dd>
                    {% endif %}
                </div>
            </div>

            <!-- 95th Percentile Pages per Document -->
            <div class="bg-green-50 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">
                        95th Percentile Pages
                    </dt>
                    <dd class="mt-1 text-3xl font-semibold text-green-600">
                        {{ percentile_95_pages }}
                    </dd>
                    {% if percentile_95_pages_change is not None and compare %}
                    <dd class="mt-2 text-sm {% if percentile_95_pages_change > 0 %}text-green-600{% elif percentile_95_pages_change < 0 %}text-red-600{% else %}text-gray-500{% endif %}">
                        {% if percentile_95_pages_change > 0 %}+{% endif %}{{ percentile_95_pages_change }}% from previous period ({{ percentile_95_pages_previous }})
                    </dd>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Documents by Category -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <div class="flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Documents by Category
            </h3>
            <div class="flex items-center text-xs">
                <div class="flex items-center mr-4">
                    <div class="w-3 h-3 bg-indigo-600 rounded-full mr-1"></div>
                    <span>% of all documents</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-600 rounded-full mr-1"></div>
                    <span>% of dossiers with this category</span>
                </div>
            </div>
        </div>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for category in docs_by_category %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col w-full">
                                <a href="{% url 'stats:category_documents' category.document_category__id %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" class="text-sm font-medium text-indigo-600 truncate hover:underline">
                                    {{ category.document_category__name|default:"No Category" }}
                                </a>
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>Documents: {{ category.percentage }}%</span>
                                    <span>Dossiers: {{ category.dossier_percentage }}%</span>
                                </div>
                                <div class="w-full flex mt-1">
                                    <div class="w-1/2 pr-1">
                                        <div class="bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-indigo-600 h-2.5 rounded-full" style="width: {{ category.percentage }}%"></div>
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-1">
                                        <div class="bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-green-600 h-2.5 rounded-full" style="width: {{ category.dossier_percentage }}%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <div class="flex flex-col items-end">
                                    <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">
                                        {{ category.count }} documents
                                    </p>
                                    <p class="px-2 mt-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        {{ category.dossier_count }} dossiers
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No document categories found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Documents by Status -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Documents by Status
        </h3>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for status in docs_by_status %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col">
                                <p class="text-sm font-medium text-indigo-600 truncate">
                                    {{ status.work_status__name_en|default:"No Status" }}
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ status.percentage }}%"></div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    {{ status.count }} documents
                                </p>
                                <span class="ml-2 text-xs text-gray-500">{{ status.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No document status data found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}

{% extends 'stats/base.html' %}

{% block title %}Exception Statistics{% endblock %}

{% block content %}
<!-- Time Range Filter -->
{% include 'stats/time_filter.html' %}

<!-- Date Range Info -->
{% if start_date and end_date %}
<div class="bg-white shadow rounded-lg mb-6 p-4">
    <h3 class="text-lg font-medium text-gray-900">
        {% if start_date == end_date %}
            Statistics for {{ start_date|date:"F j, Y" }}
        {% else %}
            Statistics from {{ start_date|date:"F j, Y" }} to {{ end_date|date:"F j, Y" }}
        {% endif %}

        {% if compare and prev_start_date and prev_end_date %}
            <span class="text-sm text-gray-500 ml-2">
                (compared to {{ prev_start_date|date:"F j, Y" }} - {{ prev_end_date|date:"F j, Y" }})
            </span>
        {% endif %}
    </h3>
</div>
{% endif %}

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900">
            File Extraction Exception Statistics
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Detailed statistics about file extraction exceptions in the system.
        </p>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4">
        <!-- Total Exceptions -->
        <div class="bg-red-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Total Exceptions
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-red-600">
                    {{ total_exceptions }}
                </dd>
                {% if total_exceptions_change is not None and compare %}
                <dd class="mt-2 text-sm {% if total_exceptions_change > 0 %}text-red-600{% elif total_exceptions_change < 0 %}text-green-600{% else %}text-gray-500{% endif %}">
                    {% if total_exceptions_change > 0 %}+{% endif %}{{ total_exceptions_change }}% from previous period ({{ total_exceptions_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Exception Rate -->
        <div class="bg-orange-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Exception Rate
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-orange-600">
                    {{ exception_rate }}%
                </dd>
                {% if exception_rate_change is not None and compare %}
                <dd class="mt-2 text-sm {% if exception_rate_change > 0 %}text-red-600{% elif exception_rate_change < 0 %}text-green-600{% else %}text-gray-500{% endif %}">
                    {% if exception_rate_change > 0 %}+{% endif %}{{ exception_rate_change }}% from previous period ({{ exception_rate_previous }}%)
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Original File Exceptions -->
        <div class="bg-yellow-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Original File Exceptions
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-yellow-600">
                    {{ original_file_exceptions }}
                </dd>
                {% if original_file_exceptions_change is not None and compare %}
                <dd class="mt-2 text-sm {% if original_file_exceptions_change > 0 %}text-red-600{% elif original_file_exceptions_change < 0 %}text-green-600{% else %}text-gray-500{% endif %}">
                    {% if original_file_exceptions_change > 0 %}+{% endif %}{{ original_file_exceptions_change }}% from previous period ({{ original_file_exceptions_previous }})
                </dd>
                {% endif %}
            </div>
        </div>

        <!-- Extracted File Exceptions -->
        <div class="bg-purple-50 overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <dt class="text-sm font-medium text-gray-500 truncate">
                    Extracted File Exceptions
                </dt>
                <dd class="mt-1 text-3xl font-semibold text-purple-600">
                    {{ extracted_file_exceptions }}
                </dd>
                {% if extracted_file_exceptions_change is not None and compare %}
                <dd class="mt-2 text-sm {% if extracted_file_exceptions_change > 0 %}text-red-600{% elif extracted_file_exceptions_change < 0 %}text-green-600{% else %}text-gray-500{% endif %}">
                    {% if extracted_file_exceptions_change > 0 %}+{% endif %}{{ extracted_file_exceptions_change }}% from previous period ({{ extracted_file_exceptions_previous }})
                </dd>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Exceptions by Type -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Exceptions by Type
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for exception_type in exceptions_by_type %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col w-full">
                                <p class="text-sm font-medium text-indigo-600 truncate">
                                    {{ exception_type.exception_name }}
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                    <div class="bg-red-600 h-2.5 rounded-full" style="width: {{ exception_type.percentage }}%"></div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ exception_type.count }} exceptions
                                </p>
                                <span class="ml-2 text-xs text-gray-500">{{ exception_type.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No exceptions found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <!-- Unsupported File Types -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Unsupported File Types
        </h3>
    </div>
    <div class="px-4 py-3">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            {% if unsupported_file_types %}
            <div class="p-4">
                <p class="text-sm text-gray-500 mb-4">
                    Total unsupported file exceptions: <span class="font-semibold">{{ total_unsupported }}</span>
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for file_type in unsupported_file_types %}
                    <div class="border rounded-lg p-4 bg-gray-50">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-lg font-bold text-indigo-600">{{ file_type.extension }}</span>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                {{ file_type.count }} files
                            </span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-red-600 h-2.5 rounded-full" style="width: {{ file_type.percentage }}%"></div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 text-right">{{ file_type.percentage }}%</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="p-4 text-sm text-gray-500">
                No unsupported file types found in the selected time period.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Exceptions by Account -->
    <div class="px-4 py-5 sm:px-6 border-t border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Exceptions by Account
        </h3>
    </div>
    <div class="px-4 py-3 pb-6">
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <ul class="divide-y divide-gray-200">
                {% for account in exceptions_by_account %}
                <li>
                    <div class="px-4 py-4 sm:px-6">
                        <div class="flex items-center justify-between">
                            <div class="flex flex-col w-full">
                                <p class="text-sm font-medium text-indigo-600 truncate">
                                    {{ account.name }}
                                </p>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                                    <div class="bg-red-600 h-2.5 rounded-full" style="width: {{ account.percentage }}%"></div>
                                </div>
                            </div>
                            <div class="ml-2 flex-shrink-0 flex items-center">
                                <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    {{ account.count }} exceptions
                                </p>
                                <span class="ml-2 text-xs text-gray-500">{{ account.percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </li>
                {% empty %}
                <li class="px-4 py-4 sm:px-6 text-sm text-gray-500">
                    No exceptions found
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>
{% endblock %}

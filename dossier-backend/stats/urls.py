from django.urls import path
from . import views

app_name = "stats"

urlpatterns = [
    path("", views.dashboard, name="dashboard"),
    path("dossiers/", views.dossier_stats, name="dossier_stats"),
    path("documents/", views.document_stats, name="document_stats"),
    path("users/", views.user_stats, name="user_stats"),
    path("files/", views.file_stats, name="file_stats"),
    path("exceptions/", views.exception_stats, name="exception_stats"),
    path(
        "category/<str:category_id>/",
        views.category_documents,
        name="category_documents",
    ),
    path(
        "api/document/<uuid:document_uuid>/pages/",
        views.document_pages_api,
        name="document_pages_api",
    ),
]

"""backend URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.conf import settings
from django.contrib import admin
from django.urls import path, include, re_path

from bekb.api import api as bekb_api
from bekb.api_fipla import api as bekb_api_fipla
from projectconfig.api import api
from swissfex.api import api as swissfex_api
from zkb.api import api as zkb_api
from zkb.api import openapi_without_auth as zkb_openapi_without_auth
from cdp.api import api as cdp_api
from bcge.api import api as bcge_api
from clientis.api import api as clientis_api
from hypohaus.api import api as hypohaus_api
from hypoteq.api import api as hypoteq_api
from tophypo.api import api as tophypo_api
from vontobel.api import api as vontobel_api
from vz.api import api as vz_api
from pentest.api import api as pentest_api
from gputest.api import api as gputest_api
from finnova.api import api as finnova_api

if settings.ENABLE_ADMIN_KEYCLOAK_LOGIN:
    from adminauth.utils import get_provider_patterns
    from adminauth.views import keycloak_logout
    from allauth.socialaccount import views as social_views


urlpatterns = [
    *(
        [
            path("accounts/logout/", keycloak_logout, name="account_logout"),
            # Also overwrite the admin logout, or create a custom template
            path("admin/logout/", keycloak_logout, name="admin_logout"),
            # Include only oic urls, so we exclude everything else such as signup
            path("accounts/", include(get_provider_patterns())),
            # Prevent signup
            path(
                "accounts/signup/",
                social_views.login_error,
                name="socialaccount_signup",
            ),
            re_path(
                r"^accounts/password/.*",
                social_views.login_error,
                name="password_catchall",
            ),
            re_path(
                r"^accounts/email.*",
                social_views.login_error,
                name="email_catchall",
            ),
            path("accounts/", include("allauth.urls")),
        ]
        if settings.ENABLE_ADMIN_KEYCLOAK_LOGIN
        else []
    ),
    path("admin/", admin.site.urls),
    path("api/", api.urls),
    path(
        "partner/zkb/api/v1/openapi_without_auth.json",
        zkb_openapi_without_auth,
        name="zkbopenapiwithoutauth",
    ),
    path("partner/bekb/api/0.7/", bekb_api.urls),
    path("partner/bekbfipla/api/v1/", bekb_api_fipla.urls),
    path("partner/zkb/api/v1/", zkb_api.urls),
    path("partner/swissfex/api/0.7/", swissfex_api.urls),
    path("cdp/api/v1/", cdp_api.urls),
    path("partner/bcge/api/v1/", bcge_api.urls),
    path("partner/clientis/api/v1/", clientis_api.urls),
    path("partner/hypohaus/api/v1/", hypohaus_api.urls),
    path("partner/hypoteq/api/v1/", hypoteq_api.urls),
    path("partner/tophypo/api/v1/", tophypo_api.urls),
    path("partner/vontobel/api/v1/", vontobel_api.urls),
    path("partner/vz/api/v1/", vz_api.urls),
    path("partner/finnova/api/v1/", finnova_api.urls),
    path("partner/pentest/api/v1/", pentest_api.urls),
    path("partner/gputest/api/v1/", gputest_api.urls),
    # Stats app URLs - staff only access is enforced in the views
    path("stats/", include("stats.urls", namespace="stats")),
]


if settings.DEBUG_ENABLE_SILK:
    urlpatterns += [path("silk/", include("silk.urls", namespace="silk"))]

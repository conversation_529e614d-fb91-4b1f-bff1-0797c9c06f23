{"openapi": "3.0.2", "info": {"title": "Hypodossier - ZKB API", "version": "1.10.0", "description": ""}, "paths": {"/partner/zkb/api/v1/dossier": {"post": {"operationId": "zkb_api_create_dossier", "summary": "Create Dossier", "parameters": [], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDossier"}}}, "required": true}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}": {"patch": {"operationId": "zkb_api_update_dossier", "summary": "Update Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Updates a new Dossier based on the provided parameters\n\nWe provide a external_dossier_id as part of the URL and allow the client to change it\nas part of ChangeDossier", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangeDossier"}}}, "required": true}}, "delete": {"operationId": "zkb_api_delete_dossier", "summary": "Delete Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"202": {"description": "Accepted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/entities/realestateproperty": {"put": {"operationId": "zkb_api_create_or_update_real_estate_property", "summary": "Create Or Update Real Estate Property", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Create or update a new RealEstateProperty for a Dossier", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RealEstatePropertyCreate"}}}, "required": true}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/entities/realestateproperties/": {"get": {"operationId": "zkb_api_get_real_estate_properties", "summary": "Get Real Estate Properties", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/RealEstatePropertyResponse"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Get all real estate properties assigned to a dossier"}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/original-files": {"post": {"operationId": "zkb_api_add_original_file", "summary": "Add Original File", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatedObjectReference"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Add an original file to a dossier and optionally link it to a real estate property\nIf the estate property does not exist, it will be created with the entity_key provided\nbut will be missing additional information such as title, floor, street, street_nr, zipcode, city", "requestBody": {"content": {"multipart/form-data": {"schema": {"title": "MultiPartBodyParams", "type": "object", "properties": {"file": {"title": "File", "type": "string", "format": "binary"}, "entity_type": {"title": "EntityTypes", "description": "An enumeration.", "enum": ["realestateproperty", ""], "type": "string"}, "entity_key": {"title": "Entity Key", "type": "string"}, "allow_duplicate_and_rename": {"title": "Allow Duplicate And Rename", "default": false, "type": "boolean"}}, "required": ["file"]}}}, "required": true}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/filename/{original_filename}": {"get": {"operationId": "zkb_api_check_original_filename", "summary": "Check Original Filename", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "original_filename", "schema": {"title": "Original Filename", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}}, "description": "Check if a file with the same name already exists in the dossier"}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/file-status": {"get": {"operationId": "zkb_api_get_file_status", "summary": "Get File Status", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DossierProcessingStatus"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/details": {"get": {"operationId": "zkb_api_get_dossier", "summary": "Get Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Dossier"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-documents": {"get": {"operationId": "zkb_api_get_semantic_documents", "summary": "Get Semantic Documents", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "query", "name": "show_pages", "schema": {"title": "Show Pages", "default": false, "type": "boolean"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"title": "Response", "type": "array", "items": {"$ref": "#/components/schemas/SemanticDocument"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}}}, "/partner/zkb/api/v1/semantic-document/{semantic_document_uuid}/external_dossier_id": {"get": {"operationId": "zkb_api_get_external_dossier_id_from_semantic_document", "summary": "Get External Dossier Id From Semantic Document", "parameters": [{"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"title": "Response", "minLength": 1, "maxLength": 2083, "format": "uri", "type": "string"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportDossierExport"}}}}}, "description": "Get a dossier external id from a semantic document uuid"}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"patch": {"operationId": "zkb_api_update_semantic_document", "summary": "Update Semantic Document", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string", "format": "uuid"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocument"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}, "description": "Update the external_semantic_document_id and/or\naccess_mode for a semantic document using a semantic_document_uuid", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentUpdate"}}}, "required": true}}}, "/partner/zkb/api/v1/document-categories": {"get": {"operationId": "zkb_api_get_document_categories", "summary": "Get Document Categories", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCategories"}}}}}}}, "/partner/zkb/api/v1/export/dossier/{external_dossier_id}/semantic-documents/{semantic_document_uuid}": {"post": {"operationId": "zkb_api_export_semantic_document_pdf", "summary": "Export Semantic Document Pdf", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}, {"in": "path", "name": "semantic_document_uuid", "schema": {"title": "Semantic Document Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SemanticDocumentPDFExportRequest"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}}}}, "/partner/zkb/api/v1/export/{semantic_document_export_request_uuid}/status": {"get": {"operationId": "zkb_api_get_available_export", "summary": "Get Available Export", "parameters": [{"in": "path", "name": "semantic_document_export_request_uuid", "schema": {"title": "Semantic Document Export Request Uuid", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportStatus"}}}}}}}, "/partner/zkb/api/v1/dossier/{external_dossier_id}/show-dossier": {"get": {"operationId": "zkb_api_show_dossier", "summary": "Show Dossier", "parameters": [{"in": "path", "name": "external_dossier_id", "schema": {"title": "External Dossier Id", "type": "string"}, "required": true}], "responses": {"303": {"description": "See Other", "content": {"application/json": {"schema": {"title": "Response", "minLength": 1, "maxLength": 2083, "format": "uri", "type": "string"}}}}}}}}, "components": {"schemas": {"Dossier": {"title": "Dossier", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}}, "required": ["uuid", "external_dossier_id", "updated_at", "created_at"]}, "Error": {"title": "Error", "type": "object", "properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"]}, "CreateDossier": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}}, "required": ["external_dossier_id", "name"]}, "Message": {"title": "Message", "type": "object", "properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"]}, "Language": {"title": "Language", "description": "An enumeration.", "enum": ["de", "fr", "it", "en"], "type": "string"}, "ChangeDossier": {"title": "Change<PERSON><PERSON><PERSON>", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}, "name": {"title": "Name", "maxLength": 255, "type": "string"}, "lang": {"$ref": "#/components/schemas/Language"}}, "required": ["external_dossier_id"]}, "RealEstatePropertyResponse": {"title": "RealEstatePropertyResponse", "type": "object", "properties": {"key": {"title": "Key", "maxLength": 255, "type": "string"}, "title": {"title": "Title", "maxLength": 255, "type": "string"}, "floor": {"title": "Floor", "type": "integer"}, "street": {"title": "Street", "maxLength": 255, "type": "string"}, "street_nr": {"title": "Street Nr", "maxLength": 10, "type": "string"}, "zipcode": {"title": "Zipcode", "maxLength": 60, "type": "string"}, "city": {"title": "City", "maxLength": 255, "type": "string"}, "dossier_uuid": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}}, "required": ["key", "dossier_uuid", "external_dossier_id"]}, "RealEstatePropertyCreate": {"title": "RealEstatePropertyCreate", "type": "object", "properties": {"key": {"title": "Key", "maxLength": 255, "type": "string"}, "title": {"title": "Title", "maxLength": 255, "type": "string"}, "floor": {"title": "Floor", "type": "integer"}, "street": {"title": "Street", "maxLength": 255, "type": "string"}, "street_nr": {"title": "Street Nr", "maxLength": 10, "type": "string"}, "zipcode": {"title": "Zipcode", "maxLength": 60, "type": "string"}, "city": {"title": "City", "maxLength": 255, "type": "string"}}, "required": ["key"]}, "CreatedObjectReference": {"title": "CreatedObjectReference", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}}, "required": ["uuid"]}, "FileStatus": {"title": "FileStatus", "description": "An enumeration.", "enum": ["processing", "error", "processed"], "type": "string"}, "ExtractedFile": {"title": "ExtractedFile", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "path_from_original": {"title": "Path From Original", "type": "string"}, "file_name": {"title": "File Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "file_url": {"title": "File Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["uuid", "path_from_original", "file_name", "status", "file_url", "created_at", "updated_at"]}, "OriginalFile": {"title": "OriginalFile", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "name": {"title": "Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "extracted_files": {"title": "Extracted Files", "type": "array", "items": {"$ref": "#/components/schemas/ExtractedFile"}}, "file_url": {"title": "File Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "created_at": {"title": "Created At", "type": "string", "format": "date-time"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["uuid", "name", "status", "extracted_files", "file_url", "created_at", "updated_at"]}, "DossierProcessingStatus": {"title": "DossierProcessingStatus", "type": "object", "properties": {"dossier_uuid": {"title": "<PERSON><PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_id": {"title": "External Id", "type": "string"}, "progress": {"title": "Progress", "type": "integer"}, "original_files": {"title": "Original Files", "type": "array", "items": {"$ref": "#/components/schemas/OriginalFile"}}}, "required": ["dossier_uuid", "external_id", "progress", "original_files"]}, "Confidence": {"title": "Confidence", "description": "An enumeration.", "enum": ["certain", "high", "medium", "low"], "type": "string"}, "SemanticPage": {"title": "SemanticPage", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "number": {"title": "Number", "description": "Page number is zero based. First page has page number 0", "type": "integer"}, "image_url": {"title": "Image Url", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "last_change": {"title": "Last Change", "default": "2023-10-05T11:24:14.846960+00:00", "type": "string", "format": "date-time"}}, "required": ["uuid", "number", "updated_at"]}, "EntityTypes": {"title": "EntityTypes", "description": "An enumeration.", "enum": ["realestateproperty", ""], "type": "string"}, "SemanticDocumentAccessMode": {"title": "SemanticDocumentAccessMode", "description": "An enumeration.", "enum": ["read_only", "read_write"], "type": "string"}, "SemanticDocument": {"title": "SemanticDocument", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}, "external_semantic_document_id": {"title": "External Semantic Document Id", "maxLength": 255, "type": "string"}, "title": {"title": "Title", "type": "string"}, "document_category_confidence": {"$ref": "#/components/schemas/Confidence"}, "document_category_key": {"title": "Document Category Key", "type": "string"}, "semantic_pages": {"title": "Semantic Pages", "type": "array", "items": {"$ref": "#/components/schemas/SemanticPage"}}, "entity_type": {"$ref": "#/components/schemas/EntityTypes"}, "entity_key": {"title": "Entity Key", "type": "string"}, "access_mode": {"default": "read_write", "allOf": [{"$ref": "#/components/schemas/SemanticDocumentAccessMode"}]}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}, "last_change": {"title": "Last Change", "default": "2023-10-05T11:24:14.848078+00:00", "type": "string", "format": "date-time"}}, "required": ["uuid", "title", "document_category_confidence", "document_category_key", "semantic_pages", "updated_at"]}, "ExportDossierExport": {"title": "ExportDossierExport", "type": "object", "properties": {"external_dossier_id": {"title": "External Dossier Id", "maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "type": "string"}}, "required": ["external_dossier_id"]}, "SemanticDocumentUpdate": {"title": "SemanticDocumentUpdate", "type": "object", "properties": {"external_semantic_document_id": {"title": "External Semantic Document Id", "maxLength": 255, "type": "string"}, "access_mode": {"$ref": "#/components/schemas/SemanticDocumentAccessMode"}}}, "DocumentCategory": {"title": "DocumentCategory", "type": "object", "properties": {"key": {"title": "Key", "type": "string"}, "id": {"title": "Id", "type": "string"}, "title_de": {"title": "Title De", "type": "string"}, "description_de": {"title": "Description De", "type": "string"}}, "required": ["key", "id", "title_de"]}, "DocumentCategories": {"title": "DocumentCategories", "type": "object", "additionalProperties": {"$ref": "#/components/schemas/DocumentCategory"}}, "SemanticDocumentPDFExportRequest": {"title": "SemanticDocumentPDFExportRequest", "type": "object", "properties": {"uuid": {"title": "<PERSON><PERSON>", "type": "string", "format": "uuid"}}, "required": ["uuid"]}, "ExportProcessingStatus": {"title": "ExportProcessingStatus", "description": "An enumeration.", "enum": ["PROCESSING", "ERROR", "PROCESSED"], "type": "string"}, "ExportStatus": {"title": "ExportStatus", "type": "object", "properties": {"semantic_document_export_request_uuid": {"title": "Semantic Document Export Request Uuid", "type": "string", "format": "uuid"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "dossier_url": {"title": "Dossier Url", "minLength": 1, "maxLength": 65536, "format": "uri", "type": "string"}, "updated_at": {"title": "Updated At", "type": "string", "format": "date-time"}}, "required": ["semantic_document_export_request_uuid", "status", "updated_at"]}}}, "servers": []}
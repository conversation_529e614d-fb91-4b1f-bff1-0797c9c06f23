import json
import tempfile
from io import BytesIO
from pathlib import Path
from typing import Optional, Sequence, Callable, List

import PyPDF2
import requests
import structlog
from PyPDF2 import PdfWriter, PdfReader
from reportlab.pdfgen import canvas
from PyPDF2.generic import (
    DictionaryObject,
    NameObject,
    ArrayObject,
    NumberObject,
    FloatObject,
)
from pydantic import BaseModel
from reportlab.platypus.paragraph import Paragraph
from reportlab.platypus.frames import Frame
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle

from dossier.doc_cat_helpers import get_document_categories_by_name
from dossier.schemas import SemanticDocumentFullApiData
from dossier_zipper.helpers import rotate_pdf
from semantic_document.schemas_page_annotations import (
    AnnotationType,
    UserAnnotationsSchema,
)

logger = structlog.get_logger()

HYPODOSSIER_METADATA_SCHEMA_VERSION = "1.0.0"


class PdfSemanticDocumentMetadata(BaseModel):
    version: str
    uuid: str
    title: str
    # document_category_id: str
    document_category_name: str
    document_category_title_de: str
    document_category_title_en: str
    document_category_title_fr: str
    document_category_title_it: str
    # filename: str
    # confidence: float
    # creator: str
    # producer: str


def create_document_info_properties(data: PdfSemanticDocumentMetadata):
    return {
        # '/Subject': data.document_category_name,
        "/Title": data.title,
        "/Creator": "HypoDossier AG",
        "/Producer": "HypoDossier AG",
        # '/document_category_id': data.document_category_id,
        # '/document_category_name': data.document_category_name,
        # '/document_category_translated': data.document_category_translated,
        # '/confidence': str(data.confidence),
    }


def add_document_info_to_pdfwriter(
    writer: PdfWriter, document: SemanticDocumentFullApiData
):
    m = create_pdf_semantic_document_metadata(document)
    d = create_document_info_properties(m)
    writer.add_metadata(d)


def add_metadata_attachment_to_pdfwriter(
    writer: PdfWriter, document: SemanticDocumentFullApiData
):
    m = create_pdf_semantic_document_metadata(document)

    with tempfile.TemporaryDirectory() as temp_dir:
        metafile_path = Path(temp_dir) / "HypoDossierData.json"
        with open(metafile_path, "w") as f:
            j = json.dumps(m.model_dump(), indent=4)
            f.write(j)

        with open(metafile_path, "rb") as f:
            # txt_data = b'asdfasdfasdf'
            txt_data = f.read()
            writer.add_attachment(metafile_path.name, txt_data)


def create_pdf_semantic_document_metadata(document: SemanticDocumentFullApiData):
    document_categories = get_document_categories_by_name()
    doc_cat_name = document.document_category.name
    doc_cat = document_categories[doc_cat_name]
    title = (
        document.formatted_title
        if hasattr(document, "formatted_title")
        else document.title
    )

    return PdfSemanticDocumentMetadata(
        version=HYPODOSSIER_METADATA_SCHEMA_VERSION,
        title=title,
        # document_category_id=document.document_category.id,
        document_category_name=document.document_category.name,
        document_category_title_de=doc_cat.translated("de"),
        document_category_title_en=doc_cat.translated("en"),
        document_category_title_fr=doc_cat.translated("fr"),
        document_category_title_it=doc_cat.translated("it"),
        # filename=document.filename,
        # confidence=document.confidence,
        uuid=str(document.uuid),
        # creator='HypoDossier AG',
        # producer='HypoDossier AG',
    )


def hex_to_rgb_float(hex_color: str):
    """
    Convert a hexadecimal color string to RGB float tuple (R, G, B).
    Each value will be between 0 and 1.
    """
    hex_color = hex_color.lstrip("#")
    lv = len(hex_color)
    if lv != 6:
        raise ValueError(
            "Incorrect hex color format. Use 6-digit format without '#' prefix."
        )
    return tuple(int(hex_color[i : i + 2], 16) / 255.0 for i in range(0, lv, 2))


def normalize_newlines(text: str) -> str:
    """
    Normalize different types of newlines to a consistent format.

    Args:
        text: The text to normalize

    Returns:
        str: Text with normalized newlines
    """
    if not text:
        return ""
    return text.replace("\r\n", "\n").replace("\r", "\n").strip()


def prepare_text_for_reportlab(text: str) -> str:
    if not text:
        return ""
    else:
        t1 = normalize_newlines(text)
        t2 = t1.replace("\n", "<br/>")
        return t2


def check_text_overflow(
    text: str, frame_width: float, frame_height: float, style: ParagraphStyle
) -> bool:
    """
    Check if text will overflow the given frame dimensions.

    Args:
        text: The text to check
        frame_width: Width of the frame in points
        frame_height: Height of the frame in points
        style: ParagraphStyle to use for rendering

    Returns:
        bool: True if text overflows, False otherwise
    """
    if not text:
        return False

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Create paragraph with the text
    p = Paragraph(text, style)

    # Get the space required by the paragraph
    needed_width, needed_height = p.wrap(frame_width, frame_height)

    # If needed_height is zero, text couldn't be wrapped to fit width
    if needed_height == 0:
        return True

    # Check if text needs more height than available
    if needed_height > frame_height:
        return True

    return False


def calculate_required_height(
    text: str, frame_width: float, style: ParagraphStyle
) -> float:
    """
    Calculate the height needed to fit text within a given width.

    Args:
        text: The text to measure
        frame_width: Width of the frame in points
        style: ParagraphStyle to use for rendering

    Returns:
        float: Required height in points, or 0 if text cannot be wrapped at all
    """
    if not text:
        return 0

    # Normalize newlines before processing
    text = prepare_text_for_reportlab(text)

    # Create paragraph with the text
    p = Paragraph(text, style)

    # Use a large height for initial wrap to find required height
    _, needed_height = p.wrap(frame_width, 1000000)

    # Add extra margin to ensure text fits comfortably
    margin_factor = 1.05  # Add 5% extra space
    return needed_height * margin_factor


def add_annotations_to_page(
    page,
    annotations: List[UserAnnotationsSchema],
    writer: PyPDF2.PdfWriter,
    highlight_opacity: float = 0.5,
):
    """
    Helper function to add annotations to a PDF page
    @param page: PDF page object
    @param annotations: Pydantic model containing list of annotations
    @param writer: PDF writer instance
    @param highlight_opacity: Opacity level for highlights (0-1)
    """
    if not hasattr(page, "annotations"):
        page[NameObject("/Annots")] = ArrayObject()

    for annotation in annotations:
        page_height = float(page.mediabox.height)
        page_width = float(page.mediabox.width)

        # Scale bbox values to absolute coordinates
        abs_top = annotation.bbox_top * page_height
        abs_height = annotation.bbox_height * page_height
        abs_left = annotation.bbox_left * page_width
        abs_width = annotation.bbox_width * page_width

        # PDF coordinate system with bottom-left origin:
        bottom = page_height - (abs_top + abs_height)
        top = page_height - abs_top
        left = abs_left
        right = abs_left + abs_width

        # Convert the hex color to RGB float values
        rgb_color = hex_to_rgb_float(annotation.hexcolor)

        if annotation.annotation_type == AnnotationType.HIGHLIGHT:
            highlight_dict = DictionaryObject()

            highlight_dict.update(
                {
                    NameObject("/Type"): NameObject("/Annot"),
                    NameObject("/Subtype"): NameObject("/Highlight"),
                    NameObject("/F"): NumberObject(4),
                    NameObject("/Rect"): ArrayObject(
                        [
                            FloatObject(left),
                            FloatObject(bottom),
                            FloatObject(right),
                            FloatObject(top),
                        ]
                    ),
                    NameObject("/QuadPoints"): ArrayObject(
                        [
                            FloatObject(left),
                            FloatObject(top),
                            FloatObject(right),
                            FloatObject(top),
                            FloatObject(left),
                            FloatObject(bottom),
                            FloatObject(right),
                            FloatObject(bottom),
                        ]
                    ),
                    NameObject("/C"): ArrayObject([FloatObject(c) for c in rgb_color]),
                    NameObject("/CA"): FloatObject(highlight_opacity),
                }
            )

            # Add the annotation directly to the page's annotations array
            if not isinstance(page.get("/Annots"), ArrayObject):
                page[NameObject("/Annots")] = ArrayObject()

            # Add the annotation to the writer and get a reference
            highlight_ref = writer._add_object(highlight_dict)
            page["/Annots"].append(highlight_ref)

        if annotation.annotation_type == AnnotationType.COMMENT:
            # Create a rectangle overlay with text using reportlab
            packet = BytesIO()

            # Set up text rendering style first
            styles = getSampleStyleSheet()
            style = styles["Normal"]
            style.fontSize = 10  # Set font size to 10
            style.textColor = (0, 0, 0)  # Black text
            style.fontName = "Helvetica"  # Use Helvetica as it's one of the specified fonts and is widely supported in PDFs

            # Calculate required dimensions with padding
            padding = 5
            frame_width = abs_width - (2 * padding)
            frame_height = abs_height - (2 * padding)

            # Check if text will overflow and calculate new height if needed
            if check_text_overflow(
                annotation.text or "", frame_width, frame_height, style
            ):
                required_height = calculate_required_height(
                    annotation.text or "", frame_width, style
                )
                if (
                    required_height > 0
                ):  # Only adjust if we can calculate a valid height
                    # Add padding to required height
                    new_height = required_height + (2 * padding)

                    # Convert back to relative coordinates
                    new_rel_height = new_height / page_height

                    # Log the height adjustment
                    logger.info(
                        "Adjusting comment box height to fit text",
                        annotation_group_uuid=annotation.annotation_group_uuid,
                        original_height=annotation.bbox_height,
                        new_height=new_rel_height,
                    )

                    # Update dimensions
                    abs_height = new_height
                    frame_height = abs_height - (2 * padding)
                    # Recalculate bottom coordinate since height changed
                    bottom = page_height - (abs_top + abs_height)

            # Create frame for text
            frame = Frame(
                left + padding,
                bottom + padding,
                frame_width,
                frame_height,
                showBoundary=0,
            )

            # Create paragraph with automatic text wrapping
            text = prepare_text_for_reportlab(annotation.text)
            p = Paragraph(text, style)

            # Use a smaller height if possible by calculation
            needed_width, needed_height = p.wrap(frame_width, frame_height * 1000)
            if needed_height < frame_height:
                # we can make the yellow box less high
                random_extra_margin_factor = 3
                abs_height_adjusted = (
                    needed_height + (2 * padding) * random_extra_margin_factor
                )
                abs_height = abs_height_adjusted
                bottom_new = page_height - (abs_top + abs_height)
                if bottom_new > bottom:
                    logger.info(
                        "adjusted smaller height of comment box",
                        abs_height=abs_height,
                        abs_height_adjusted=abs_height_adjusted,
                        bottom_new=bottom_new,
                        bottom=bottom,
                    )
                    bottom = bottom_new

            # First draw the background rectangle
            c = canvas.Canvas(packet, pagesize=(page_width, page_height))
            c.setFillColorRGB(*rgb_color)  # Use the annotation's hexcolor
            c.setFillAlpha(1)  # Full opacity for rectangle
            c.rect(left, bottom, abs_width, abs_height, fill=1, stroke=0)

            # Add the paragraph to the frame
            frame.addFromList([p], c)

            c.save()

            # Create PDF from the overlay
            packet.seek(0)
            overlay = PdfReader(packet)

            # Merge the overlay with the page
            page.merge_page(overlay.pages[0])


def create_semantic_document_pdf(
    semantic_document,
    pages: Sequence,
    get_pdf_url_func,
    get_annotations_func: Optional[Callable] = None,
    add_metadata: bool = True,
    writer: Optional[PyPDF2.PdfWriter] = None,
) -> PyPDF2.PdfWriter:
    if writer is None:
        writer = PyPDF2.PdfWriter()

    for page in pages:
        pdf_page_url = get_pdf_url_func(page)
        response = requests.get(pdf_page_url)
        pdf_reader = PyPDF2.PdfReader(BytesIO(response.content))
        pdf_page = pdf_reader.pages[0]
        rotation_angle = getattr(page, "rotation_angle", 0)

        # Apply rotation if needed
        rotated_page = rotate_pdf(pdf_page, rotation_angle)

        # Add annotations before adding the page to the writer
        if get_annotations_func:
            annotations = get_annotations_func(page)
            if annotations and len(annotations) > 0:
                add_annotations_to_page(rotated_page, annotations, writer)

        # Add the page to the writer
        writer.add_page(rotated_page)

    if add_metadata:
        add_metadata_attachment_to_pdfwriter(writer, semantic_document)

    filename = getattr(semantic_document, "filename", None) or semantic_document.title
    writer.add_outline_item(filename, 0)

    return writer

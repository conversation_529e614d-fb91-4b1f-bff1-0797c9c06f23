{"openapi": "3.0.0", "info": {"title": "Open API Spec", "version": "1.0.0"}, "servers": [{"url": "${url}"}], "paths": {"/partnerExtern/hasDatenraumrecht": {"get": {"summary": "Check if partner has the necessary rights.", "operationId": "hasDatenraumrecht", "tags": ["partner.intern.PartnerExternService"], "parameters": [{"name": "partnerUuid", "in": "query", "required": true, "schema": {"type": "string", "minLength": 0, "maxLength": 254}}, {"name": "logonId", "in": "query", "required": true, "schema": {"type": "string", "minLength": 0, "maxLength": 254}}], "responses": {"200": {"description": "Success with content", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "default": {"description": "Error response with exception details", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/slx.global.ServiceException"}, {"$ref": "#/components/schemas/slx.global.ValidationException"}], "discriminator": {"propertyName": "_class"}}}}}}}}}, "components": {"schemas": {"eai.ContractException": {"description": "Common abstract base-class for all exceptions.", "type": "object", "properties": {"_class": {"type": "string", "description": "Discriminator field"}}, "required": ["_class"]}, "slx.global.ServiceException": {"description": "A concrete exception with message support.", "allOf": [{"$ref": "#/components/schemas/eai.ContractException"}, {"type": "object", "properties": {"msg": {"$ref": "#/components/schemas/slx.global.Msg"}}, "required": ["msg"]}]}, "slx.global.Msg": {"description": "A message abstraction consisting of a message lookup key / template with placeholders and optional arguments for formatting. Also contains a pre-translated message if clients dont want to localize messages by themself.", "type": "object", "properties": {"key": {"type": "string", "description": "The message key or message template or message"}, "args": {"type": "array", "items": {"type": "string"}, "description": "The message arguments"}, "message": {"type": "string", "description": "The server-side localized and formatted message"}}, "required": ["key", "message"]}, "slx.global.ValidationException": {"description": "A validation exception will be thrown if one or more validation rules have been violated", "allOf": [{"$ref": "#/components/schemas/slx.global.ServiceException"}, {"type": "object", "properties": {"failures": {"type": "array", "items": {"$ref": "#/components/schemas/slx.global.ValidationFailure"}, "description": "The list of validation failures"}}}]}, "slx.global.ValidationFailure": {"description": "A single validation failure with a message describing the violated constraint", "type": "object", "properties": {"msg": {"$ref": "#/components/schemas/slx.global.Msg"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/slx.global.PropertyFailure"}, "description": "The list of properties to blame"}}, "required": ["msg"]}, "slx.global.PropertyFailure": {"description": "A single property failure that identifies the qualified path to the property", "type": "object", "properties": {"path": {"type": "string", "description": "The qualified path to the property"}, "value": {"type": "string", "description": "The property value as string"}, "proposal": {"type": "string", "description": "The optional proposal for a new, valid value as string"}}, "required": ["path"]}}}}
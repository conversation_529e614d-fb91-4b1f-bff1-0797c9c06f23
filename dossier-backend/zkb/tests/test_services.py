import datetime
from functools import partial

import pytest
from django.contrib.auth.models import AnonymousUser
from ninja.errors import AuthenticationError
from django.test import RequestFactory

from core.authentication import AuthenticatedClient
from dossier.dossier_access_external import create_or_update_access_grant
from dossier.helpers_access_check import (
    get_dossier_from_request_with_access_check,
    account_specific_access_checks,
    check_account_specific_access_check,
)
from dossier.models import JWK, DossierAccessCheckProvider, DossierAccessGrant
from dossier.services import create_dossier
from projectconfig.authentication import JWTAuth
from zkb.services import (
    check_dossier_access_external_service,
    external_service_check,
)
from django.utils import timezone
from unittest.mock import AsyncMock
import structlog

from zkb.tests.factories import ZKBAccountFactoryFaker
from zkb.external_auth import (
    check_zkb_dossier_access,
    check_zkb_dossier_access_external_always_true,
)

logger = structlog.get_logger()


def mock_service_granted(user_id, dossier_id, url):
    return True


def mock_service_denied(user_id, dossier_id, url):
    return False


def mock_service_error(user_id, dossier_id, url):
    raise Exception("Some Error")


@pytest.mark.django_db
def test_cached_has_access(zkb_account, zkb_user):
    now = timezone.now()
    expiration = now + datetime.timedelta(minutes=10)
    dossier = create_dossier(
        account=zkb_account,
        dossier_name="dossier example",
        language="de",
        owner=zkb_user,
    )
    create_or_update_access_grant(
        user=zkb_user, dossier=dossier, expires_at=expiration, has_access=True
    )

    assert (
        check_dossier_access_external_service(
            user=zkb_user,
            user_id="1234",
            dossier=dossier,
            external_service_check_callable=mock_service_granted,
            url="http://auth.hypo.duckdns.org",
        )
        is True
    )


@pytest.mark.django_db
def test_cached_access_denied(zkb_account, zkb_user):
    now = timezone.now()
    expiration = now + datetime.timedelta(minutes=10)
    dossier = create_dossier(
        account=zkb_account,
        dossier_name="dossier example",
        language="de",
        owner=zkb_user,
    )
    create_or_update_access_grant(
        user=zkb_user, dossier=dossier, expires_at=expiration, has_access=False
    )
    assert (
        check_dossier_access_external_service(
            user=zkb_user,
            user_id="1234",
            dossier=dossier,
            external_service_check_callable=mock_service_denied,
            url="http://auth.hypo.duckdns.org",
        )
        is False
    )
    assert DossierAccessGrant.objects.filter(
        user=zkb_user, dossier=dossier, has_access=False
    ).exists()


@pytest.mark.django_db
def test_external_service_grants_access(zkb_account, zkb_user):
    dossier = create_dossier(
        account=zkb_account,
        dossier_name="dossier example",
        language="de",
        owner=zkb_user,
    )
    assert (
        check_dossier_access_external_service(
            user=zkb_user,
            user_id="1234",
            dossier=dossier,
            external_service_check_callable=mock_service_granted,
            url="http://auth.hypo.duckdns.org",
        )
        is True
    )
    assert DossierAccessGrant.objects.filter(
        user=zkb_user, dossier=dossier, has_access=True
    ).exists()


@pytest.mark.django_db
def test_external_service_denies_access(zkb_account, zkb_user):
    dossier = create_dossier(
        account=zkb_account,
        dossier_name="dossier example",
        language="de",
        owner=zkb_user,
    )
    assert (
        check_dossier_access_external_service(
            user=zkb_user,
            user_id="1234",
            dossier=dossier,
            external_service_check_callable=mock_service_denied,
            url="http://auth.hypo.duckdns.org",
        )
        is False
    )
    assert (
        DossierAccessGrant.objects.filter(user=zkb_user, dossier=dossier).exists()
        is False
    )


@pytest.mark.django_db
def test_external_service_error(zkb_account, zkb_user):
    dossier = create_dossier(
        account=zkb_account,
        dossier_name="dossier example",
        language="de",
        owner=zkb_user,
    )
    assert (
        check_dossier_access_external_service(
            user=zkb_user,
            user_id="1234",
            dossier=dossier,
            external_service_check_callable=mock_service_error,
            url="http://auth.hypo.duckdns.org",
        )
        is False
    )
    assert not DossierAccessGrant.objects.filter(
        user=zkb_user, dossier=dossier
    ).exists()


@pytest.fixture
def mock_async_client(mocker):
    mock = mocker.patch("httpx.AsyncClient")
    mock.return_value.__aenter__.return_value = AsyncMock()
    return mock.return_value.__aenter__.return_value


async def test_external_service_check_bool_response(mock_async_client):
    mock_async_client.get.return_value = AsyncMock(
        headers={"content-type": "application/json"}, json=lambda: True
    )

    response = await external_service_check("some_partner_uuid", "some_logon_id")
    assert response is True


def test_dossier_access(
    db,
    zkb_account_factory: ZKBAccountFactoryFaker,
    zkb_authenticated_client: AuthenticatedClient,
    rf: RequestFactory,
    mock_token,
    mock_jwks_public_private,
    django_assert_num_queries,
):
    dossier_access_check = DossierAccessCheckProvider.objects.create(
        name="dossier-access-check"
    )
    account_specific_access_checks[dossier_access_check.name] = (
        check_zkb_dossier_access_external_always_true
    )
    account = zkb_account_factory.account
    account.dossier_access_check_provider = dossier_access_check
    account.save()

    dossier = zkb_account_factory.create_dossier()

    user_id = zkb_account_factory.faker.user_name()
    external_dossier_id = dossier.external_id
    assert external_dossier_id is not None

    JWK.objects.all().delete()

    jwks = JWK.objects.create(
        account=account,
        jwk=mock_jwks_public_private["keys"][1],
        enabled=True,
    )

    assert jwks.account.key == "zkbd"

    # check that the dossier is accessible for user_id and external_dossier_id in the external service allows access
    request = rf.get(f"/api/v1/dossiers/{dossier.uuid}")
    request.user = AnonymousUser()
    request.auth = JWTAuth().authenticate(request, mock_token)
    # AttributeError: 'WSGIRequest' object has no attribute 'jwt'
    # type(request) == django.core.handlers.wsgi.WSGIRequest
    assert request.jwt is not None

    request.jwt["UserID"] = user_id

    assert account.dossier_access_check_provider is not None
    access_cache_entries = list(DossierAccessGrant.objects.all())
    assert len(access_cache_entries) == 0

    with django_assert_num_queries(24):
        assert (
            get_dossier_from_request_with_access_check(request, dossier.uuid) == dossier
        )

    with django_assert_num_queries(11):
        assert (
            check_account_specific_access_check(
                request.jwt,
                dossier.uuid,
                dossier_user=request.auth,
                is_manager=False,
            )
            == dossier
        )

    # There was one successful access check so this should be in the cache
    access_cache_entries_after_check = list(DossierAccessGrant.objects.all())
    assert len(access_cache_entries_after_check) == 1
    assert access_cache_entries_after_check[0].has_access is True

    # check that the dossier is not accessible for user_id and the external_dossier_id if the external service denies access
    DossierAccessGrant.objects.all().delete()
    account_specific_access_checks[dossier_access_check.name] = partial(
        check_zkb_dossier_access, external_authorization=mock_service_denied
    )

    with pytest.raises(AuthenticationError):
        get_dossier_from_request_with_access_check(request, dossier.uuid)

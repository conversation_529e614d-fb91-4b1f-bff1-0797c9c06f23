import json
import random
import uuid
from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import List

import django
import jwt
import pytest
import structlog
from asgiref.sync import async_to_sync
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AbstractUser
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test.client import Client
from django.urls import reverse
from django.utils import timezone
from freezegun import freeze_time
from jwcrypto import jwk
from pydantic import TypeAdapter
from pytest_mock import MockerFixture

import dossier.schemas as dossier_schemas
from conftest import prepare_demo_dossier_for_account
from core.authentication import AuthenticatedClient
from dossier.conftest import create_synthetic_dossier
from dossier.fakes import (
    add_some_fake_semantic_documents,
    get_law_files,
)
from dossier.models import (
    <PERSON><PERSON><PERSON>,
    OriginalFile,
    DocumentCategory,
    RealestateProperty,
    <PERSON>ssierF<PERSON>,
    Account,
    FileStatus,
)
from dossier.schemas import Language, Message
from dossier_zipper.conftest import mocked_get_dossier  # noqa: F401
from processed_file.schemas import PageObjectSchema
from projectconfig.authentication import get_user_or_create
from semantic_document import (
    schemas as semantic_document_schemas,
)
from semantic_document.models import (
    SemanticDocument,
    AssignedRealEstatePropertySemanticDocument,
    SemanticPage,
)
from workers.models import SemanticDocumentExport
from workers.schemas import SemanticDocumentPDFRequestV1
from workers.workers import process_semantic_dossier_pdf_request
from zkb.schemas import schemas
from zkb.tests.data.unique_page_objects_per_sample_dossier_fixture import (
    unique_page_objects_per_sample_dossier_fixture,
)

User: AbstractUser = get_user_model()


logger = structlog.get_logger(__name__)


pytestmark = pytest.mark.django_db


def test_openapi_without_auth(client: django.test.client.Client):
    res = client.get(reverse("zkbopenapiwithoutauth"))
    assert res.status_code == 200
    assert "securitySchemes" not in res.json()


def test_ping(zkb_authenticated_client, zkb_account, set_zkb_JWK):
    res = zkb_authenticated_client.get(reverse("zkb-api:ping"))
    assert res.status_code == 200
    assert Message.model_validate_json(res.content) == Message(detail="pong")


def test_create_dossier_api_success(zkb_authenticated_client, zkb_account, set_zkb_JWK):
    # Base case to check whether we can create a dossier
    url = reverse("zkb-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    result = zkb_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id, account__key="zkbd")
    parsed = schemas.Dossier.model_validate_json(result.content)
    assert parsed.external_dossier_id == external_dossier_id
    assert parsed.uuid == dossier.uuid

    # Test for conflict when dossier already exists
    result = zkb_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )
    assert result.status_code == 409


def test_update_dossier_api_success(zkb_authenticated_client, zkb_account, set_zkb_JWK):
    # Base case to check whether we can create a dossier
    external_dossier_id = str(uuid.uuid4())

    result = zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    # Test changing two params
    result = zkb_authenticated_client.patch(
        path=reverse(
            "zkb-api:update-dossier",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 2",
            external_dossier_id=external_dossier_id,
            lang=Language.fr,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 2"
    assert dossier.lang == "fr"

    # Test changing one param
    result = zkb_authenticated_client.patch(
        path=reverse(
            "zkb-api:update-dossier",
            kwargs=dict(external_dossier_id=external_dossier_id),
        ),
        data=schemas.ChangeDossier(
            name="Test Dossier 3",
            external_dossier_id=external_dossier_id,
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    dossier = Dossier.objects.get(external_id=external_dossier_id)
    assert dossier.name == "Test Dossier 3"
    # lang should not be changed
    assert dossier.lang == "fr"


def test_create_to_delete_dossier_api_success(
    zkb_authenticated_client, zkb_account, set_zkb_JWK
):
    # Test creating and deleting a dossier via API
    url = reverse("zkb-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    result = zkb_authenticated_client.post(
        path=url,
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    assert Dossier.objects.get(
        external_id=external_dossier_id,
        account__key=zkb_account.key,
    )

    result = zkb_authenticated_client.delete(
        path=reverse(
            "zkb-api:dossier-delete",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
    )

    assert result.status_code == 202

    # Setting expiry date to past causes object to not be visible
    # as default dossier model manager has been overwritten
    assert (
        Dossier.objects.filter(
            external_id=external_dossier_id,
            account__key=zkb_account.key,
        ).count()
        == 0
    )


def test_delete_dossier_api_failure(zkb_authenticated_client, set_zkb_JWK):
    # Expect failure when trying to delete a dossier that does not exist
    result = zkb_authenticated_client.delete(
        path=reverse(
            "zkb-api:dossier-delete",
            kwargs={"external_dossier_id": str(uuid.uuid4())},
        )
    )

    assert result.status_code == 404


def test_add_original_file(
    zkb_authenticated_client, zkb_account, mocker: MockerFixture, set_zkb_JWK
):
    """
    Test the behavior of the 'add-original-file' API endpoint for adding original files to a dossier.

    Specifically, it verifies:

    Successful file upload and its subsequent processing.
    Default prevention of duplicate file uploads.
    Option to allow and rename duplicate files.
    """
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    assert (
        RealestateProperty.objects.filter(
            assignedrealestatepropertyoriginalfile__originalfile=original_file
        ).exists()
        is False
    )

    # Check correct association with dossier
    assert original_file.dossier.external_id == external_dossier_id

    # Test checking if file exists by filename, these should be refactored into separate test
    assert zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:check-original-filename",
            kwargs={
                "external_dossier_id": str(external_dossier_id),
                "original_filename": original_file.file.name,
            },
        ),
    ).json()

    assert (
        zkb_authenticated_client.get(
            path=reverse(
                "zkb-api:check-original-filename",
                kwargs={
                    "external_dossier_id": str(external_dossier_id),
                    "original_filename": "wrong_filename.pdf",
                },
            ),
        ).json()
        is False
    )

    # same file should not be allowed by default
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 409

    assert (
        "The file test_page1.jpg already exists in the dossier"
        in response.json()["detail"]
    )

    # Use parameter to allow duplicate files
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
        data={"file": file, "allow_duplicate_and_rename": True},
        format="multipart",
    )

    assert response.status_code == 201
    duplicate_and_rename_original_file_response = (
        dossier_schemas.CreatedObjectReference(**response.json())
    )

    duplicate_and_rename_original_file = OriginalFile.objects.get(
        uuid=duplicate_and_rename_original_file_response.uuid
    )

    # Check that file model objects are different
    assert original_file.uuid != duplicate_and_rename_original_file.uuid

    # Check that new duplicate file has new name
    assert original_file.file.name != duplicate_and_rename_original_file.file.name

    # Check that they belong to the same dossier
    assert original_file.dossier == duplicate_and_rename_original_file.dossier


@pytest.mark.parametrize(
    ("entities", "created"),
    [
        ({"entity_type": "realestateproperty", "entity_key": "mansion"}, True),
        ({"entity_type": "", "entity_key": ""}, False),
        ({}, False),
    ],
)
def test_add_original_file_realestate_success(
    zkb_authenticated_client,
    zkb_account,
    mocker: MockerFixture,
    set_zkb_JWK,
    entities,
    created,
):
    # Test adding with real estate property
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    property_entity = RealestateProperty.objects.create(
        dossier=Dossier.objects.get(external_id=external_dossier_id), key="mansion"
    )

    mocker.patch("dossier.services_external.process_original_file")
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={
            "file": file,
            **entities,
        },
        format="multipart",
    )

    assert response.status_code == 201

    created_original_file = OriginalFile.objects.get(uuid=response.json()["uuid"])

    assert (
        created
        == RealestateProperty.objects.filter(
            assignedrealestatepropertyoriginalfile__originalfile=created_original_file
        ).exists()
    )

    if created:
        assert (
            RealestateProperty.objects.filter(
                assignedrealestatepropertyoriginalfile__originalfile=created_original_file
            ).first()
            == property_entity
        )


def test_add_original_file_realestate_no_existing_key(
    zkb_authenticated_client, zkb_account, mocker: MockerFixture, set_zkb_JWK
):
    # Test the case where the real estate key doesn't exist
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    file = SimpleUploadedFile(
        "test_page1.jpg", b"file_content1", content_type="image/jpeg"
    )

    assert (
        RealestateProperty.objects.filter(
            dossier=Dossier.objects.get(external_id=external_dossier_id), key="wrongkey"
        ).exists()
        is False
    )

    mocker.patch("dossier.services_external.process_original_file")
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={
            "file": file,
            "entity_type": "realestateproperty",
            "entity_key": "mansion",
        },
        format="multipart",
    )

    assert response.status_code == 201

    assert RealestateProperty.objects.filter(
        dossier=Dossier.objects.get(external_id=external_dossier_id), key="mansion"
    ).exists()


def test_get_file_status(
    zkb_authenticated_client,
    zkb_account,
    document_categories,
    mocker: MockerFixture,
    set_zkb_JWK,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"
    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    # Upload a file
    mocker.patch("dossier.services_external.process_original_file")
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.headers["Content-Type"] == "application/json; charset=utf-8"

    OriginalFile.objects.get(uuid=response.json()["uuid"])

    random.seed(42)
    add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False, num_docs=5
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:file-status",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.headers["Content-Type"] == "application/json; charset=utf-8"

    parsed = schemas.DossierProcessingStatus.model_validate_json(response.content)
    assert parsed.dossier_uuid == dossier.uuid
    assert len(parsed.original_files) == 2

    for original_file in parsed.original_files:
        original_file_object = OriginalFile.objects.get(uuid=original_file.uuid)
        extracted_files = original_file_object.extractedfile_set.all()

        assert len(original_file.extracted_files) == len(extracted_files)

        for extracted_file in extracted_files:
            assert extracted_file.uuid in [
                extracted_file.uuid for extracted_file in original_file.extracted_files
            ]

    # Check that 50% of original files are processed
    assert parsed.progress == 50


def test_get_dossier_details_api(zkb_authenticated_client, zkb_account, set_zkb_JWK):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-details",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    parsed = schemas.Dossier.model_validate_json(response.content)
    assert parsed.external_dossier_id == external_dossier_id

    assert (
        zkb_authenticated_client.get(
            path=reverse(
                "zkb-api:dossier-details",
                kwargs={"external_dossier_id": str(uuid.uuid4())},
            )
        ).status_code
        == 404
    )

    # Set the dossier to be expired
    dossier = Dossier.objects.get(external_id=external_dossier_id)
    dossier.expiry_date = timezone.now() - timedelta(days=1)
    dossier.save()
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-details",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.status_code == 404


def test_get_semantic_documents_api_success(
    zkb_authenticated_client, zkb_account, document_categories, set_zkb_JWK
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    # Select a semantic document to test
    semantic_document_with_property = semantic_documents[0]
    # Create a real estate property
    real_estate_property = RealestateProperty.objects.create(
        dossier=dossier, key="zkb_super_secret_mansion"
    )

    # and assign it to the semantic document
    AssignedRealEstatePropertySemanticDocument.objects.create(
        semantic_document=semantic_document_with_property,
        realestate_property=real_estate_property,
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )

    assert response.status_code == 200

    assert ("null" in str(response.content)) is False

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_python(
        response.json()
    )

    assert len(semantic_documents) == len(parsed)

    assert parsed[0].semantic_document_date

    documents_set = {doc.uuid for doc in parsed}
    for semantic_document in semantic_documents:
        assert semantic_document.uuid in documents_set

    response = zkb_authenticated_client.get(
        path=f"{reverse('zkb-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)
    documents_set = {doc.uuid for doc in parsed}
    pages_set = {page.uuid for doc in parsed for page in doc.semantic_pages}

    for semantic_document in semantic_documents:
        assert semantic_document.uuid in documents_set
        assert semantic_document.access_mode.name == "READ_WRITE"
        for page in semantic_document.semantic_pages.all():
            assert page.uuid in pages_set

    # Test that the real estate property is returned
    for semantic_document in parsed:
        assert semantic_document.document_category_confidence
        assert semantic_document.document_category_key
        assert semantic_document.document_category_key != ""
        if semantic_document.uuid == semantic_document_with_property.uuid:
            assert semantic_document.entity_key == "zkb_super_secret_mansion"
            assert semantic_document.entity_type == "realestateproperty"
        else:
            assert semantic_document.entity_key is None


# This test is periodically flaky and I'm not sure why. If it gets annoying disable it
def test_get_semantic_documents_api_success_deep_last_update(
    zkb_authenticated_client, zkb_account, document_categories, set_zkb_JWK
):
    """Test that the last_update field is correctly set on the semantic documents and pages"""
    external_dossier_id = str(uuid.uuid4())

    current_time = timezone.now()

    # Step 1: 10 min ago we create a dossier with some documents

    current_minus_10 = current_time - timedelta(minutes=10)
    with freeze_time(current_minus_10):
        fixed_time = current_minus_10
        # Create a dossier
        assert (
            zkb_authenticated_client.post(
                path=reverse("zkb-api:create-dossier"),
                data=schemas.CreateDossier(
                    name="Test Dossier",
                    external_dossier_id=external_dossier_id,
                    language="de",
                ).model_dump_json(),
                content_type="application/json",
            ).status_code
            == 201
        )

        dossier = Dossier.objects.get(external_id=external_dossier_id)

        add_some_fake_semantic_documents(dossier=dossier, allow_empty_docs=False)

        response = zkb_authenticated_client.get(
            path=reverse(
                "zkb-api:semantic-documents",
                kwargs={"external_dossier_id": external_dossier_id},
            )
        )

        assert response.status_code == 200

        parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
            response.content
        )

        # difference in timestamps might be a few milliseconds negative due to rounding
        assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

        # Last change to the document was by creating it which sets the updated_at
        assert parsed[0].last_change == parsed[0].updated_at

    # Step 2: 5 min ago: soft delete a page which will change last_change but not updated_at

    current_minus_5 = current_time - timedelta(minutes=5)
    with freeze_time(current_minus_5):
        fixed_time = current_minus_5

        semantic_document = SemanticDocument.objects.get(uuid=parsed[0].uuid)

        # Soft delete a page
        first_page = semantic_document.semantic_pages.first()
        first_page.deleted_at = current_minus_5
        first_page.save()

        response = zkb_authenticated_client.get(
            path=reverse(
                "zkb-api:semantic-documents",
                kwargs={"external_dossier_id": external_dossier_id},
            )
        )

        assert response.status_code == 200

        parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(
            response.content
        )

        # The updated_at did not change since creation of the document but the last_change
        # changed due to the soft delete of a page

        # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
        # assert (parsed[0].last_change - parsed[0].updated_at) > timedelta(minutes=4)

        # Soft delete happened in less than 2 seconds
        # difference in timestamps might be a few milliseconds negative due to rounding
        assert (parsed[0].last_change - fixed_time) < timedelta(seconds=2)

        # Last change happened around 5 minutes before 'now'
        # TODO: Figure out why this does work locally but "sometimes" not on staging (fails in 50% of tries)
        # assert (current_time - parsed[0].last_change) > timedelta(minutes=4)


def test_update_semantic_documents_api_success(
    zkb_authenticated_client, zkb_account, document_categories, set_zkb_JWK
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(
        dossier=dossier, allow_empty_docs=False
    )

    semantic_document = semantic_documents[0]

    assert semantic_document.access_mode.name == "READ_WRITE"

    assert semantic_document.external_semantic_document_id is None

    response = zkb_authenticated_client.patch(
        path=reverse(
            "zkb-api:update-semantic-document",
            kwargs={
                "external_dossier_id": external_dossier_id,
                "semantic_document_uuid": semantic_document.uuid,
            },
        ),
        data=schemas.SemanticDocumentUpdate(
            external_semantic_document_id="Test external document",
            access_mode="read_only",
        ).model_dump_json(),
        content_type="application/json",
    )

    assert response.status_code == 200

    parsed = schemas.SemanticDocument.model_validate_json(response.content)

    assert parsed.access_mode == "read_only"
    assert parsed.external_semantic_document_id == "Test external document"


def test_get_semantic_documents_api_failure(
    zkb_authenticated_client, zkb_account, document_categories, set_zkb_JWK
):
    # Test the case where the dossier does not exist
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-documents",
            kwargs={"external_dossier_id": str(uuid.uuid4())},
        )
    )

    assert response.status_code == 404

    external_dossier_id = str(uuid.uuid4())

    # Create a dossier, but don't add any semantic documents
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    response = zkb_authenticated_client.get(
        path=f"{reverse('zkb-api:semantic-documents', kwargs={'external_dossier_id': external_dossier_id})}?show_pages=true",
    )

    assert response.status_code == 200
    assert response.json() == []


def test_get_document_categories_api_success(
    zkb_authenticated_client, zkb_account, document_categories, set_zkb_JWK
):
    response = zkb_authenticated_client.get(
        path=reverse("zkb-api:document-categories"),
    )

    assert response.status_code == 200

    # Check we can parse the response
    schemas.DocumentCategories.model_validate_json(response.content)

    for document_category in document_categories:
        if document_category.account.key == "zkb":
            assert document_category.name in response.json().keys()


def test_get_document_categories_api_empty(
    zkb_authenticated_client, zkb_account, set_zkb_JWK
):
    # Test the case where there are no document categories
    DocumentCategory.objects.all().delete()
    response = zkb_authenticated_client.get(
        path=reverse("zkb-api:document-categories"),
    )

    assert response.status_code == 200

    assert response.json() == {}


def test_export_dossier_semantic_document_pdf_success(
    mocked_get_dossier,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
    set_zkb_JWK,
    mocker: MockerFixture,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    # def sync_process_semantic_dossier_pdf_request_actor(
    #     message: bytes,
    #     routing_key: str,
    #     exchange: str = "",
    #     properties: pika.spec.BasicProperties = None,
    # ):
    #     # print(message)
    #     #
    #     # message_adapter = Mock()
    #     #
    #     # message_adapter.type = "test_type"
    #     # message_adapter.body = "message"
    #
    #     incoming_message: aio_pika.Message = aio_pika.Message(body=message)
    #
    #     # body=message, properties=properties
    #     return async_to_sync(process_semantic_dossier_pdf_request_actor)(
    #         incoming_message
    #     )
    #
    # mock_publich = mocker.patch(
    #     "zkb.api.publish",
    #     side_effect=sync_process_semantic_dossier_pdf_request_actor,
    # )

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "zkb.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = zkb_authenticated_client.post(
        path=reverse(
            "zkb-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 200
    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    semantic_document_export = SemanticDocumentExport.objects.get(
        uuid=schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
    )

    assert semantic_document_export.file
    assert semantic_document_export.file.get_fast_url()

    # Check we can poll for dossier status
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-semantic-document-export-status",
            kwargs={
                "semantic_document_export_request_uuid": str(
                    semantic_document_export.uuid
                )
            },
        ),
    )

    assert response.status_code == 200

    parse = schemas.ExportStatus.model_validate_json(response.content)
    assert parse.semantic_document_export_request_uuid == semantic_document_export.uuid
    assert parse.status == "PROCESSING"
    # These are set to none as export_semantic_document.done is None
    assert parse.dossier_url is None
    assert parse.dossier_file_uuid is None


def test_export_dossier_semantic_document_pdf_auth_failure(
    mocked_get_dossier,
    zkb_authenticated_client,
    swissfex_miss_signed_authenticated_client,
    zkb_account,
    document_categories,
    set_zkb_JWK,
    mocker: MockerFixture,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "zkb.api.publish",
        side_effect=mock_publish_side_effect,
    )

    # Request to generate an export
    response = swissfex_miss_signed_authenticated_client.post(
        path=reverse(
            "zkb-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert response.status_code == 401
    mock_dispatch_publish_request.assert_not_called()


def test_dossier_end_to_end(
    mocked_get_dossier,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
    mocker: MockerFixture,
    set_zkb_JWK,
):
    # Test the whole process of creating a dossier, adding documents, exporting and checking status
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    file = SimpleUploadedFile(
        file_path.name, file_path.read_bytes(), content_type="application/octet-stream"
    )

    process_original_file_mock = mocker.patch(
        "dossier.services_external.process_original_file"
    )
    response = zkb_authenticated_client.post(
        reverse(
            "zkb-api:add-original-file",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data={"file": file},
        format="multipart",
    )

    assert response.status_code == 201
    original_file = dossier_schemas.CreatedObjectReference(**response.json())

    original_file = OriginalFile.objects.get(uuid=original_file.uuid)

    assert process_original_file_mock.call_args_list[0].args[0] == original_file

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    def mock_publish_side_effect(*args, **kwargs):
        request = SemanticDocumentPDFRequestV1.model_validate_json(kwargs["message"])

        process_semantic_dossier_pdf_request(semantic_document_pdf_request=request)

    mock_dispatch_publish_request = mocker.patch(
        "zkb.api.publish",
        side_effect=mock_publish_side_effect,
    )

    response = zkb_authenticated_client.post(
        path=reverse(
            "zkb-api:export-dossier-semantic-document-pdf",
            kwargs={
                "semantic_document_uuid": str(semantic_document.uuid),
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    mock_dispatch_publish_request.assert_called_once()

    assert response.status_code == 200

    assert SemanticDocumentExport.objects.get(
        uuid=schemas.SemanticDocumentPDFExportRequest.model_validate_json(
            response.content
        ).uuid
    )


@pytest.mark.parametrize(
    ("token_overwrite", "expected"),
    [
        ({}, 201),  # Base case
        ({"user_roles": ""}, 401),  # No user roles
        ({"account_key": "wrong"}, 401),  # Wrong account key
        ({"exp": 1}, 401),  # Expired token
        ({"aud": ""}, 401),  # Aud not set
    ],
)
def test_create_dossier_api_authentication(
    token_overwrite,
    expected,
    zkb_account,
    mock_jwks_public_private,
    token_data,
    set_zkb_JWK,
):
    # Test various cases for JWT authentication
    url = reverse("zkb-api:create-dossier")

    external_dossier_id = str(uuid.uuid4())

    dossier_data = schemas.CreateDossier(
        name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
    ).model_dump_json()

    # Use the token_overwrite to overwrite certain fields in the token to ensure JWT authentication is working
    jwk_key = jwk.JWK.from_json(json.dumps(mock_jwks_public_private["keys"][1]))
    pem = jwk_key.export_to_pem(private_key=True, password=None)
    authenticated_client = AuthenticatedClient(
        jwt.encode(
            {
                "aud": "account",
                "user_roles": [settings.API_ROLE],
                **token_data,
                **token_overwrite,
            },
            key=pem,
            algorithm="RS256",
        )
    )

    result = authenticated_client.post(
        path=url,
        data=dossier_data,
        content_type="application/json",
    )

    assert result.status_code == expected


def test_show_dossier_success(zkb_account):
    # Unauthenticated client
    dossier = Dossier.objects.create(external_id="test", account=zkb_account)
    client = Client()
    response = client.get(
        reverse("zkb-api:show-dossier", kwargs={"external_dossier_id": "test"}),
    )

    assert response.status_code == 302
    assert (
        f"{zkb_account.dmf_endpoint}/dossier/{dossier.uuid}/view/page?lang=de"
        == response.url
    )


def test_show_dossier_failure_no_dossier(zkb_account):
    # Dossier with external id for this account does not exist
    Dossier.objects.create(external_id="test", account=zkb_account)
    client = Client()
    response = client.get(
        reverse("zkb-api:show-dossier", kwargs={"external_dossier_id": "DOESNOTEXIST"}),
    )

    assert response.status_code == 302
    assert (
        f"{zkb_account.dmf_endpoint}/dossier/not-found?external-dossier-id=DOESNOTEXIST"
        == response.url
    )


def test_show_dossier_failure_no_account():
    # Account with zkb key does not exist
    client = Client()
    response = client.get(
        reverse("zkb-api:show-dossier", kwargs={"external_dossier_id": "DOESNOTEXIST"}),
    )

    assert response.status_code == 404


def test_create_get_update_real_estate_property(
    zkb_authenticated_client, zkb_account, set_zkb_JWK
):
    # Base case to check whether we can create a dossier
    external_dossier_id = str(uuid.uuid4())

    result = zkb_authenticated_client.post(
        path=reverse("zkb-api:create-dossier"),
        data=schemas.CreateDossier(
            name="Test Dossier", external_dossier_id=external_dossier_id, language="de"
        ).model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    new_data = schemas.RealEstatePropertyCreate(
        **{
            "key": "property1",
            "title": "Spacious Apartment",
            "floor": 3,
            "street": "Main Street",
            "street_nr": "123A",
            "zipcode": "12345",
            "city": "Exampleville",
        }
    )

    result = zkb_authenticated_client.put(
        path=reverse(
            "zkb-api:create-update-real-estate-property",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=new_data.model_dump_json(),
        content_type="application/json",
    )

    assert result.status_code == 201

    parse = schemas.RealEstatePropertyCreate.model_validate_json(result.content)

    assert parse == new_data

    res = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:get-real-estate-properties",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert res.status_code == 200

    parse = TypeAdapter(List[schemas.RealEstatePropertyCreate]).validate_json(
        res.content
    )

    assert parse == [new_data]

    result = zkb_authenticated_client.put(
        path=reverse(
            "zkb-api:create-update-real-estate-property",
            kwargs={"external_dossier_id": external_dossier_id},
        ),
        data=schemas.RealEstatePropertyCreate(
            **{
                "key": "property1",
                "floor": 10,
            }
        ).model_dump_json(),
        content_type="application/json",
    )

    parse = schemas.RealEstatePropertyCreate.model_validate_json(result.content)

    assert parse.floor == 10

    new_data.floor = 10

    assert parse == new_data

    res = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:get-real-estate-properties",
            kwargs={
                "external_dossier_id": external_dossier_id,
            },
        ),
    )

    assert res.status_code == 200

    parse = TypeAdapter(List[schemas.RealEstatePropertyCreate]).validate_json(
        res.content
    )

    assert parse == [new_data]


def test_get_external_dossier_id_from_semantic_document_success(
    mocked_get_dossier,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
    set_zkb_JWK,
):
    external_dossier_id = str(uuid.uuid4())

    # Create a dossier
    assert (
        zkb_authenticated_client.post(
            path=reverse("zkb-api:create-dossier"),
            data=schemas.CreateDossier(
                name="Test Dossier",
                external_dossier_id=external_dossier_id,
                language="de",
            ).model_dump_json(),
            content_type="application/json",
        ).status_code
        == 201
    )

    dossier = Dossier.objects.get(external_id=external_dossier_id)

    semantic_documents = add_some_fake_semantic_documents(dossier=dossier)

    # Pick a first one
    semantic_document = semantic_documents[0]

    res = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:get-external-dossier-id",
            kwargs={
                "semantic_document_uuid": semantic_document.uuid,
            },
        ),
    )

    assert res.status_code == 200

    parse = schemas.ExportDossierExport.model_validate_json(res.content)

    assert parse.external_dossier_id == external_dossier_id


# convert streaming_content into bytes
async def convert_streaming_content_to_bytes(streaming_content):
    bytes = b""
    async for chunk in streaming_content:
        bytes += chunk
    return bytes


def test_download_dossier_file_success(
    set_zkb_JWK, zkb_authenticated_client, zkb_account
):
    dossier = Dossier.objects.create(external_id="test", account=zkb_account)

    img_file, _, _ = get_law_files()
    image_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=img_file.read_bytes(), name=img_file.name),
        bucket=dossier.bucket,
    )

    res = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:get-dossier-file",
            kwargs={
                "dossier_file_uuid": image_file.uuid,
            },
        ),
    )

    assert res.status_code == 200
    content = async_to_sync(convert_streaming_content_to_bytes)(res.streaming_content)
    assert len(content) == len(img_file.read_bytes())


def test_download_dossier_file_no_access(set_zkb_JWK, zkb_authenticated_client):
    another_account = Account.objects.create(key="another_account")
    dossier = Dossier.objects.create(external_id="test", account=another_account)

    img_file, _, _ = get_law_files()
    image_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(content=img_file.read_bytes(), name=img_file.name),
        bucket=dossier.bucket,
    )

    res = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:get-dossier-file",
            kwargs={
                "dossier_file_uuid": image_file.uuid,
            },
        ),
    )

    assert res.status_code == 404


def test_soft_delete_semantic_document(
    set_zkb_JWK,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
):
    # Test deleting and recovering a semantic document
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=zkb_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_documents = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
    )

    # Get Semantic documents
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
        + "?show_pages=true"
    )

    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid

    response = zkb_authenticated_client.delete(
        path=f"{reverse('zkb-api:semantic-document-soft-delete',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "deleted"
    assert response.status_code == 200

    # Note trick is here to get the object from the deleted objects manager (all objects)
    # otherwise we will not see soft deleted objects
    soft_deleted_semantic_document = SemanticDocument.all_objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert soft_deleted_semantic_document.deleted_at is not None

    # Check we can't fetch it
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)

    assert len(parsed) == 0

    # Check we can recover it

    response = zkb_authenticated_client.put(
        path=f"{reverse('zkb-api:semantic-document-restore',kwargs={'external_dossier_id': external_dossier_id, 'semantic_document_uuid': semantic_documents[0].uuid})}",
    )

    parsed = semantic_document_schemas.SavingResultWithMessage.model_validate_json(
        response.content
    )

    assert parsed.message == "restored"
    assert response.status_code == 200

    restored_semantic_document = SemanticDocument.objects.get(
        uuid=semantic_documents[0].uuid
    )
    assert restored_semantic_document.deleted_at is None

    # Check we can fetch it
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-documents",
            kwargs={"external_dossier_id": external_dossier_id},
        )
        + "?show_pages=true"
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[schemas.SemanticDocument]).validate_json(response.content)

    assert len(parsed) == 1
    assert len(parsed[0].semantic_pages) == 5
    assert parsed[0].uuid == semantic_documents[0].uuid


def create_dossier_file_timestamped(dossier, timestamp):
    # This needs to be created outside freezetime, otherwise s3 bucket/minio
    # complains about the timestamp being too out of sync
    file_path = Path(__file__).parent / "data/240_betreibungsauskunft-mt-at.pdf"

    dossier_file = DossierFile.objects.create(
        dossier=dossier,
        data=ContentFile(
            content=file_path.read_bytes(), name="240_betreibungsauskunft-mt-at.pdf"
        ),
        bucket=dossier.bucket,
    )

    return dossier_file


def test_get_dossier_last_change(
    set_zkb_JWK, zkb_authenticated_client, zkb_account, document_categories, token_data
):
    (
        dossier_1,
        dossier_1_dossier_file_a,
        dossier_1_dossier_file_b,
        dossier_1_dossier_file_c,
        dossier_2,
        dossier_2_dossier_file,
    ) = prepare_for_dossier_unchanged_tests(token_data, zkb_account)

    parsed = get_remote_zkb_dossiers_unchanged(5, zkb_authenticated_client)

    # Both dossiers have not been changed in the last 5 days
    assert len(parsed) == 2
    assert parsed[0].uuid == dossier_1.uuid
    assert parsed[1].uuid == dossier_2.uuid

    parsed = get_remote_zkb_dossiers_unchanged(25, zkb_authenticated_client)

    # Only dossier_1 was not changed in the last 25 days
    assert len(parsed) == 1
    assert parsed[0].uuid == dossier_1.uuid

    parsed = get_remote_zkb_dossiers_unchanged(35, zkb_authenticated_client)

    # all dossier were changed in the last 30 days
    assert len(parsed) == 0


def test_get_dossier_last_change_with_deleted_dossier(
    set_zkb_JWK, zkb_authenticated_client, zkb_account, document_categories, token_data
):
    (
        dossier_1,
        dossier_1_dossier_file_a,
        dossier_1_dossier_file_b,
        dossier_1_dossier_file_c,
        dossier_2,
        dossier_2_dossier_file,
    ) = prepare_for_dossier_unchanged_tests(token_data, zkb_account)

    num_days = 5

    parsed = get_remote_zkb_dossiers_unchanged(num_days, zkb_authenticated_client)

    # Both dossiers have not been changed in the last 5 days
    assert len(parsed) == 2
    assert parsed[0].uuid == dossier_1.uuid
    assert parsed[1].uuid == dossier_2.uuid

    dossier_1.expiry_date = timezone.now()
    dossier_1.save()

    parsed = get_remote_zkb_dossiers_unchanged(5, zkb_authenticated_client)
    # Both dossiers have not been changed in the last 5 days but one is deleted
    assert len(parsed) == 1
    assert parsed[0].uuid == dossier_2.uuid


def get_remote_zkb_dossiers_unchanged(num_days, zkb_authenticated_client):
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossiers-unchanged",
            kwargs={"num_days_documents_unchanged": num_days},
        )
    )
    assert response.status_code == 200
    parsed = TypeAdapter(List[schemas.Dossier]).validate_json(response.content)
    return parsed


def prepare_for_dossier_unchanged_tests(token_data, zkb_account):
    now = timezone.now()
    dossier_user = get_user_or_create(
        account=zkb_account,
        username=token_data["preferred_username"],
        email=token_data.get("email"),
        fname=token_data.get("given_name"),
        lname=token_data.get("family_name"),
    )
    with freeze_time(now - timedelta(days=60)):
        dossier_1: Dossier = create_synthetic_dossier(
            external_id="test external id1", account=zkb_account, user=dossier_user.user
        )
        dossier_2: Dossier = create_synthetic_dossier(
            external_id="test external id2", account=zkb_account, user=dossier_user.user
        )

        dossier_1.expiry_date = now + timedelta(days=1)
        dossier_1.save()
        dossier_2.expiry_date = now + timedelta(days=1)
        dossier_2.save()
    frozen_time_45 = now - timedelta(days=45)
    frozen_time_30 = now - timedelta(days=30)
    frozen_time_15 = now - timedelta(days=15)
    dossier_1_dossier_file_a = create_dossier_file_timestamped(
        dossier_1, frozen_time_45
    )
    dossier_1_dossier_file_b = create_dossier_file_timestamped(
        dossier_1, frozen_time_30
    )
    dossier_1_dossier_file_c = create_dossier_file_timestamped(
        dossier_1, frozen_time_30
    )
    dossier_2_dossier_file = create_dossier_file_timestamped(dossier_2, frozen_time_15)

    with freeze_time(frozen_time_30):

        OriginalFile.objects.create(
            dossier=dossier_1,
            file=dossier_1_dossier_file_a,
            status=FileStatus.PROCESSING,
        )

        OriginalFile.objects.create(
            dossier=dossier_1,
            file=dossier_1_dossier_file_b,
            status=FileStatus.PROCESSING,
        )

        OriginalFile.objects.create(
            dossier=dossier_1,
            file=dossier_1_dossier_file_c,
            status=FileStatus.PROCESSING,
        )

    with freeze_time(frozen_time_15):
        OriginalFile.objects.create(
            dossier=dossier_2,
            file=dossier_2_dossier_file,
            status=FileStatus.PROCESSING,
        )

    return (
        dossier_1,
        dossier_1_dossier_file_a,
        dossier_1_dossier_file_b,
        dossier_1_dossier_file_c,
        dossier_2,
        dossier_2_dossier_file,
    )


def test_get_semantic_document_unique_page_objects(
    set_zkb_JWK,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
):
    # Test getting all unique page objects for a semantic document
    SemanticPage.objects.all().delete()

    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=zkb_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    semantic_document = add_some_fake_semantic_documents(
        dossier,
        num_docs=1,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )[0]

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-document-unique-page-objects",
            kwargs={"semantic_document_uuid": str(semantic_document.uuid)},
        )
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[PageObjectSchema]).validate_json(response.content)

    # 5 pages, 2 page objects per page
    assert len(parsed) == 10

    first_semantic_page = semantic_document.semantic_pages.get(number=0)
    first_semantic_page_uuid = first_semantic_page.uuid

    second_semantic_page = semantic_document.semantic_pages.get(number=1)
    second_semantic_page_uuid = second_semantic_page.uuid

    # Check that the page objects are correct
    # Two page objects for each page
    assert (
        len(
            [
                x
                for x in parsed
                if x.semantic_page_uuid == first_semantic_page_uuid
                and x.page_number == 0
            ]
        )
        == 2
    )

    assert (
        len(
            [
                x
                for x in parsed
                if x.semantic_page_uuid == second_semantic_page_uuid
                and x.page_number == 1
            ]
        )
        == 2
    )

    # Swap the page numbers
    first_semantic_page.number = 1
    first_semantic_page.save()
    second_semantic_page.number = 0
    second_semantic_page.save()

    # recall the API
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-document-unique-page-objects",
            kwargs={"semantic_document_uuid": str(semantic_document.uuid)},
        )
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[PageObjectSchema]).validate_json(response.content)

    # 5 pages, 2 page objects per page
    assert len(parsed) == 10

    assert (
        len(
            [
                x
                for x in parsed
                if x.semantic_page_uuid == first_semantic_page_uuid
                # Changed 0 -> 1
                and x.page_number == 1
            ]
        )
        == 2
    )

    assert (
        len(
            [
                x
                for x in parsed
                if x.semantic_page_uuid == second_semantic_page_uuid
                # Changed 1 -> 0
                and x.page_number == 0
            ]
        )
        == 2
    )


def test_get_dossier_page_objects(
    set_zkb_JWK,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
):
    # Test getting all page objects for a dossier
    external_dossier_id = str(uuid.uuid4())

    dossier = Dossier.objects.create(
        external_id=external_dossier_id, account=zkb_account
    )

    document_category = DocumentCategory.objects.filter(account=dossier.account).first()

    add_some_fake_semantic_documents(
        dossier,
        num_docs=2,
        allow_empty_docs=False,
        valid_document_category_keys=[document_category.name],
        max_pages=5,
        min_num_pages=5,
        no_page_objects_per_page=2,
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-unique-page-objects",
            kwargs={"external_dossier_id": external_dossier_id},
        )
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[PageObjectSchema]).validate_json(response.content)

    # 2 semantic documents, 5 pages, 2 page objects per page
    assert len(parsed) == 20


def test_semantic_document_unique_page_objects_sample_dossier(
    set_zkb_JWK, zkb_authenticated_client, zkb_account
):
    """
    @param set_zkb_JWK:
    @param zkb_authenticated_client:
    @param zkb_account:
    @return:
    """

    dossier = prepare_demo_dossier_for_account(
        account=zkb_account, external_id="254e93ec-c0f2-4133-be04-24170c607777"
    )

    # Check a single document of type Pension Certificate
    semdoc_pension_cert = dossier.semantic_documents.get(
        document_category__name="PENSION_CERTIFICATE",
        title_suffix="Thiemann Manuel AXA Zusatz GL 2017-03-01",
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:semantic-document-unique-page-objects",
            kwargs={"semantic_document_uuid": str(semdoc_pension_cert.uuid)},
        )
    )
    assert response.status_code == 200

    parsed_for_one_semdoc = TypeAdapter(List[PageObjectSchema]).validate_json(
        response.content
    )
    logger.info(
        "page_objects for single semantic document",
        parsed_for_one_semdoc=parsed_for_one_semdoc,
    )

    compact_result = []
    for po in parsed_for_one_semdoc:
        compact_result.append(
            (po.key, po.titles.en, po.value, po.semantic_document_titles.fr)
        )

    expected_result = [
        (
            "company",
            "Company",
            "AXA",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "document_date",
            "Date",
            "01.03.2017",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "product",
            "Product",
            "Zusatz GL",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "pc_employer",
            "Employer",
            "HypoPlus AG",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "fullname",
            "Name",
            "Thiemann Manuel",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "date_of_birth",
            "Date of Birth",
            "04.09.1977",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "applicable_annual_salary_declared",
            "Applicable annual Salary (declared)",
            "CHF 189'300",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "ahv_new",
            "New AHV No",
            "756.4078.9585.31",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "projected_assets_retirement",
            "Assets on retirement",
            "CHF 901'974",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "projected_pension_retirement",
            "Benefits on retirement",
            "CHF 52'008",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
        (
            "withdrawal_benefit",
            "Withdrawal benefit (vesting Art. 15)",
            "CHF 15'229",
            "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
        ),
    ]

    assert sorted(expected_result, key=lambda x: x[0]) == sorted(
        compact_result, key=lambda x: x[0]
    )


def test_dossier_unique_page_objects_sample_dossier(
    set_zkb_JWK, zkb_authenticated_client, zkb_account
):
    """
    Moving the sample dossier from default to zkb is not a clean way of doing it.
    @param set_zkb_JWK:
    @param zkb_authenticated_client:
    @param zkb_account:
    @return:
    """
    dossier = prepare_demo_dossier_for_account(
        account=zkb_account, external_id="254e93ec-c0f2-4133-be04-24170c607777"
    )

    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-unique-page-objects",
            kwargs={"external_dossier_id": dossier.external_id},
        )
    )
    assert response.status_code == 200

    parsed = TypeAdapter(List[PageObjectSchema]).validate_json(response.content)

    logger.info("page_objects", parsed=parsed)

    assert len(parsed) == 157

    compact_result = []
    for po in parsed:
        compact_result.append(
            (po.key, po.titles.en, po.value, po.semantic_document_titles.fr)
        )

    assert sorted(compact_result, key=lambda x: x[0]) == sorted(
        unique_page_objects_per_sample_dossier_fixture, key=lambda x: x[0]
    )


def test_performance_dossier_unique_page_objects(
    set_zkb_JWK,
    zkb_authenticated_client,
    zkb_account,
    document_categories,
):
    """
    # Takes 0.76 seconds on Ubuntu 22.04, 64GB RAM, AMD Ryzen 7 4800H; takes 1.7 seconds on MT predator

    :param set_zkb_JWK:
    :param zkb_authenticated_client:
    :param zkb_account:
    :param document_categories:
    :return:
    """
    # user = User.objects.get(email="<EMAIL>")
    external_id = str(uuid.UUID("254e93ec-c0f2-4133-be04-24170c607788"))

    dossier, created = Dossier.objects.get_or_create(
        name="Test Dossier for Profiling",
        external_id=external_id,
        defaults=dict(account=zkb_account),
    )

    if created:

        document_category = DocumentCategory.objects.filter(
            account=dossier.account
        ).first()

        add_some_fake_semantic_documents(
            dossier,
            num_docs=10,
            allow_empty_docs=False,
            valid_document_category_keys=[document_category.name],
            max_pages=10,
            min_num_pages=10,
            no_page_objects_per_page=10,
        )
    response_start = timezone.now()
    response = zkb_authenticated_client.get(
        path=reverse(
            "zkb-api:dossier-unique-page-objects",
            kwargs={"external_dossier_id": dossier.external_id},
        )
    )
    duration_seconds = (timezone.now() - response_start).total_seconds()
    logger.info("Time to get unique page objects", duration_seconds=duration_seconds)
    assert (
        duration_seconds < 10
    ), "Should be less than 2 seconds on your machine but certainly not more than 10"
    assert response.status_code == 200

    parsed = TypeAdapter(List[PageObjectSchema]).validate_json(response.content)

    assert len(parsed) == 1000

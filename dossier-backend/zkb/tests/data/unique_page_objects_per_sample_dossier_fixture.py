unique_page_objects_per_sample_dossier_fixture = [
    (
        "identity_swiss_passport_first",
        "identity_swiss_passport_first",
        None,
        "210 Passeport <PERSON>»",
    ),
    ("firstname", "Firstname", "<PERSON>»", "210 Passeport CH Andreas Dom<PERSON>k»"),
    (
        "status",
        "Status",
        "Kein Betreibungen",
        "240 Extrait du registre des poursuites Thiemann Manuel Antonius Keine Betreibungen 2014-04-11",
    ),
    (
        "addressline_fullname",
        "Name",
        "Thiemann Manuel <PERSON>",
        "240 Extrait du registre des poursuites Thiemann Manuel <PERSON>ius Keine Betreibungen 2014-04-11",
    ),
    (
        "date_of_birth",
        "Date of Birth",
        "04.09.1977",
        "240 Extrait du registre des poursuites Thiemann Manuel Antonius Keine Betreibungen 2014-04-11",
    ),
    (
        "fullname",
        "Name",
        "Thiemann Manuel <PERSON>",
        "240 Extrait du registre des poursuites Thiemann Manuel <PERSON> Betreibungen 2014-04-11",
    ),
    (
        "addressline",
        "Name and Address",
        "<PERSON>ntinistr. 195 8049 Zürich, geb. 04.09.1977",
        "240 Extrait du registre des poursuites Thiemann Manuel Antonius Keine Betreibungen 2014-04-11",
    ),
    (
        "firstname",
        "Firstname",
        "<PERSON>",
        "240 Extrait du registre des poursuites Thiemann Manuel <PERSON>ius Keine Betreibungen 2014-04-11",
    ),
    (
        "zip",
        "Zip Code",
        "8049",
        "240 Extrait du registre des poursuites Thiemann Manuel Antonius Keine <PERSON>reibungen 2014-04-11",
    ),
    (
        "document_date",
        "Date",
        "11.04.2014",
        "240 Extrait du registre des poursuites Thiemann Manuel Antonius Keine Betreibungen 2014-04-11",
    ),
    (
        "status",
        "Status",
        "Kein Betreibungen",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "addressline_fullname",
        "Name",
        "Thiemann-Strutz Angelica",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "fullname",
        "Name",
        "Thiemann-Strutz Angelica c/o",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "date_of_birth",
        "Date of Birth",
        "21.10.1976",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "addressline",
        "Name and Address",
        "Frau Thiemann-Strutz Angelica c/o Thiemann Segantinistr. 195 8049 Zürich, geb. 21.10.1976",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "firstname",
        "Firstname",
        "Angelica",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "zip",
        "Zip Code",
        "8049",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "document_date",
        "Date",
        "11.04.2014",
        "240 Extrait du registre des poursuites Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11",
    ),
    (
        "status",
        "Status",
        "Keine Einträge",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "lastname",
        "Lastname",
        "Thiemann",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "firstname",
        "Firstname",
        "Manuel Antonius",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "date_of_birth",
        "Date of Birth",
        "04.09.1977",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "native_place",
        "Native Place",
        "Ringgenberg BE",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "nationality",
        "Nationality",
        "CH",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    (
        "document_date",
        "Date",
        "01.09.2020",
        "245 Extrait du casier judiciaire Thiemann Manuel Antonius 2020-09-01",
    ),
    ("canton_short", "Canton", "ZH", "310 Déclaration d'impôt Mustermann Max ZH 2019"),
    ("year", "Year", "2019", "310 Déclaration d'impôt Mustermann Max ZH 2019"),
    (
        "p1_ahv_new",
        "P1 New AHV No.",
        "756.4078.6666.31",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_fullname",
        "P1 Name",
        "Mustermann Max",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "street",
        "Street",
        "Beispielstrasse 42",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    ("zip", "Zip Code", "8055", "310 Déclaration d'impôt Mustermann Max ZH 2019"),
    ("city", "City", "Zürich", "310 Déclaration d'impôt Mustermann Max ZH 2019"),
    (
        "p1_date_of_birth",
        "P1 Date of Birth",
        "04.09.1967",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_date_of_birth",
        "P2 Date of Birth",
        "21.10.1976",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_firstname",
        "P2 Lastname",
        "Maria",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_marital_status",
        "P1 Civil Status",
        "verheiratet",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_profession",
        "P1 Profession",
        "Informatiker",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_profession",
        "P2 Profession",
        "Solution Consultant",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_employer",
        "P1 Employer",
        "Beispielfirma AG",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_employer",
        "P2 Employer",
        "ServiceFirma",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_employer_location",
        "P1 Place of work",
        "Zürich",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "phone_secondary",
        "Phone number (2nd Priority)",
        "Telefon G       0799991234",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_phone_primary",
        "P2 Phone number",
        "0799991234",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "phone_primary",
        "Phone number",
        "0763331234      P 07633",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "section_children",
        "Children",
        "Raphael Mustermann\nAngelo Mustermann",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "document_date",
        "Date",
        "20.04.2020",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_income_employed_main",
        "P1 Main Income",
        "CHF 22'506",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_income_employed_main",
        "P2 Main Income",
        "CHF 128'991",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_income_employed_side",
        "P1 Additional Income",
        "CHF 7'502",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_income_eo",
        "P2 Income EO",
        "CHF 4'388",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_income_child_benefits",
        "P2 Income Child Benefits",
        "CHF 4'800",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_portfolio",
        "Income Portfolio",
        "CHF 15",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "property_imputed_rental_value",
        "Imputed Rental Value",
        "CHF 25'700",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_real_estate_gross",
        "Income Real Estate gross",
        "CHF 25'700",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "property_maintenance_cost",
        "Maintenance Cost",
        "CHF 5'140",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_real_estate_net_primary",
        "Primary Self used Real Estate Income net",
        "CHF 20'560",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_gross_total",
        "Total Income gross",
        "CHF 188'762",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_expense_employment",
        "P1 Employment Expenses",
        "CHF 7'901",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_expense_employment",
        "P2 Employment Expenses",
        "CHF 8'850",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "interest_paid_on_debt",
        "Interest paid on Debt",
        "CHF 11'257",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p1_contribution_pillar_3a",
        "P1 Contribution Pillar 3a",
        "CHF 3'350",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "p2_contribution_pillar_3a",
        "P2 Contribution Pillar 3a",
        "CHF 6'700",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "insurance_premiums_and_interest_on_savings_accounts",
        "Insurance Premiums and Interest on Savings Accounts",
        "CHF 7'800",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "expense_children_daycare",
        "Expenses Daycare Children",
        "CHF 19'248",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "deductions_total",
        "Total Deductions",
        "CHF 71'006",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_net_total",
        "Total Income net",
        "CHF 117'756",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_taxable_global",
        "Taxable Income total",
        "CHF 99'756",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "income_taxable_local",
        "Taxable Income in Canton",
        "CHF 99'756",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_portfolio",
        "Portfolio and Accounts",
        "CHF 2'458'532",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_cars",
        "Vehicles",
        "CHF 1'040",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_other",
        "Further Assets",
        "CHF 180'288",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_real_estate_main_property",
        "Real Estate (house or appartment",
        "CHF 1'172'000",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "address_real_estate_primary",
        "Primary Self used Real Estate Address",
        "Gemeinde ZH                    Zürich           Strasse Beispielstrasse 42",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_gross_total",
        "Total Assets Gross",
        "CHF 3'811'860",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "debt_total",
        "Total Debt",
        "CHF 1'135'000",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_taxable_global",
        "Taxable Assets (global)",
        "CHF 2'676'860",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "assets_taxable_local",
        "Taxable Assets in Canton",
        "CHF 2'676'860",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "debt_detail_lines",
        "Debt Details",
        "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257",
        "310 Déclaration d'impôt Mustermann Max ZH 2019",
    ),
    (
        "ahv_new",
        "New AHV No",
        "756.4078.9585.31",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "accounting_period_to",
        "End of Period",
        "31.12.2016",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "accounting_period_from",
        "Start of Period",
        "01.03.2016",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "year",
        "Year",
        "2016",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "fullname",
        "Name",
        "Manuel Thiemann",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "firstname",
        "Firstname",
        "Manuel",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "street",
        "Street",
        "Birmensdorferstrasse 578",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "zip",
        "Zip Code",
        "8055",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "city",
        "City",
        "Zürich",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "salary_board",
        "Board Compensation",
        "CHF 10'000",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "salary_gross",
        "Gross Salary",
        "CHF 10'000",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "salary_benefits_ahv",
        "Contributions OASI/DI/IC/UI/NBUV",
        "CHF 622",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "salary_net",
        "Net Salary",
        "CHF 9'378",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "salary_comments",
        "Comments",
        "Spesenreglement durch Kanton GR am 17.11.2010 genehmigt.",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "document_date",
        "Date",
        "20.01.2017",
        "330 Certificat de salaire Manuel Thiemann 2016 brutto CHF 10'000",
    ),
    (
        "document_date",
        "Date",
        "01.03.2017",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "company",
        "Company",
        "AXA",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "product",
        "Product",
        "Basis Kader",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "pc_employer",
        "Employer",
        "HypoPlus AG",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "fullname",
        "Name",
        "Thiemann Manuel",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "date_of_birth",
        "Date of Birth",
        "04.09.1977",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "applicable_annual_salary_declared",
        "Applicable annual Salary (declared)",
        "CHF 189'300",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "ahv_new",
        "New AHV No",
        "756.4078.9585.31",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "projected_pension_retirement",
        "Benefits on retirement",
        "CHF 36'966",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "projected_assets_retirement",
        "Assets on retirement",
        "CHF 561'965",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "withdrawal_benefit",
        "Withdrawal benefit (vesting Art. 15)",
        "CHF 99'765",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "wef_possible",
        "Payout possible for home ownership",
        "CHF 99'765",
        "410 Certificat de prévoyance Thiemann Manuel AXA Basis Kader 2017-03-01",
    ),
    (
        "company",
        "Company",
        "AXA",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "document_date",
        "Date",
        "01.03.2017",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "product",
        "Product",
        "Zusatz GL",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "pc_employer",
        "Employer",
        "HypoPlus AG",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "fullname",
        "Name",
        "Thiemann Manuel",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "date_of_birth",
        "Date of Birth",
        "04.09.1977",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "applicable_annual_salary_declared",
        "Applicable annual Salary (declared)",
        "CHF 189'300",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "ahv_new",
        "New AHV No",
        "756.4078.9585.31",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "projected_assets_retirement",
        "Assets on retirement",
        "CHF 901'974",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "projected_pension_retirement",
        "Benefits on retirement",
        "CHF 52'008",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "withdrawal_benefit",
        "Withdrawal benefit (vesting Art. 15)",
        "CHF 15'229",
        "410 Certificat de prévoyance Thiemann Manuel AXA Zusatz GL 2017-03-01",
    ),
    (
        "document_date",
        "Date",
        "31.12.2019",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "company",
        "Company",
        "ZKB",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "firstname",
        "Firstname",
        "Manuel",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "fullname",
        "Name",
        "Manuel Thiemann",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "street",
        "Street",
        "Birmensdorferstrasse 578",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "zip",
        "Zip Code",
        "8055",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "city",
        "City",
        "Zürich",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "iban",
        "IBAN",
        "CH33 0070 0110 0062 5719 6",
        "424 Compte de libre passage Manuel Thiemann ZKB 2019-12-31",
    ),
    (
        "document_date",
        "Date",
        "17.01.2014",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "company",
        "Company",
        "Postfinance",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "fullname",
        "Name",
        "Angelica Thiemann",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "firstname",
        "Firstname",
        "Angelica",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "street",
        "Street",
        "Segantinistrasse 195",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "zip",
        "Zip Code",
        "8049",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "city",
        "City",
        "Zürich",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "account_no",
        "Account No.",
        "553.804.842",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "total_amount",
        "Account Value",
        "CHF 12'902",
        "430 Compte pilier 3 Angelica Thiemann Postfinance Saldo CHF 12'902",
    ),
    (
        "canton_short",
        "Canton",
        "ZH",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "document_date",
        "Date",
        "20.03.2020",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "police_no",
        "Contract ID",
        "259'879",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "property_address_city",
        "Property City",
        "Zürich-Albisrieden",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "pi_cadaster_no",
        "Cadaster No.",
        "261.AR6701",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "zip",
        "Zip Code",
        "8001",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "city",
        "City",
        "Zürich",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "insurance_amount",
        "Insured Value",
        "CHF 6'661'950",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "property_address",
        "Property Address",
        "Birmensdorferstrasse 576, 8055 Zürich",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "cubature",
        "Cubature",
        "7150",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "pi_estimation_reason",
        "Reason for Estimation",
        "Überprüfung GVZ",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576, 8055 Zürich Wert CHF 6'661'950 2020-03-20",
    ),
    (
        "canton_short",
        "Canton",
        "ZH",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "document_date",
        "Date",
        "18.01.2019",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "property_address",
        "Property Address",
        "Birmensdorferstrasse 576\nBirmensdorferstrasse 578\n8055 Zürich",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "zip",
        "Zip Code",
        "8001",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "city",
        "City",
        "Zürich",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "property_desc",
        "Property Description",
        "Wohnhaus",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "year_construction",
        "Year of Construction",
        "2007",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "cubature",
        "Cubature",
        "7150",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "pi_cadaster_no",
        "Cadaster No.",
        "AR6701",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "pi_estimation_date",
        "Date of Estimation",
        "05.10.2010",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "pi_estimation_reason",
        "Reason for Estimation",
        "Überprüfung GVZ",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "insurance_amount",
        "Insured Value",
        "CHF 6'662'000",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
    (
        "insurance_yearly_bill_amount",
        "Yearly Fee",
        "CHF 2'131",
        "617 Attestation assurance des batiments ZH Birmensdorferstrasse 576 Birmensdorferstrasse 578 8055 Zürich Wert CHF 6'662'000 2019-01-18",
    ),
]

import pytest
from pytest_httpx import HTTPXMock

from zkb.external_auth import authorize

test_key = "test"


def test_external_auth(httpx_mock: HTTPXMock, settings):
    settings.ZKB_AUTHORIZATION_ENDPOINT = "https://doesnotexist"
    settings.ZKB_AUTHORIZATION_CERT_PEM_FILE = None

    httpx_mock.add_response(
        content=b"true",
        url="https://doesnotexist/partnerExtern/hasDatenraumrecht?partnerUuid=123&logonId=test",
    )
    external_dossier_id = "123"
    login_id = "test"

    res = authorize(login_id, external_dossier_id)
    assert res is True

    httpx_mock.add_response(
        content=b"false",
        url="https://doesnotexist/partnerExtern/hasDatenraumrecht?partnerUuid=123&logonId=test",
    )
    res = authorize(login_id, external_dossier_id)
    assert res is False


@pytest.mark.integration
@pytest.mark.skip(
    reason="This test requires a valid certificate and a local proxy to the hypodossier gw"
)
def test_external_auth_with_certificate():
    res = authorize("test", "123")
    assert res is False

import logging
from pprint import pprint

import djclick as click

import json
from deepdiff import DeepDiff

from django.test import Client, override_settings
from django.urls import reverse

from zkb.tests.data import DATA_PATH

logger = logging.getLogger(__name__)


@click.command()
@override_settings(ALLOWED_HOSTS=["testserver"])
def compare_zkb_openapi_schema():
    """
    python manage.py compare_zkb_openapi_schema
    """
    openapi_v1 = json.loads(
        (DATA_PATH / "zkb_openapi_without_auth_2024_05_24.json").read_text()
    )

    client = Client()
    openapi_v2 = client.get(
        path=reverse(
            # "zkb-api:openapi-json"
            "zkbopenapiwithoutauth",
        )
    ).json()

    # comparing files
    diff = DeepDiff(openapi_v1, openapi_v2, ignore_order=True)

    # for key, value in openapi_v1.items():
    #     if openapi_v1[key] != openapi_v2[key]:
    #         logger.error(f"Key: {key}, Value1: {value}, Value2: {openapi_v2[key]}")

    pprint(diff)

# Generated by Django 3.2.21 on 2023-09-20 15:28

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AccessCache',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user_id', models.Char<PERSON>ield(max_length=50)),
                ('dossier_id', models.Char<PERSON>ield(max_length=255)),
                ('expires_at', models.DateTimeField()),
                ('has_access', models.BooleanField(default=False)),
            ],
        ),
        migrations.AddIndex(
            model_name='accesscache',
            index=models.Index(fields=['user_id', 'dossier_id'], name='zkb_accessc_user_id_ee4afb_idx'),
        ),
    ]

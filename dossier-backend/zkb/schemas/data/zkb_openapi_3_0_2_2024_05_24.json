{"schemas": {"Message": {"properties": {"detail": {"title": "Detail", "type": "string"}}, "required": ["detail"], "title": "Message", "type": "object"}, "Dossier": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}}, "required": ["uuid", "external_dossier_id", "updated_at", "created_at"], "title": "Dossier", "type": "object"}, "Error": {"properties": {"code": {"title": "Code", "type": "integer"}, "message": {"title": "Message", "type": "string"}}, "required": ["code", "message"], "title": "Error", "type": "object"}, "CreateDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"maxLength": 255, "title": "Name", "type": "string"}}, "required": ["external_dossier_id", "name"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "ChangeDossier": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}, "name": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Name"}, "lang": {"anyOf": [{"$ref": "#/components/schemas/Language"}, {"type": "null"}]}}, "required": ["external_dossier_id"], "title": "Change<PERSON><PERSON><PERSON>", "type": "object"}, "Language": {"enum": ["de", "fr", "it", "en"], "title": "Language", "type": "string"}, "RealEstatePropertyResponse": {"properties": {"key": {"maxLength": 255, "title": "Key", "type": "string"}, "title": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Title"}, "floor": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Floor"}, "street": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Street"}, "street_nr": {"anyOf": [{"maxLength": 10, "type": "string"}, {"type": "null"}], "title": "Street Nr"}, "zipcode": {"anyOf": [{"maxLength": 60, "type": "string"}, {"type": "null"}], "title": "Zipcode"}, "city": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "City"}, "dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}}, "required": ["key", "dossier_uuid", "external_dossier_id"], "title": "RealEstatePropertyResponse", "type": "object"}, "RealEstatePropertyCreate": {"properties": {"key": {"maxLength": 255, "title": "Key", "type": "string"}, "title": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Title"}, "floor": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Floor"}, "street": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "Street"}, "street_nr": {"anyOf": [{"maxLength": 10, "type": "string"}, {"type": "null"}], "title": "Street Nr"}, "zipcode": {"anyOf": [{"maxLength": 60, "type": "string"}, {"type": "null"}], "title": "Zipcode"}, "city": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "City"}}, "required": ["key"], "title": "RealEstatePropertyCreate", "type": "object"}, "CreatedObjectReference": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "CreatedObjectReference", "type": "object"}, "EntityTypes": {"enum": ["realestateproperty", ""], "title": "EntityTypes", "type": "string"}, "DossierProcessingStatus": {"properties": {"dossier_uuid": {"format": "uuid", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "external_id": {"title": "External Id", "type": "string"}, "progress": {"title": "Progress", "type": "integer"}, "original_files": {"items": {"$ref": "#/components/schemas/OriginalFile"}, "title": "Original Files", "type": "array"}}, "required": ["dossier_uuid", "external_id", "progress", "original_files"], "title": "DossierProcessingStatus", "type": "object"}, "ExtractedFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "path_from_original": {"title": "Path From Original", "type": "string"}, "file_name": {"title": "File Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "path_from_original", "file_name", "status", "file_url", "created_at", "updated_at"], "title": "ExtractedFile", "type": "object"}, "FileStatus": {"enum": ["processing", "error", "processed"], "title": "FileStatus", "type": "string"}, "OriginalFile": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "name": {"title": "Name", "type": "string"}, "status": {"$ref": "#/components/schemas/FileStatus"}, "extracted_files": {"items": {"$ref": "#/components/schemas/ExtractedFile"}, "title": "Extracted Files", "type": "array"}, "file_url": {"title": "File Url", "type": "string"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["uuid", "name", "status", "extracted_files", "file_url", "created_at", "updated_at"], "title": "OriginalFile", "type": "object"}, "AccessMode": {"description": "Access mode of a semantic document (not the same as access mode of a dossier)", "enum": ["read_only", "read_write"], "title": "AccessMode", "type": "string"}, "Confidence": {"enum": ["certain", "high", "medium", "low"], "title": "Confidence", "type": "string"}, "SemanticDocument": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "external_semantic_document_id": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "title": {"title": "Title", "type": "string"}, "document_category_confidence": {"$ref": "#/components/schemas/Confidence"}, "document_category_key": {"title": "Document Category Key", "type": "string"}, "semantic_pages": {"items": {"$ref": "#/components/schemas/SemanticPage"}, "title": "Semantic Pages", "type": "array"}, "entity_type": {"anyOf": [{"$ref": "#/components/schemas/EntityTypes"}, {"type": "null"}]}, "entity_key": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Entity Key"}, "access_mode": {"allOf": [{"$ref": "#/components/schemas/AccessMode"}], "default": "read_write"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "last_change": {"default": "2024-06-04T17:29:33.603112Z", "format": "date-time", "title": "Last Change", "type": "string"}}, "required": ["uuid", "title", "document_category_confidence", "document_category_key", "semantic_pages", "updated_at"], "title": "SemanticDocument", "type": "object"}, "SemanticPage": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}, "number": {"description": "Page number is zero based. First page has page number 0", "title": "Number", "type": "integer"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "last_change": {"default": "2024-06-04T17:29:33.589739Z", "format": "date-time", "title": "Last Change", "type": "string"}}, "required": ["uuid", "number", "updated_at"], "title": "SemanticPage", "type": "object"}, "ExportDossierExport": {"properties": {"external_dossier_id": {"maxLength": 255, "pattern": "[A-Za-z0-9-]{1,255}", "title": "External Dossier Id", "type": "string"}}, "required": ["external_dossier_id"], "title": "ExportDossierExport", "type": "object"}, "SemanticDocumentUpdate": {"properties": {"external_semantic_document_id": {"anyOf": [{"maxLength": 255, "type": "string"}, {"type": "null"}], "title": "External Semantic Document Id"}, "access_mode": {"anyOf": [{"$ref": "#/components/schemas/AccessMode"}, {"type": "null"}]}}, "required": ["external_semantic_document_id"], "title": "SemanticDocumentUpdate", "type": "object"}, "DocumentCategories": {"additionalProperties": {"$ref": "#/components/schemas/DocumentCategory"}, "title": "DocumentCategories", "type": "object"}, "DocumentCategory": {"properties": {"key": {"title": "Key", "type": "string"}, "id": {"title": "Id", "type": "string"}, "title_de": {"title": "Title De", "type": "string"}, "description_de": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description De"}}, "required": ["key", "id", "title_de"], "title": "DocumentCategory", "type": "object"}, "SemanticDocumentPDFExportRequest": {"properties": {"uuid": {"format": "uuid", "title": "<PERSON><PERSON>", "type": "string"}}, "required": ["uuid"], "title": "SemanticDocumentPDFExportRequest", "type": "object"}, "ExportProcessingStatus": {"enum": ["PROCESSING", "ERROR", "PROCESSED"], "title": "ExportProcessingStatus", "type": "string"}, "ExportStatus": {"properties": {"semantic_document_export_request_uuid": {"format": "uuid", "title": "Semantic Document Export Request Uuid", "type": "string"}, "status": {"$ref": "#/components/schemas/ExportProcessingStatus"}, "dossier_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Dossier Url"}, "dossier_file_uuid": {"anyOf": [{"format": "uuid", "type": "string"}, {"type": "null"}], "title": "Dossier File <PERSON>"}, "updated_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Updated At"}}, "required": ["semantic_document_export_request_uuid", "status"], "title": "ExportStatus", "type": "object"}, "SavingResultWithMessage": {"properties": {"message": {"title": "Message", "type": "string"}}, "required": ["message"], "title": "SavingResultWithMessage", "type": "object"}}, "securitySchemes": {"ZKBJWTAuth": {"type": "http", "scheme": "bearer"}}}
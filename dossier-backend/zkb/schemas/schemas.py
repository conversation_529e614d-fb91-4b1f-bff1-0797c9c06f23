from enum import Enum
from typing import List, Dict, Optional
from uuid import UUID

from dateutil import tz
from datetime import datetime, date
from pydantic import (
    StringConstraints,
    ConfigDict,
    Field,
    RootModel,
    BaseModel,
)
from core.generics import AnyHttpUrlStr

from dossier.models import RealestateProperty
from dossier.schemas import Language, EntityTypes, AccessMode
from semantic_document.schemas import Confidence
from typing_extensions import Annotated

ExternalDossierID = Annotated[
    str, StringConstraints(max_length=255, pattern="[A-Za-z0-9-]{1,255}")
]
PrincipalID = Annotated[str, StringConstraints(max_length=255)]
DossierName = Annotated[str, StringConstraints(max_length=255)]


class CreateDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: DossierName


class ExportDossierExport(BaseModel):
    external_dossier_id: ExternalDossierID


class RealEstatePropertyCreate(BaseModel):
    key: Annotated[str, StringConstraints(max_length=255)]
    title: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    floor: Optional[int] = None
    street: Optional[Annotated[str, StringConstraints(max_length=255)]] = None
    street_nr: Optional[Annotated[str, StringConstraints(max_length=10)]] = None
    zipcode: Optional[Annotated[str, StringConstraints(max_length=60)]] = None
    city: Optional[Annotated[str, StringConstraints(max_length=255)]] = None

    @staticmethod
    def to_schema(
        react_estate_property: RealestateProperty,
    ):
        return RealEstatePropertyCreate(
            key=react_estate_property.key,
            title=react_estate_property.title,
            floor=react_estate_property.floor,
            street=react_estate_property.street,
            street_nr=react_estate_property.street_nr,
            zipcode=react_estate_property.zipcode,
            city=react_estate_property.city,
        )


class RealEstatePropertyResponse(RealEstatePropertyCreate):
    dossier_uuid: UUID
    external_dossier_id: ExternalDossierID
    model_config = ConfigDict(from_attributes=True)

    # Some of the built-in data-loading functionality has been slated for removal.
    # In particular, parse_raw and parse_file are now deprecated. In Pydantic V2,
    # model_validate_json works like parse_raw. Otherwise, you should load the data and then pass it to model_validate.
    # The from_orm method has been deprecated; you can now just use model_validate
    # (equivalent to parse_obj from Pydantic V1) to achieve something similar, as long as you've set
    # from_attributes=True in the model config.
    # https://docs.pydantic.dev/2.7/migration/#changes-to-pydanticbasemodel

    @staticmethod
    def to_schema(real_estate_property: RealestateProperty):
        return RealEstatePropertyResponse(
            key=real_estate_property.key,
            title=real_estate_property.title,
            floor=real_estate_property.floor,
            street=real_estate_property.street,
            street_nr=real_estate_property.street_nr,
            zipcode=real_estate_property.zipcode,
            city=real_estate_property.city,
            dossier_uuid=real_estate_property.dossier.uuid,
            external_dossier_id=real_estate_property.dossier.external_id,
        )


class SemanticPage(BaseModel):
    uuid: UUID
    number: int = Field(
        description="Page number is zero based. First page has page number 0"
    )
    image_url: Optional[str] = None

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())


class SemanticDocument(BaseModel):
    uuid: UUID

    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ] = None

    title: str

    document_category_confidence: Confidence

    document_category_key: str

    semantic_pages: List[SemanticPage]

    entity_type: Optional[EntityTypes] = None
    entity_key: Optional[str] = None

    access_mode: AccessMode = AccessMode.READ_WRITE

    updated_at: datetime
    last_change: datetime = datetime.now(tz=tz.tzutc())

    semantic_document_date: Optional[date] = None

    # 250213 mt disabled version where we deliver a datetime
    # ZKB still prefers a date after all. Code kept here if they change opinion again.
    # semantic_document_date: Optional[datetime] = None
    #
    # @field_validator("semantic_document_date")
    # def convert_date_to_datetime(
    #     cls, v: Union[date, datetime, None]
    # ) -> Optional[datetime]:
    #     if isinstance(v, date) and not isinstance(v, datetime):
    #         return datetime.combine(v, datetime.min.time(), tzinfo=timezone.utc)
    #     return v


class Dossier(BaseModel):
    uuid: UUID
    external_dossier_id: ExternalDossierID

    updated_at: datetime
    created_at: datetime
    name: str


class ExportProcessingStatus(str, Enum):
    PROCESSING = "PROCESSING"
    ERROR = "ERROR"
    PROCESSED = "PROCESSED"


class ExportStatus(BaseModel):
    semantic_document_export_request_uuid: UUID
    status: ExportProcessingStatus
    # We use AnyHttpUrlStr instead of HttpUrl as CI uses ports as part of the URL
    dossier_url: Optional[AnyHttpUrlStr] = None
    dossier_file_uuid: Optional[UUID] = None

    updated_at: Optional[datetime] = None


class SemanticDocumentPDFExportRequest(BaseModel):
    uuid: UUID


class DocumentCategory(BaseModel):
    key: str
    id: str
    title_de: str
    description_de: Optional[str] = None


class DocumentCategories(RootModel):
    root: Dict[str, DocumentCategory]


class DossierAccessRequest(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class DossierAuthorization(BaseModel):
    principalID: PrincipalID
    external_dossier_id: ExternalDossierID


class FileStatus(str, Enum):
    PROCESSING = "processing"
    ERROR = "error"
    PROCESSED = "processed"


class ExtractedFile(BaseModel):
    uuid: UUID
    path_from_original: str
    file_name: str
    status: FileStatus
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class OriginalFile(BaseModel):
    uuid: UUID
    name: str
    status: FileStatus
    extracted_files: List[ExtractedFile]
    file_url: AnyHttpUrlStr
    created_at: datetime
    updated_at: datetime


class DossierProcessingStatus(BaseModel):
    dossier_uuid: UUID
    external_id: str
    progress: int
    original_files: List[OriginalFile]


class ChangeDossier(BaseModel):
    external_dossier_id: ExternalDossierID
    name: Optional[DossierName] = None
    lang: Optional[Language] = None


class SemanticDocumentUpdate(BaseModel):
    external_semantic_document_id: Optional[
        Annotated[str, StringConstraints(max_length=255)]
    ]
    access_mode: Optional[AccessMode] = None

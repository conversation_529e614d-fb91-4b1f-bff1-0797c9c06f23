version: '3.8'
services:

  dms:
    depends_on:
      minio:
        condition: service_healthy
      django-db:
        condition: service_healthy

  keycloak:
    volumes:
      - ./keycloak/realm-export.json:/config/realm-export.json
    depends_on:
      keycloak-db:
        condition: service_healthy

  cypress-run:
    image: "cypress/included:7.4.0"
    depends_on:
      keycloak:
        condition: service_healthy
      dms:
        condition: service_healthy
      caddy:
        condition: service_healthy
      dossier-frontend:
        condition: service_healthy
    entrypoint: cypress run
    volumes:
      - ./cypress/:/cypress
      - ./cypress/cypress.json:/cypress.json

  cypress-gui:
    image: "cypress/included:7.4.0"
    depends_on:
      keycloak:
        condition: service_healthy
      dms:
        condition: service_healthy
      caddy:
        condition: service_healthy
      dossier-frontend:
        condition: service_healthy
    entrypoint: cypress open --project /
    environment:
      - DISPLAY
    volumes:
      # for Cypress to communicate with the X11 server pass this socket file
      # in addition to any other mapped volumes
      - ./cypress/:/cypress
      - ./cypress/cypress.json:/cypress.json
      #- ./cypress/cypress/cypress.json:/cypress/cypress.json
      - /tmp/.X11-unix:/tmp/.X11-unix:rw

volumes:
  test_result:

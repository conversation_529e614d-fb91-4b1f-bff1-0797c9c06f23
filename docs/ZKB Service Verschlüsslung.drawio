<mxfile host="Electron" modified="2023-09-08T14:14:31.100Z" agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/21.6.8 Chrome/114.0.5735.289 Electron/25.5.0 Safari/537.36" etag="Wt8mG19asr2ABLX-IEBF" version="21.6.8" type="device">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
    <mxGraphModel dx="1766" dy="1066" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-4" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=open;endFill=0;dashed=1;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-0" target="FtFLuUjVtAH8YpAqQOGy-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-13" value="local" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FtFLuUjVtAH8YpAqQOGy-4">
          <mxGeometry x="0.04" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-0" value="Database" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="600" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-9" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=none;endFill=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-1" target="FtFLuUjVtAH8YpAqQOGy-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-1" value="Exoscale S3" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="827" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-5" value="TLS" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;endArrow=open;endFill=0;dashed=1;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-2" target="FtFLuUjVtAH8YpAqQOGy-0">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-6" value="TLS + SSE-C" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=open;endFill=0;dashed=1;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-2" target="FtFLuUjVtAH8YpAqQOGy-8">
          <mxGeometry x="0.1988" y="12" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-2" value="HypoDossier Manager" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="747" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-3" value="Filesystem Encryption&lt;br&gt;(Longhorn)" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="593.5" y="520" width="133" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-8" value="S3 API" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="867" y="420" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-10" value="ZKB API" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="500" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-11" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=1;entryDx=0;entryDy=0;endArrow=none;endFill=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-2" target="FtFLuUjVtAH8YpAqQOGy-10">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="720" y="330" as="sourcePoint" />
            <mxPoint x="719.5" y="270" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-14" value="ZKB API Gateway" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="270" y="110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-15" value="mTLS + IP Restriction" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=open;endFill=0;dashed=1;entryX=0;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-14" target="FtFLuUjVtAH8YpAqQOGy-10">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="737" y="400" as="sourcePoint" />
            <mxPoint x="642" y="480" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-17" value="ZKB User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="290" y="380" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-18" value="Browser mit TLS" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=open;endFill=0;dashed=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-17" target="FtFLuUjVtAH8YpAqQOGy-2">
          <mxGeometry x="-0.07" y="-6" relative="1" as="geometry">
            <mxPoint x="400" y="165" as="sourcePoint" />
            <mxPoint x="716" y="246" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-22" value="ZKB" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="145" y="90" width="320" height="610" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-23" value="Authorisierungs Service" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="490" y="120" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-24" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;endFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-14" target="FtFLuUjVtAH8YpAqQOGy-23">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="700" y="303" as="sourcePoint" />
            <mxPoint x="614" y="264" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-25" value="Hypodossier" style="shape=umlFrame;whiteSpace=wrap;html=1;pointerEvents=0;width=95;height=30;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="575" y="90" width="405" height="610" as="geometry" />
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-26" value="mTLS + IP Restriction" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=open;endFill=0;dashed=1;entryX=1;entryY=1;entryDx=0;entryDy=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="WIyWlLk6GJQsqaUBKTNV-1" source="FtFLuUjVtAH8YpAqQOGy-2" target="FtFLuUjVtAH8YpAqQOGy-23">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="179" as="sourcePoint" />
            <mxPoint x="536" y="246" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FtFLuUjVtAH8YpAqQOGy-30" value="&lt;meta charset=&quot;utf-8&quot;&gt;&lt;pre style=&quot;tab-size: 4;&quot;&gt;&lt;code data-lang=&quot;yaml&quot; class=&quot;language-yaml&quot;&gt;  &lt;span style=&quot;&quot;&gt;CRYPTO_KEY_CIPHER&lt;/span&gt;: &lt;span style=&quot;&quot;&gt;&quot;aes-xts-plain64&quot;&lt;/span&gt;&#xa;  &lt;span style=&quot;&quot;&gt;CRYPTO_KEY_HASH&lt;/span&gt;: &lt;span style=&quot;&quot;&gt;&quot;sha256&quot;&lt;/span&gt;&#xa;  &lt;span style=&quot;&quot;&gt;CRYPTO_KEY_SIZE&lt;/span&gt;: &lt;span style=&quot;&quot;&gt;&quot;256&quot;&lt;/span&gt;&#xa;  &lt;span style=&quot;&quot;&gt;CRYPTO_PBKDF&lt;/span&gt;: &lt;span style=&quot;&quot;&gt;&quot;argon2i&quot;&lt;/span&gt;&lt;/code&gt;&lt;/pre&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#000000;strokeColor=#666666;labelBackgroundColor=default;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
          <mxGeometry x="593.5" y="600" width="280" height="70" as="geometry" />
        </mxCell>
        <UserObject label="https://community.exoscale.com/documentation/storage/encryption/" link="https://community.exoscale.com/documentation/storage/encryption/" id="FtFLuUjVtAH8YpAqQOGy-31">
          <mxCell style="text;whiteSpace=wrap;" vertex="1" parent="WIyWlLk6GJQsqaUBKTNV-1">
            <mxGeometry x="827" y="540" width="173" height="50" as="geometry" />
          </mxCell>
        </UserObject>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

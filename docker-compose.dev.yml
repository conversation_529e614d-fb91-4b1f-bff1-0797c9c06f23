services:
  caddy:
    extra_hosts:
      - "sample-react:host-gateway" # this requires # sudo ufw allow proto tcp from  **********/12 to any port 3000

  dms:
    build:
      context: dossier-backend
      dockerfile: Dockerfile.dev
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - ./dossier-backend:/app
    ports:
      - 8000:8000

  dossier-frontend:
    env_file:
      - ./dossier-manager-frontend/.env-example.dev
    build:
      context: dossier-manager-frontend
      dockerfile: Dockerfile.dev
      args:
        NPM_TOKEN: ${NPM_TOKEN}
    restart: always
    volumes:
      - ./dossier-manager-frontend/src:/app/src
    ports:
      - 3000:3000